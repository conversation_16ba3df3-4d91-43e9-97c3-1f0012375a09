<?php
/**
 * BizManage Pro Documents Class
 *
 * Handles document management, upload, encryption, and storage
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Documents Class
 */
class BizManage_Pro_Documents {

    /**
     * Instance of this class
     * @var BizManage_Pro_Documents
     */
    private static $instance = null;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Security instance
     * @var BizManage_Pro_Security
     */
    private $security;

    /**
     * Utilities instance
     * @var BizManage_Pro_Utilities
     */
    private $utilities;

    /**
     * Upload directory
     * @var string
     */
    private $upload_dir;

    /**
     * Get instance
     * @return BizManage_Pro_Documents
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->db = BizManage_Pro_Database::instance();
        $this->security = BizManage_Pro_Security::instance();
        $this->utilities = BizManage_Pro_Utilities::instance();
        $this->init_upload_dir();
        $this->init_hooks();
    }

    /**
     * Initialize upload directory
     */
    private function init_upload_dir() {
        $wp_upload_dir = wp_upload_dir();
        $this->upload_dir = $wp_upload_dir['basedir'] . '/bizmanage-pro/documents';
        
        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
            $this->secure_upload_directory();
        }
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_ajax_bizmanage_download_document', array($this, 'download_document'));
        add_action('wp_ajax_bizmanage_preview_document', array($this, 'preview_document'));
    }

    /**
     * Secure upload directory
     */
    private function secure_upload_directory() {
        // Create .htaccess file
        $htaccess_content = "deny from all\n";
        file_put_contents($this->upload_dir . '/.htaccess', $htaccess_content);
        
        // Create index.php file
        $index_content = "<?php\n// Silence is golden.\n";
        file_put_contents($this->upload_dir . '/index.php', $index_content);
    }

    /**
     * Upload document
     * @param array $file_data
     * @param array $document_data
     * @return int|false
     */
    public function upload_document($file_data, $document_data) {
        // Validate file
        $validation_errors = $this->security->validate_file_upload($file_data);
        if (!empty($validation_errors)) {
            return false;
        }

        // Validate document data
        $document_errors = $this->validate_document_data($document_data);
        if (!empty($document_errors)) {
            return false;
        }

        // Generate unique filename
        $original_filename = sanitize_file_name($file_data['name']);
        $file_extension = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));
        $unique_filename = $this->generate_unique_filename($original_filename);
        
        $file_path = $this->upload_dir . '/' . $unique_filename;

        // Move uploaded file
        if (!move_uploaded_file($file_data['tmp_name'], $file_path)) {
            return false;
        }

        // Encrypt file if enabled
        $encrypted = false;
        $encryption_key = null;
        
        if (get_option('bizmanage_pro_encryption_enabled', '1') === '1') {
            $encrypted_result = $this->encrypt_file($file_path);
            if ($encrypted_result) {
                $encrypted = true;
                $encryption_key = $encrypted_result;
            }
        }

        // Prepare document data for database
        $db_data = array(
            'entity_id' => intval($document_data['entity_id']),
            'user_id' => get_current_user_id(),
            'title' => $this->security->sanitize_input($document_data['title'], 'text'),
            'description' => $this->security->sanitize_input($document_data['description'] ?? '', 'textarea'),
            'category' => $this->security->sanitize_input($document_data['category'], 'text'),
            'file_name' => $original_filename,
            'file_path' => $file_path,
            'file_size' => $file_data['size'],
            'file_type' => $file_extension,
            'mime_type' => $file_data['type'],
            'encrypted' => $encrypted ? 1 : 0,
            'encryption_key' => $encryption_key,
            'version' => '1.0',
            'tags' => $this->security->sanitize_input($document_data['tags'] ?? '', 'text'),
            'status' => 'active',
        );

        // Insert document record
        $document_id = $this->db->insert('documents', $db_data);

        if ($document_id) {
            do_action('bizmanage_document_uploaded', $document_id, $db_data);
            return $document_id;
        } else {
            // Remove uploaded file if database insert failed
            unlink($file_path);
            return false;
        }
    }

    /**
     * Update document
     * @param int $document_id
     * @param array $data
     * @return bool
     */
    public function update_document($document_id, $data) {
        // Check permissions
        if (!$this->user_can_edit_document($document_id)) {
            return false;
        }

        // Validate data
        $validation_errors = $this->validate_document_data($data, $document_id);
        if (!empty($validation_errors)) {
            return false;
        }

        // Sanitize data
        $sanitized_data = array(
            'title' => $this->security->sanitize_input($data['title'], 'text'),
            'description' => $this->security->sanitize_input($data['description'] ?? '', 'textarea'),
            'category' => $this->security->sanitize_input($data['category'], 'text'),
            'tags' => $this->security->sanitize_input($data['tags'] ?? '', 'text'),
            'modified_date' => current_time('mysql'),
        );

        $result = $this->db->update('documents', $sanitized_data, array('id' => $document_id));

        if ($result !== false) {
            do_action('bizmanage_document_updated', $document_id, $sanitized_data);
        }

        return $result !== false;
    }

    /**
     * Delete document
     * @param int $document_id
     * @return bool
     */
    public function delete_document($document_id) {
        // Check permissions
        if (!$this->user_can_edit_document($document_id)) {
            return false;
        }

        // Get document data
        $document = $this->get_document($document_id);
        if (!$document) {
            return false;
        }

        // Soft delete - update status
        $result = $this->db->update('documents', 
            array('status' => 'deleted', 'modified_date' => current_time('mysql')), 
            array('id' => $document_id)
        );

        if ($result !== false) {
            // Optionally delete physical file
            if (get_option('bizmanage_pro_delete_files_on_delete', '0') === '1') {
                if (file_exists($document->file_path)) {
                    unlink($document->file_path);
                }
            }
            
            do_action('bizmanage_document_deleted', $document_id);
        }

        return $result !== false;
    }

    /**
     * Get document by ID
     * @param int $document_id
     * @return object|null
     */
    public function get_document($document_id) {
        return $this->db->get_row('documents', array('id' => $document_id));
    }

    /**
     * Get documents
     * @param array $args
     * @return array
     */
    public function get_documents($args = array()) {
        $defaults = array(
            'entity_id' => 0,
            'category' => '',
            'search' => '',
            'status' => 'active',
            'order_by' => 'upload_date',
            'order' => 'DESC',
            'limit' => '',
            'offset' => '',
        );

        $args = wp_parse_args($args, $defaults);

        $where_conditions = array('status' => $args['status']);

        if ($args['entity_id'] > 0) {
            $where_conditions['entity_id'] = $args['entity_id'];
        }

        if (!empty($args['category'])) {
            $where_conditions['category'] = $args['category'];
        }

        $query_args = array(
            'where' => $where_conditions,
            'order_by' => $args['order_by'],
            'order' => $args['order']
        );

        if (!empty($args['limit'])) {
            $query_args['limit'] = $args['limit'];
        }

        if (!empty($args['offset'])) {
            $query_args['offset'] = $args['offset'];
        }

        $documents = $this->db->get_results('documents', $query_args);

        // Filter by search term if provided
        if (!empty($args['search']) && !empty($documents)) {
            $search_term = strtolower($args['search']);
            $documents = array_filter($documents, function($doc) use ($search_term) {
                return strpos(strtolower($doc->title), $search_term) !== false ||
                       strpos(strtolower($doc->description), $search_term) !== false ||
                       strpos(strtolower($doc->tags), $search_term) !== false;
            });
        }

        return $documents;
    }

    /**
     * Create new version of document
     * @param int $parent_document_id
     * @param array $file_data
     * @param array $document_data
     * @return int|false
     */
    public function create_document_version($parent_document_id, $file_data, $document_data) {
        $parent_document = $this->get_document($parent_document_id);
        if (!$parent_document) {
            return false;
        }

        // Check permissions
        if (!$this->user_can_edit_document($parent_document_id)) {
            return false;
        }

        // Get next version number
        $version_number = $this->get_next_version_number($parent_document_id);

        // Set parent ID and version
        $document_data['parent_id'] = $parent_document_id;
        $document_data['version'] = $version_number;
        $document_data['entity_id'] = $parent_document->entity_id;

        // Upload new version
        $document_id = $this->upload_document($file_data, $document_data);

        if ($document_id) {
            do_action('bizmanage_document_version_created', $document_id, $parent_document_id);
        }

        return $document_id;
    }

    /**
     * Get document versions
     * @param int $document_id
     * @return array
     */
    public function get_document_versions($document_id) {
        return $this->db->get_results('documents', array(
            'where' => array(
                'parent_id' => $document_id,
                'status' => 'active'
            ),
            'order_by' => 'version',
            'order' => 'DESC'
        ));
    }

    /**
     * Download document
     */
    public function download_document() {
        // Verify nonce and permissions
        if (!$this->security->verify_nonce($_GET['nonce'] ?? '', 'bizmanage_download_document')) {
            wp_die(__('Security check failed.', 'bizmanage-pro'));
        }

        $document_id = intval($_GET['document_id'] ?? 0);
        
        if (!$this->user_can_view_document($document_id)) {
            wp_die(__('You do not have permission to download this document.', 'bizmanage-pro'));
        }

        $document = $this->get_document($document_id);
        if (!$document || !file_exists($document->file_path)) {
            wp_die(__('Document not found.', 'bizmanage-pro'));
        }

        // Decrypt file if encrypted
        $file_content = file_get_contents($document->file_path);
        if ($document->encrypted) {
            $file_content = $this->security->decrypt_data($file_content);
            if ($file_content === false) {
                wp_die(__('Failed to decrypt document.', 'bizmanage-pro'));
            }
        }

        // Set headers for download
        header('Content-Type: ' . $document->mime_type);
        header('Content-Disposition: attachment; filename="' . $document->file_name . '"');
        header('Content-Length: ' . strlen($file_content));
        header('Cache-Control: private');
        header('Pragma: private');

        echo $file_content;
        exit;
    }

    /**
     * Preview document
     */
    public function preview_document() {
        // Verify nonce and permissions
        if (!$this->security->verify_nonce($_GET['nonce'] ?? '', 'bizmanage_preview_document')) {
            wp_die(__('Security check failed.', 'bizmanage-pro'));
        }

        $document_id = intval($_GET['document_id'] ?? 0);
        
        if (!$this->user_can_view_document($document_id)) {
            wp_die(__('You do not have permission to preview this document.', 'bizmanage-pro'));
        }

        $document = $this->get_document($document_id);
        if (!$document || !file_exists($document->file_path)) {
            wp_die(__('Document not found.', 'bizmanage-pro'));
        }

        // Only allow preview for certain file types
        $previewable_types = array('pdf', 'jpg', 'jpeg', 'png', 'gif');
        if (!in_array($document->file_type, $previewable_types)) {
            wp_die(__('This document type cannot be previewed.', 'bizmanage-pro'));
        }

        // Decrypt file if encrypted
        $file_content = file_get_contents($document->file_path);
        if ($document->encrypted) {
            $file_content = $this->security->decrypt_data($file_content);
            if ($file_content === false) {
                wp_die(__('Failed to decrypt document.', 'bizmanage-pro'));
            }
        }

        // Set headers for preview
        header('Content-Type: ' . $document->mime_type);
        header('Content-Length: ' . strlen($file_content));
        header('Cache-Control: private');
        header('Pragma: private');

        echo $file_content;
        exit;
    }

    /**
     * Validate document data
     * @param array $data
     * @param int $document_id
     * @return array
     */
    private function validate_document_data($data, $document_id = 0) {
        $errors = array();

        // Required fields
        if (empty($data['title'])) {
            $errors[] = __('Document title is required.', 'bizmanage-pro');
        }

        if (empty($data['category'])) {
            $errors[] = __('Document category is required.', 'bizmanage-pro');
        }

        if (empty($data['entity_id']) || !is_numeric($data['entity_id'])) {
            $errors[] = __('Valid entity ID is required.', 'bizmanage-pro');
        }

        return $errors;
    }

    /**
     * Generate unique filename
     * @param string $filename
     * @return string
     */
    private function generate_unique_filename($filename) {
        return $this->utilities->generate_unique_filename($filename, $this->upload_dir);
    }

    /**
     * Encrypt file
     * @param string $file_path
     * @return string|false
     */
    private function encrypt_file($file_path) {
        $file_content = file_get_contents($file_path);
        $encrypted_content = $this->security->encrypt_data($file_content);
        
        if ($encrypted_content) {
            file_put_contents($file_path, $encrypted_content);
            return get_option('bizmanage_pro_encryption_key');
        }
        
        return false;
    }

    /**
     * Get next version number
     * @param int $parent_document_id
     * @return string
     */
    private function get_next_version_number($parent_document_id) {
        $versions = $this->get_document_versions($parent_document_id);
        
        if (empty($versions)) {
            return '1.1';
        }

        $latest_version = $versions[0]->version;
        $version_parts = explode('.', $latest_version);
        $minor_version = intval($version_parts[1] ?? 0) + 1;
        
        return $version_parts[0] . '.' . $minor_version;
    }

    /**
     * Check if user can view document
     * @param int $document_id
     * @return bool
     */
    private function user_can_view_document($document_id) {
        if (!current_user_can('bizmanage_view_documents')) {
            return false;
        }

        $document = $this->get_document($document_id);
        if (!$document) {
            return false;
        }

        // Check entity access
        $roles = BizManage_Pro_Roles::instance();
        return $roles->user_can_access_entity(get_current_user_id(), $document->entity_id);
    }

    /**
     * Check if user can edit document
     * @param int $document_id
     * @return bool
     */
    private function user_can_edit_document($document_id) {
        if (!current_user_can('bizmanage_manage_documents')) {
            return false;
        }

        return $this->user_can_view_document($document_id);
    }

    /**
     * Get document categories
     * @return array
     */
    public function get_document_categories() {
        return $this->utilities->get_document_categories();
    }

    /**
     * Search documents
     * @param string $search_term
     * @param array $filters
     * @return array
     */
    public function search_documents($search_term, $filters = array()) {
        $args = array_merge($filters, array('search' => $search_term));
        return $this->get_documents($args);
    }
}

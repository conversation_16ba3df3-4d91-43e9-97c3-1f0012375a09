<?php
/**
 * BizManage Pro Database Class
 *
 * Handles database operations and queries
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Database Class
 */
class BizManage_Pro_Database {

    /**
     * Instance of this class
     * @var BizManage_Pro_Database
     */
    private static $instance = null;

    /**
     * WordPress database object
     * @var wpdb
     */
    private $wpdb;

    /**
     * Table names
     * @var array
     */
    private $tables;

    /**
     * Get instance
     * @return BizManage_Pro_Database
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->init_tables();
    }

    /**
     * Initialize table names
     */
    private function init_tables() {
        $this->tables = array(
            'business_entities' => $this->wpdb->prefix . 'bizmanage_business_entities',
            'documents' => $this->wpdb->prefix . 'bizmanage_documents',
            'transactions' => $this->wpdb->prefix . 'bizmanage_transactions',
            'recurring_transactions' => $this->wpdb->prefix . 'bizmanage_recurring_transactions',
            'bank_accounts' => $this->wpdb->prefix . 'bizmanage_bank_accounts',
            'categories' => $this->wpdb->prefix . 'bizmanage_categories',
            'settings' => $this->wpdb->prefix . 'bizmanage_settings',
        );
    }

    /**
     * Get table name
     * @param string $table
     * @return string
     */
    public function get_table($table) {
        return isset($this->tables[$table]) ? $this->tables[$table] : '';
    }

    /**
     * Insert data into table
     * @param string $table
     * @param array $data
     * @param array $format
     * @return int|false
     */
    public function insert($table, $data, $format = null) {
        $table_name = $this->get_table($table);
        if (empty($table_name)) {
            return false;
        }

        $result = $this->wpdb->insert($table_name, $data, $format);
        return $result ? $this->wpdb->insert_id : false;
    }

    /**
     * Update data in table
     * @param string $table
     * @param array $data
     * @param array $where
     * @param array $format
     * @param array $where_format
     * @return int|false
     */
    public function update($table, $data, $where, $format = null, $where_format = null) {
        $table_name = $this->get_table($table);
        if (empty($table_name)) {
            return false;
        }

        return $this->wpdb->update($table_name, $data, $where, $format, $where_format);
    }

    /**
     * Delete data from table
     * @param string $table
     * @param array $where
     * @param array $where_format
     * @return int|false
     */
    public function delete($table, $where, $where_format = null) {
        $table_name = $this->get_table($table);
        if (empty($table_name)) {
            return false;
        }

        return $this->wpdb->delete($table_name, $where, $where_format);
    }

    /**
     * Get single row from table
     * @param string $table
     * @param array $where
     * @param string $output
     * @return object|array|null
     */
    public function get_row($table, $args = array(), $output = OBJECT) {
        $table_name = $this->get_table($table);
        if (empty($table_name)) {
            return null;
        }

        $sql = "SELECT * FROM $table_name";
        $where_values = array();

        // Handle different parameter formats
        if (isset($args['where']) && is_array($args['where'])) {
            $where = $args['where'];
        } elseif (is_array($args) && !isset($args['where'])) {
            $where = $args; // Direct where array
        } else {
            $where = array();
        }

        if (!empty($where)) {
            $conditions = array();
            foreach ($where as $column => $value) {
                if (is_numeric($value)) {
                    $conditions[] = $this->wpdb->prepare("$column = %d", $value);
                } else {
                    $conditions[] = $this->wpdb->prepare("$column = %s", $value);
                }
            }
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }

        return $this->wpdb->get_row($sql, $output);
    }

    /**
     * Get multiple rows from table
     * @param string $table
     * @param array $args
     * @return array
     */
    public function get_results($table, $args = array()) {
        $table_name = $this->get_table($table);
        if (empty($table_name)) {
            return array();
        }

        $defaults = array(
            'select' => '*',
            'where' => array(),
            'order_by' => '',
            'order' => 'ASC',
            'limit' => '',
            'offset' => '',
        );

        $args = wp_parse_args($args, $defaults);

        $sql = "SELECT {$args['select']} FROM $table_name";

        // WHERE clause
        if (!empty($args['where'])) {
            $conditions = array();
            foreach ($args['where'] as $column => $value) {
                if (is_array($value)) {
                    $placeholders = implode(',', array_fill(0, count($value), '%s'));
                    $conditions[] = $this->wpdb->prepare("$column IN ($placeholders)", $value);
                } else {
                    $conditions[] = $this->wpdb->prepare("$column = %s", $value);
                }
            }
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }

        // ORDER BY clause
        if (!empty($args['order_by'])) {
            $sql .= " ORDER BY {$args['order_by']} {$args['order']}";
        }

        // LIMIT clause
        if (!empty($args['limit'])) {
            $sql .= " LIMIT {$args['limit']}";
            
            if (!empty($args['offset'])) {
                $sql .= " OFFSET {$args['offset']}";
            }
        }

        return $this->wpdb->get_results($sql);
    }

    /**
     * Get count of rows
     * @param string $table
     * @param array $where
     * @return int
     */
    public function get_count($table, $where = array()) {
        $table_name = $this->get_table($table);
        if (empty($table_name)) {
            return 0;
        }

        $sql = "SELECT COUNT(*) FROM $table_name";

        if (!empty($where)) {
            $conditions = array();
            foreach ($where as $column => $value) {
                $conditions[] = $this->wpdb->prepare("$column = %s", $value);
            }
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }

        return (int) $this->wpdb->get_var($sql);
    }

    /**
     * Execute custom query
     * @param string $sql
     * @return mixed
     */
    public function query($sql) {
        return $this->wpdb->query($sql);
    }

    /**
     * Get results from custom query
     * @param string $sql
     * @param string $output
     * @return array
     */
    public function get_query_results($sql, $output = OBJECT) {
        return $this->wpdb->get_results($sql, $output);
    }

    /**
     * Get single value from custom query
     * @param string $sql
     * @return string|null
     */
    public function get_var($sql) {
        // Add query caching for performance
        $cache_key = 'bizmanage_query_' . md5($sql);
        $result = wp_cache_get($cache_key, 'bizmanage_pro');

        if ($result === false) {
            $result = $this->wpdb->get_var($sql);
            wp_cache_set($cache_key, $result, 'bizmanage_pro', 300); // Cache for 5 minutes
        }

        return $result;
    }

    /**
     * Prepare SQL statement
     * @param string $query
     * @param mixed ...$args
     * @return string
     */
    public function prepare($query, ...$args) {
        return $this->wpdb->prepare($query, ...$args);
    }

    /**
     * Get last insert ID
     * @return int
     */
    public function get_insert_id() {
        return $this->wpdb->insert_id;
    }

    /**
     * Get last error
     * @return string
     */
    public function get_last_error() {
        return $this->wpdb->last_error;
    }

    /**
     * Start transaction
     */
    public function start_transaction() {
        $this->wpdb->query('START TRANSACTION');
    }

    /**
     * Commit transaction
     */
    public function commit() {
        $this->wpdb->query('COMMIT');
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        $this->wpdb->query('ROLLBACK');
    }

    /**
     * Check if table exists
     * @param string $table
     * @return bool
     */
    public function table_exists($table) {
        $table_name = $this->get_table($table);
        if (empty($table_name)) {
            return false;
        }

        $result = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SHOW TABLES LIKE %s",
                $table_name
            )
        );

        return $result === $table_name;
    }

    /**
     * Get database version
     * @return string
     */
    public function get_db_version() {
        return get_option('bizmanage_pro_db_version', '0.0.0');
    }

    /**
     * Update database version
     * @param string $version
     */
    public function update_db_version($version) {
        update_option('bizmanage_pro_db_version', $version);
    }

    /**
     * Clear query cache
     * @param string $pattern Optional pattern to match cache keys
     */
    public function clear_cache($pattern = null) {
        if ($pattern) {
            // Clear specific cache pattern
            wp_cache_flush_group('bizmanage_pro');
        } else {
            // Clear all BizManage Pro cache
            wp_cache_flush_group('bizmanage_pro');
        }
    }

    /**
     * Get database statistics
     * @return array
     */
    public function get_stats() {
        $stats = array();

        foreach ($this->tables as $table_key => $table_name) {
            $count = $this->wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
            $stats[$table_key] = array(
                'name' => $table_name,
                'count' => intval($count)
            );
        }

        return $stats;
    }

    /**
     * Optimize database tables
     * @return bool
     */
    public function optimize_tables() {
        try {
            foreach ($this->tables as $table_name) {
                $this->wpdb->query("OPTIMIZE TABLE {$table_name}");
            }
            return true;
        } catch (Exception $e) {
            error_log('BizManagePro: Database optimization failed: ' . $e->getMessage());
            return false;
        }
    }
}

/**
 * BizManage Pro Admin JavaScript
 *
 * @package BizManagePro
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Global BizManage object
    window.BizManagePro = {
        init: function() {
            this.bindEvents();
            this.initComponents();
        },

        bindEvents: function() {
            // Entity management
            $(document).on('click', '.bizmanage-save-entity', this.saveEntity);
            $(document).on('click', '.bizmanage-delete-entity', this.deleteEntity);
            $(document).on('change', '#bizmanage-entity-select', this.onEntityChange);

            // Document management
            $(document).on('click', '.bizmanage-upload-document', this.uploadDocument);
            $(document).on('click', '.bizmanage-delete-document', this.deleteDocument);

            // Transaction management
            $(document).on('click', '.bizmanage-save-transaction', this.saveTransaction);
            $(document).on('click', '.bizmanage-delete-transaction', this.deleteTransaction);

            // Form validation
            $(document).on('submit', '.bizmanage-form', this.validateForm);

            // Modal events
            $(document).on('click', '.bizmanage-modal-close', this.closeModal);
            $(document).on('click', '.bizmanage-modal', function(e) {
                if (e.target === this) {
                    BizManagePro.closeModal();
                }
            });

            // Dashboard refresh
            $(document).on('click', '.bizmanage-refresh-dashboard', this.refreshDashboard);
        },

        initComponents: function() {
            // Initialize date pickers
            if ($.fn.datepicker) {
                $('.bizmanage-datepicker').datepicker({
                    dateFormat: 'yy-mm-dd',
                    changeMonth: true,
                    changeYear: true
                });
            }

            // Initialize select2 if available
            if ($.fn.select2) {
                $('.bizmanage-select2').select2({
                    width: '100%'
                });
            }

            // Load dashboard data on page load
            if ($('#bizmanage-dashboard').length) {
                this.loadDashboardData();
            }

            // Initialize charts
            this.initCharts();
        },

        // AJAX Helper
        ajaxRequest: function(action, data, callback) {
            // Get the appropriate nonce for this action
            var nonceKey = action.replace('bizmanage_', '');
            var nonce = bizmanageAjax.nonces && bizmanageAjax.nonces[nonceKey] ?
                       bizmanageAjax.nonces[nonceKey] :
                       (bizmanageAjax.nonce || '');

            var requestData = {
                action: action,
                nonce: nonce
            };

            $.extend(requestData, data);

            $.ajax({
                url: bizmanageAjax.ajaxurl,
                type: 'POST',
                data: requestData,
                dataType: 'json',
                timeout: 30000, // 30 second timeout
                beforeSend: function() {
                    BizManagePro.showLoading();
                },
                success: function(response) {
                    BizManagePro.hideLoading();

                    // Validate response structure
                    if (typeof response !== 'object') {
                        console.error('BizManagePro: Invalid response format', response);
                        BizManagePro.showAlert('Invalid response from server.', 'error');
                        return;
                    }

                    if (callback) {
                        callback(response);
                    }
                },
                error: function(xhr, status, error) {
                    BizManagePro.hideLoading();

                    console.error('BizManagePro AJAX Error:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        action: action,
                        requestData: requestData
                    });

                    var errorMessage = bizmanageAjax.strings.error_occurred;

                    // Provide more specific error messages
                    if (status === 'timeout') {
                        errorMessage = 'Request timed out. Please try again.';
                    } else if (status === 'parsererror') {
                        errorMessage = 'Server response error. Please try again.';
                    } else if (xhr.status === 403) {
                        errorMessage = 'Access denied. Please check your permissions.';
                    } else if (xhr.status === 500) {
                        errorMessage = 'Server error. Please try again later.';
                    } else if (xhr.status === 0) {
                        errorMessage = 'Network error. Please check your connection.';
                    }

                    BizManagePro.showAlert(errorMessage, 'error');
                }
            });
        },

        // Entity Management
        saveEntity: function(e) {
            e.preventDefault();
            var form = $(this).closest('form');
            var formDataArr = form.serializeArray();
            // Convert to object for ajaxRequest
            var formData = {};
            formDataArr.forEach(function(item) { formData[item.name] = item.value; });

            BizManagePro.ajaxRequest('bizmanage_save_entity', formData, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    if (response.data && response.data.entity_id) {
                        form.find('input[name="entity_id"]').val(response.data.entity_id);
                    }
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        deleteEntity: function(e) {
            e.preventDefault();
            
            if (!confirm(bizmanageAjax.strings.confirm_delete)) {
                return;
            }

            var entityId = $(this).data('entity-id');

            BizManagePro.ajaxRequest('bizmanage_delete_entity', {
                entity_id: entityId
            }, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    $(e.target).closest('tr').fadeOut();
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        onEntityChange: function() {
            var entityId = $(this).val();
            
            // Trigger entity change event
            $(document).trigger('bizmanage:entity-changed', [entityId]);
            
            // Refresh dashboard if present
            if ($('#bizmanage-dashboard').length) {
                BizManagePro.loadDashboardData();
            }
        },

        // Document Management
        uploadDocument: function(e) {
            e.preventDefault();

            var form = $(this).closest('form')[0];

            // Validate form
            var title = $(form).find('input[name="title"]').val();
            var fileInput = $(form).find('input[name="document"]')[0];

            if (!title.trim()) {
                BizManagePro.showAlert('Please enter a document title.', 'error');
                return;
            }

            if (!fileInput.files || fileInput.files.length === 0) {
                BizManagePro.showAlert('Please select a file to upload.', 'error');
                return;
            }

            var formData = new FormData(form);
            formData.append('action', 'bizmanage_upload_document');

            // Get the nonce from the form field or use AJAX nonce
            var nonceField = $(form).find('input[name="document_nonce"]');
            if (nonceField.length) {
                formData.append('nonce', nonceField.val());
            } else {
                var nonce = bizmanageAjax.nonces && bizmanageAjax.nonces.upload_document ?
                           bizmanageAjax.nonces.upload_document :
                           (bizmanageAjax.nonce || '');
                formData.append('nonce', nonce);
            }

            $.ajax({
                url: bizmanageAjax.ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    BizManagePro.showLoading();
                },
                success: function(response) {
                    BizManagePro.hideLoading();
                    if (response.success) {
                        BizManagePro.showAlert(response.message, 'success');
                        $(form)[0].reset();
                        // Refresh document list if present
                        if ($('#bizmanage-documents-list').length) {
                            BizManagePro.refreshDocumentsList();
                        }
                    } else {
                        BizManagePro.showAlert(response.message, 'error');
                    }
                },
                error: function() {
                    BizManagePro.hideLoading();
                    BizManagePro.showAlert(bizmanageAjax.strings.error_occurred, 'error');
                }
            });
        },

        deleteDocument: function(e) {
            e.preventDefault();
            
            if (!confirm(bizmanageAjax.strings.confirm_delete)) {
                return;
            }

            var documentId = $(this).data('document-id');

            BizManagePro.ajaxRequest('bizmanage_delete_document', {
                document_id: documentId
            }, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    $(e.target).closest('tr').fadeOut();
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        // Transaction Management
        saveTransaction: function(e) {
            e.preventDefault();
            
            var form = $(this).closest('form');
            var formDataArr = form.serializeArray();
            // Convert to object for ajaxRequest
            var formData = {};
            formDataArr.forEach(function(item) { formData[item.name] = item.value; });

            BizManagePro.ajaxRequest('bizmanage_save_transaction', formData, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    if (response.data && response.data.transaction_id) {
                        form.find('input[name="transaction_id"]').val(response.data.transaction_id);
                    }
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        deleteTransaction: function(e) {
            e.preventDefault();
            
            if (!confirm(bizmanageAjax.strings.confirm_delete)) {
                return;
            }

            var transactionId = $(this).data('transaction-id');

            BizManagePro.ajaxRequest('bizmanage_delete_transaction', {
                transaction_id: transactionId
            }, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    $(e.target).closest('tr').fadeOut();
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        // Dashboard
        loadDashboardData: function() {
            var entityId = $('#bizmanage-entity-select').val() || 0;
            var period = $('#dashboard-period-select').val() || 'month';

            if (!entityId) {
                console.log('No entity selected for dashboard');
                return;
            }

            // Show loading state
            this.showDashboardLoading();

            BizManagePro.ajaxRequest('bizmanage_get_dashboard_data', {
                entity_id: entityId,
                period: period
            }, function(response) {
                BizManagePro.hideDashboardLoading();

                if (response.success && response.data) {
                    BizManagePro.updateDashboard(response.data);
                    BizManagePro.updateRecentTransactions(response.data.recent_transactions || []);
                } else {
                    console.error('Dashboard data load failed:', response);
                    BizManagePro.showAlert('Failed to load dashboard data', 'error');
                }
            });
        },

        showDashboardLoading: function() {
            $('.card-value').html('<span class="loading">Loading...</span>');
        },

        hideDashboardLoading: function() {
            // Loading indicators will be replaced with actual data
        },

        updateDashboard: function(data) {
            // Update summary cards
            $('#income-total').text(BizManagePro.formatCurrency(data.income_total));
            $('#expense-total').text(BizManagePro.formatCurrency(data.expense_total));
            $('#profit-total').text(BizManagePro.formatCurrency(data.profit));

            // Update profit card color
            var profitCard = $('#profit-total').closest('.bizmanage-card');
            profitCard.removeClass('positive negative');
            profitCard.addClass(data.profit >= 0 ? 'positive' : 'negative');

            // Update charts
            this.updateCharts(data);
        },

        refreshDashboard: function(e) {
            e.preventDefault();
            BizManagePro.loadDashboardData();
        },

        // Charts
        initCharts: function() {
            // Initialize Chart.js charts
            if (typeof Chart !== 'undefined' && $('#income-expense-chart').length) {
                this.createIncomeExpenseChart();
            }
        },

        createIncomeExpenseChart: function() {
            var ctx = document.getElementById('income-expense-chart').getContext('2d');
            
            this.incomeExpenseChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Income',
                        data: [],
                        backgroundColor: 'rgba(40, 167, 69, 0.8)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1
                    }, {
                        label: 'Expenses',
                        data: [],
                        backgroundColor: 'rgba(220, 53, 69, 0.8)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },

        updateCharts: function(data) {
            // Update chart data based on dashboard data
            if (this.incomeExpenseChart && data.chart_data) {
                this.incomeExpenseChart.data.labels = data.chart_data.labels;
                this.incomeExpenseChart.data.datasets[0].data = data.chart_data.income;
                this.incomeExpenseChart.data.datasets[1].data = data.chart_data.expenses;
                this.incomeExpenseChart.update();
            }
        },

        updateRecentTransactions: function(transactions) {
            var tbody = $('.bizmanage-table tbody');
            if (!tbody.length) return;

            tbody.empty();

            if (!transactions || transactions.length === 0) {
                tbody.append('<tr><td colspan="5" style="text-align: center; padding: 20px; color: #666;">' +
                           (bizmanageAjax.strings.no_data || 'No recent transactions found.') + '</td></tr>');
                return;
            }

            transactions.forEach(function(transaction) {
                var row = '<tr>' +
                    '<td>' + transaction.transaction_date + '</td>' +
                    '<td>' + transaction.description + '</td>' +
                    '<td>' + transaction.category + '</td>' +
                    '<td><span class="transaction-type ' + transaction.transaction_type + '">' +
                    transaction.transaction_type.charAt(0).toUpperCase() + transaction.transaction_type.slice(1) + '</span></td>' +
                    '<td class="amount ' + transaction.transaction_type + '">' +
                    BizManagePro.formatCurrency(transaction.amount) + '</td>' +
                    '</tr>';
                tbody.append(row);
            });
        },

        // Form Validation
        validateForm: function(e) {
            var form = $(this);
            var isValid = true;

            // Clear previous errors
            form.find('.error').removeClass('error');
            form.find('.error-message').remove();

            // Validate required fields
            form.find('[required]').each(function() {
                var field = $(this);
                if (!field.val().trim()) {
                    field.addClass('error');
                    field.after('<div class="error-message">This field is required.</div>');
                    isValid = false;
                }
            });

            // Validate email fields
            form.find('input[type="email"]').each(function() {
                var field = $(this);
                var email = field.val().trim();
                if (email && !BizManagePro.isValidEmail(email)) {
                    field.addClass('error');
                    field.after('<div class="error-message">Please enter a valid email address.</div>');
                    isValid = false;
                }
            });

            if (!isValid) {
                e.preventDefault();
                BizManagePro.showAlert('Please correct the errors in the form.', 'error');
            }

            return isValid;
        },

        // Utility Functions
        formatCurrency: function(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        },

        isValidEmail: function(email) {
            var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },

        showAlert: function(message, type) {
            var alertClass = 'bizmanage-alert-' + (type || 'info');
            var alert = $('<div class="bizmanage-alert ' + alertClass + '">' + message + '</div>');
            
            $('.bizmanage-admin-wrap').prepend(alert);
            
            setTimeout(function() {
                alert.fadeOut(function() {
                    alert.remove();
                });
            }, 5000);
        },

        showLoading: function() {
            $('body').addClass('bizmanage-loading');
        },

        hideLoading: function() {
            $('body').removeClass('bizmanage-loading');
        },

        showModal: function(content) {
            var modal = $('<div class="bizmanage-modal"><div class="bizmanage-modal-content"><span class="bizmanage-modal-close">&times;</span>' + content + '</div></div>');
            $('body').append(modal);
            modal.show();
        },

        closeModal: function() {
            $('.bizmanage-modal').remove();
        },

        refreshDocumentsList: function() {
            // Refresh documents list
            if ($('#bizmanage-documents-list').length) {
                location.reload();
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        BizManagePro.init();
    });

})(jQuery);

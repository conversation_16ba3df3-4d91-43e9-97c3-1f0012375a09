/**
 * BizManage Pro Admin JavaScript
 *
 * @package BizManagePro
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Global BizManage object
    window.BizManagePro = {
        init: function() {
            this.bindEvents();
            this.initComponents();
        },

        bindEvents: function() {
            // Entity management
            $(document).on('click', '.bizmanage-save-entity', this.saveEntity);
            $(document).on('click', '.bizmanage-delete-entity', this.deleteEntity);
            $(document).on('change', '#bizmanage-entity-select', this.onEntityChange);

            // Document management
            $(document).on('click', '.bizmanage-upload-document', this.uploadDocument);
            $(document).on('click', '.bizmanage-delete-document', this.deleteDocument);

            // Transaction management
            $(document).on('click', '.bizmanage-save-transaction', this.saveTransaction);
            $(document).on('click', '.bizmanage-delete-transaction', this.deleteTransaction);

            // Form validation
            $(document).on('submit', '.bizmanage-form', this.validateForm);

            // Modal events
            $(document).on('click', '.bizmanage-modal-close', this.closeModal);
            $(document).on('click', '.bizmanage-modal', function(e) {
                if (e.target === this) {
                    BizManagePro.closeModal();
                }
            });

            // Dashboard refresh
            $(document).on('click', '.bizmanage-refresh-dashboard', this.refreshDashboard);
        },

        initComponents: function() {
            // Initialize date pickers
            if ($.fn.datepicker) {
                $('.bizmanage-datepicker').datepicker({
                    dateFormat: 'yy-mm-dd',
                    changeMonth: true,
                    changeYear: true
                });
            }

            // Initialize select2 if available
            if ($.fn.select2) {
                $('.bizmanage-select2').select2({
                    width: '100%'
                });
            }

            // Load dashboard data on page load
            if ($('#bizmanage-dashboard').length) {
                this.loadDashboardData();
            }

            // Initialize charts
            this.initCharts();
        },

        // AJAX Helper
        ajaxRequest: function(action, data, callback) {
            var requestData = {
                action: action,
                nonce: bizmanageAjax.nonce
            };

            $.extend(requestData, data);

            $.ajax({
                url: bizmanageAjax.ajaxurl,
                type: 'POST',
                data: requestData,
                dataType: 'json',
                beforeSend: function() {
                    BizManagePro.showLoading();
                },
                success: function(response) {
                    BizManagePro.hideLoading();
                    if (callback) {
                        callback(response);
                    }
                },
                error: function() {
                    BizManagePro.hideLoading();
                    BizManagePro.showAlert(bizmanageAjax.strings.error_occurred, 'error');
                }
            });
        },

        // Entity Management
        saveEntity: function(e) {
            e.preventDefault();
            
            var form = $(this).closest('form');
            var formData = form.serialize();

            BizManagePro.ajaxRequest('bizmanage_save_entity', formData, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    if (response.data && response.data.entity_id) {
                        form.find('input[name="entity_id"]').val(response.data.entity_id);
                    }
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        deleteEntity: function(e) {
            e.preventDefault();
            
            if (!confirm(bizmanageAjax.strings.confirm_delete)) {
                return;
            }

            var entityId = $(this).data('entity-id');

            BizManagePro.ajaxRequest('bizmanage_delete_entity', {
                entity_id: entityId
            }, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    $(e.target).closest('tr').fadeOut();
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        onEntityChange: function() {
            var entityId = $(this).val();
            
            // Trigger entity change event
            $(document).trigger('bizmanage:entity-changed', [entityId]);
            
            // Refresh dashboard if present
            if ($('#bizmanage-dashboard').length) {
                BizManagePro.loadDashboardData();
            }
        },

        // Document Management
        uploadDocument: function(e) {
            e.preventDefault();
            
            var form = $(this).closest('form')[0];
            var formData = new FormData(form);
            formData.append('action', 'bizmanage_upload_document');
            formData.append('nonce', bizmanageAjax.nonce);

            $.ajax({
                url: bizmanageAjax.ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    BizManagePro.showLoading();
                },
                success: function(response) {
                    BizManagePro.hideLoading();
                    if (response.success) {
                        BizManagePro.showAlert(response.message, 'success');
                        $(form)[0].reset();
                        // Refresh document list if present
                        if ($('#bizmanage-documents-list').length) {
                            BizManagePro.refreshDocumentsList();
                        }
                    } else {
                        BizManagePro.showAlert(response.message, 'error');
                    }
                },
                error: function() {
                    BizManagePro.hideLoading();
                    BizManagePro.showAlert(bizmanageAjax.strings.error_occurred, 'error');
                }
            });
        },

        deleteDocument: function(e) {
            e.preventDefault();
            
            if (!confirm(bizmanageAjax.strings.confirm_delete)) {
                return;
            }

            var documentId = $(this).data('document-id');

            BizManagePro.ajaxRequest('bizmanage_delete_document', {
                document_id: documentId
            }, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    $(e.target).closest('tr').fadeOut();
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        // Transaction Management
        saveTransaction: function(e) {
            e.preventDefault();
            
            var form = $(this).closest('form');
            var formData = form.serialize();

            BizManagePro.ajaxRequest('bizmanage_save_transaction', formData, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    if (response.data && response.data.transaction_id) {
                        form.find('input[name="transaction_id"]').val(response.data.transaction_id);
                    }
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        deleteTransaction: function(e) {
            e.preventDefault();
            
            if (!confirm(bizmanageAjax.strings.confirm_delete)) {
                return;
            }

            var transactionId = $(this).data('transaction-id');

            BizManagePro.ajaxRequest('bizmanage_delete_transaction', {
                transaction_id: transactionId
            }, function(response) {
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    $(e.target).closest('tr').fadeOut();
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            });
        },

        // Dashboard
        loadDashboardData: function() {
            var entityId = $('#bizmanage-entity-select').val() || 0;
            var period = $('#dashboard-period-select').val() || 'month';

            BizManagePro.ajaxRequest('bizmanage_get_dashboard_data', {
                entity_id: entityId,
                period: period
            }, function(response) {
                if (response.success) {
                    BizManagePro.updateDashboard(response.data);
                }
            });
        },

        updateDashboard: function(data) {
            // Update summary cards
            $('#income-total').text(BizManagePro.formatCurrency(data.income_total));
            $('#expense-total').text(BizManagePro.formatCurrency(data.expense_total));
            $('#profit-total').text(BizManagePro.formatCurrency(data.profit));

            // Update profit card color
            var profitCard = $('#profit-total').closest('.bizmanage-card');
            profitCard.removeClass('positive negative');
            profitCard.addClass(data.profit >= 0 ? 'positive' : 'negative');

            // Update charts
            this.updateCharts(data);
        },

        refreshDashboard: function(e) {
            e.preventDefault();
            BizManagePro.loadDashboardData();
        },

        // Charts
        initCharts: function() {
            // Initialize Chart.js charts
            if (typeof Chart !== 'undefined' && $('#income-expense-chart').length) {
                this.createIncomeExpenseChart();
            }
        },

        createIncomeExpenseChart: function() {
            var ctx = document.getElementById('income-expense-chart').getContext('2d');
            
            this.incomeExpenseChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Income',
                        data: [],
                        backgroundColor: 'rgba(40, 167, 69, 0.8)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1
                    }, {
                        label: 'Expenses',
                        data: [],
                        backgroundColor: 'rgba(220, 53, 69, 0.8)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },

        updateCharts: function(data) {
            // Update chart data based on dashboard data
            if (this.incomeExpenseChart && data.chart_data) {
                this.incomeExpenseChart.data.labels = data.chart_data.labels;
                this.incomeExpenseChart.data.datasets[0].data = data.chart_data.income;
                this.incomeExpenseChart.data.datasets[1].data = data.chart_data.expenses;
                this.incomeExpenseChart.update();
            }
        },

        // Form Validation
        validateForm: function(e) {
            var form = $(this);
            var isValid = true;

            // Clear previous errors
            form.find('.error').removeClass('error');
            form.find('.error-message').remove();

            // Validate required fields
            form.find('[required]').each(function() {
                var field = $(this);
                if (!field.val().trim()) {
                    field.addClass('error');
                    field.after('<div class="error-message">This field is required.</div>');
                    isValid = false;
                }
            });

            // Validate email fields
            form.find('input[type="email"]').each(function() {
                var field = $(this);
                var email = field.val().trim();
                if (email && !BizManagePro.isValidEmail(email)) {
                    field.addClass('error');
                    field.after('<div class="error-message">Please enter a valid email address.</div>');
                    isValid = false;
                }
            });

            if (!isValid) {
                e.preventDefault();
                BizManagePro.showAlert('Please correct the errors in the form.', 'error');
            }

            return isValid;
        },

        // Utility Functions
        formatCurrency: function(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        },

        isValidEmail: function(email) {
            var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },

        showAlert: function(message, type) {
            var alertClass = 'bizmanage-alert-' + (type || 'info');
            var alert = $('<div class="bizmanage-alert ' + alertClass + '">' + message + '</div>');
            
            $('.bizmanage-admin-wrap').prepend(alert);
            
            setTimeout(function() {
                alert.fadeOut(function() {
                    alert.remove();
                });
            }, 5000);
        },

        showLoading: function() {
            $('body').addClass('bizmanage-loading');
        },

        hideLoading: function() {
            $('body').removeClass('bizmanage-loading');
        },

        showModal: function(content) {
            var modal = $('<div class="bizmanage-modal"><div class="bizmanage-modal-content"><span class="bizmanage-modal-close">&times;</span>' + content + '</div></div>');
            $('body').append(modal);
            modal.show();
        },

        closeModal: function() {
            $('.bizmanage-modal').remove();
        },

        refreshDocumentsList: function() {
            // Refresh documents list
            if ($('#bizmanage-documents-list').length) {
                location.reload();
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        BizManagePro.init();
    });

})(jQuery);

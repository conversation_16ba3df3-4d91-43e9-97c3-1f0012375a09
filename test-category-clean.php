<?php
/**
 * Test Category Creation (Clean - No Translation Warnings)
 * 
 * This script will test category creation without translation warnings
 */

// Find WordPress root directory
$wp_root = dirname(__FILE__);
$max_depth = 5;
$depth = 0;

while ($depth < $max_depth) {
    if (file_exists($wp_root . '/wp-config.php') && file_exists($wp_root . '/wp-load.php')) {
        break;
    }
    $wp_root = dirname($wp_root);
    $depth++;
}

if (!file_exists($wp_root . '/wp-load.php')) {
    die('WordPress installation not found.');
}

// WordPress environment
define('WP_USE_THEMES', false);
require_once($wp_root . '/wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Test Category Creation (Clean)</h1>';

// Test category creation without translation warnings
echo '<h2>1. Clean Category Creation Test</h2>';

if (isset($_POST['test_clean_category'])) {
    $entity_id = intval($_POST['test_entity_id']);
    $category_name = sanitize_text_field($_POST['test_category_name']);
    $category_type = sanitize_text_field($_POST['test_category_type']);
    
    if ($entity_id && $category_name && $category_type) {
        echo '<h3>Testing Clean Category Creation...</h3>';
        
        // Simulate AJAX request
        $_POST = array(
            'action' => 'bizmanage_save_category',
            'nonce' => wp_create_nonce('bizmanage_save_category'),
            'entity_id' => $entity_id,
            'name' => $category_name,
            'type' => $category_type,
            'description' => 'Clean test category',
            'color' => '#007cba'
        );
        
        echo '<p><strong>AJAX Data:</strong></p>';
        echo '<pre>' . json_encode($_POST, JSON_PRETTY_PRINT) . '</pre>';
        
        if (class_exists('BizManage_Pro_Categories')) {
            $categories = BizManage_Pro_Categories::instance();
            
            // Capture output
            ob_start();
            try {
                $categories->ajax_save_category();
                $output = ob_get_clean();
                
                echo '<p style="color: green;">✅ AJAX method executed successfully!</p>';
                echo '<p><strong>Response:</strong></p>';
                echo '<pre>' . htmlspecialchars($output) . '</pre>';
                
                // Verify in database
                global $wpdb;
                $categories_table = $wpdb->prefix . 'bizmanage_categories';
                $created_category = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $categories_table WHERE entity_id = %d AND name = %s AND type = %s ORDER BY id DESC LIMIT 1",
                    $entity_id, $category_name, $category_type
                ));
                
                if ($created_category) {
                    echo '<p style="color: green;">✅ Category verified in database!</p>';
                    echo '<p><strong>Database Record:</strong></p>';
                    echo '<pre>' . print_r($created_category, true) . '</pre>';
                } else {
                    echo '<p style="color: red;">❌ Category not found in database</p>';
                }
                
            } catch (Exception $e) {
                ob_end_clean();
                echo '<p style="color: red;">❌ Error: ' . $e->getMessage() . '</p>';
            }
        }
    } else {
        echo '<p style="color: red;">❌ Missing required fields</p>';
    }
}

// Get entities for testing
global $wpdb;
$entities_table = $wpdb->prefix . 'bizmanage_business_entities';
$entities = $wpdb->get_results("SELECT * FROM $entities_table WHERE status = 'active'");

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<table>';
    echo '<tr><td>Entity:</td><td><select name="test_entity_id" required>';
    foreach ($entities as $entity) {
        echo '<option value="' . $entity->id . '">' . $entity->business_name . ' (ID: ' . $entity->id . ')</option>';
    }
    echo '</select></td></tr>';
    echo '<tr><td>Category Name:</td><td><input type="text" name="test_category_name" value="Clean Test ' . time() . '" required></td></tr>';
    echo '<tr><td>Type:</td><td><select name="test_category_type" required>';
    echo '<option value="income">Income</option>';
    echo '<option value="expense">Expense</option>';
    echo '</select></td></tr>';
    echo '<tr><td colspan="2"><button type="submit" name="test_clean_category" value="1">Test Clean Category Creation</button></td></tr>';
    echo '</table>';
    echo '</form>';
}

// JavaScript test
echo '<h2>2. JavaScript AJAX Test</h2>';
echo '<div id="js-result"></div>';

if (!empty($entities)) {
    $entity_id = $entities[0]->id;
    echo '<button onclick="testCleanCategoryJS()">Test Category via JavaScript</button>';
    
    echo '<script>
    function testCleanCategoryJS() {
        var data = {
            action: "bizmanage_save_category",
            nonce: "' . wp_create_nonce('bizmanage_save_category') . '",
            entity_id: ' . $entity_id . ',
            name: "JS Clean Test " + Date.now(),
            type: "income",
            description: "Clean test from JavaScript",
            color: "#28a745"
        };
        
        console.log("Clean JavaScript AJAX data:", data);
        
        jQuery.post(ajaxurl, data, function(response) {
            console.log("Clean JavaScript AJAX response:", response);
            document.getElementById("js-result").innerHTML = 
                "<h4>Success Response:</h4><pre>" + JSON.stringify(response, null, 2) + "</pre>";
        }).fail(function(xhr, status, error) {
            console.log("Clean JavaScript AJAX error:", error);
            document.getElementById("js-result").innerHTML = 
                "<h4 style=\"color: red;\">Error Response:</h4><pre>" + xhr.responseText + "</pre>";
        });
    }
    </script>';
}

// Current categories
echo '<h2>3. Recent Categories</h2>';
$categories_table = $wpdb->prefix . 'bizmanage_categories';
$categories = $wpdb->get_results("SELECT * FROM $categories_table ORDER BY id DESC LIMIT 5");

if (!empty($categories)) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Entity ID</th><th>Name</th><th>Type</th><th>Status</th><th>Created</th></tr>';
    foreach ($categories as $cat) {
        echo '<tr>';
        echo '<td>' . $cat->id . '</td>';
        echo '<td>' . $cat->entity_id . '</td>';
        echo '<td>' . $cat->name . '</td>';
        echo '<td>' . $cat->type . '</td>';
        echo '<td>' . $cat->status . '</td>';
        echo '<td>' . $cat->created_at . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p>No categories found.</p>';
}

// Debug log (only BizManage entries, no translation warnings)
echo '<h2>4. Clean Debug Log (BizManage Only)</h2>';
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -20);
    
    $bizmanage_lines = array();
    foreach ($recent_lines as $line) {
        if (strpos($line, 'BizManagePro') !== false && strpos($line, 'textdomain') === false) {
            $bizmanage_lines[] = $line;
        }
    }
    
    if (!empty($bizmanage_lines)) {
        echo '<div style="background: #f0f0f0; padding: 10px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">';
        foreach ($bizmanage_lines as $line) {
            $color = 'black';
            if (strpos($line, 'successfully') !== false) {
                $color = 'green';
            } elseif (strpos($line, 'failed') !== false || strpos($line, 'error') !== false) {
                $color = 'red';
            }
            echo '<div style="color: ' . $color . ';">' . htmlspecialchars($line) . '</div>';
        }
        echo '</div>';
    } else {
        echo '<p>No recent BizManage Pro log entries (excluding translation warnings).</p>';
    }
} else {
    echo '<p>Debug log file not found.</p>';
}

// Status summary
echo '<h2>5. Status Summary</h2>';
echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;">';
echo '<h3>✅ Translation Warnings Fixed</h3>';
echo '<p>All translation strings in AJAX responses have been hardcoded to prevent early textdomain loading.</p>';
echo '<p><strong>Changes made:</strong></p>';
echo '<ul>';
echo '<li>Removed __() functions from AJAX responses</li>';
echo '<li>Used hardcoded English strings instead</li>';
echo '<li>Category functionality remains fully working</li>';
echo '<li>No more translation loading warnings</li>';
echo '</ul>';
echo '</div>';

echo '<h2>6. Next Steps</h2>';
echo '<ol>';
echo '<li><strong>Test the clean category creation above</strong></li>';
echo '<li><strong>Go to BizManage Pro Dashboard</strong> and create categories</li>';
echo '<li><strong>Check debug log</strong> - should see no more translation warnings</li>';
echo '<li><strong>Verify category functionality</strong> in the actual interface</li>';
echo '</ol>';

echo '<h2>7. Quick Links</h2>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances') . '">BizManage Pro Finances</a></p>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">BizManage Pro Dashboard</a></p>';
?>

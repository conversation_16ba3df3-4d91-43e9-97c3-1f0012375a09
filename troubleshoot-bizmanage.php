<?php
/**
 * BizManage Pro Troubleshooting Script
 * 
 * This script will diagnose and fix common issues with BizManage Pro
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>BizManage Pro Troubleshooting</h1>';

$issues = array();
$fixes_applied = array();

// Check 1: Plugin Installation
echo '<h2>1. Plugin Installation Check</h2>';
$plugin_file = 'bizmanage-pro/bizmanage-pro.php';
$plugin_path = WP_PLUGIN_DIR . '/' . $plugin_file;

if (!file_exists($plugin_path)) {
    $issues[] = 'Plugin files not found in plugins directory';
    echo '<p style="color: red;">❌ Plugin files not found: ' . $plugin_path . '</p>';
    
    // Check if files exist in current directory
    if (file_exists('bizmanage-pro.php')) {
        echo '<p style="color: orange;">⚠️ Plugin files found in current directory. Need to copy to plugins directory.</p>';
        echo '<p><a href="install-plugin.php" style="background: #0073aa; color: white; padding: 10px; text-decoration: none;">Install Plugin</a></p>';
    }
} else {
    echo '<p style="color: green;">✅ Plugin files found</p>';
}

// Check 2: Plugin Activation
echo '<h2>2. Plugin Activation Check</h2>';
if (!is_plugin_active($plugin_file)) {
    $issues[] = 'Plugin not activated';
    echo '<p style="color: red;">❌ Plugin not activated</p>';
    
    // Try to activate
    if (isset($_GET['activate_plugin'])) {
        $result = activate_plugin($plugin_file);
        if (is_wp_error($result)) {
            echo '<p style="color: red;">Activation failed: ' . $result->get_error_message() . '</p>';
        } else {
            echo '<p style="color: green;">Plugin activated successfully!</p>';
            $fixes_applied[] = 'Plugin activated';
        }
    } else {
        echo '<p><a href="?activate_plugin=1" style="background: #0073aa; color: white; padding: 10px; text-decoration: none;">Activate Plugin</a></p>';
    }
} else {
    echo '<p style="color: green;">✅ Plugin is active</p>';
}

// Check 3: Database Tables
echo '<h2>3. Database Tables Check</h2>';
global $wpdb;

$required_tables = array(
    'business_entities' => $wpdb->prefix . 'bizmanage_business_entities',
    'documents' => $wpdb->prefix . 'bizmanage_documents',
    'transactions' => $wpdb->prefix . 'bizmanage_transactions',
    'categories' => $wpdb->prefix . 'bizmanage_categories',
    'settings' => $wpdb->prefix . 'bizmanage_settings'
);

$missing_tables = array();
foreach ($required_tables as $key => $table_name) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        echo '<p style="color: green;">✅ ' . $key . ' table exists (' . $count . ' rows)</p>';
    } else {
        echo '<p style="color: red;">❌ ' . $key . ' table missing</p>';
        $missing_tables[] = $key;
    }
}

if (!empty($missing_tables)) {
    $issues[] = 'Missing database tables: ' . implode(', ', $missing_tables);
    echo '<p><a href="setup-database.php" style="background: #0073aa; color: white; padding: 10px; text-decoration: none;">Create Database Tables</a></p>';
}

// Check 4: User Capabilities
echo '<h2>4. User Capabilities Check</h2>';
$user = wp_get_current_user();
$required_caps = array(
    'bizmanage_view_dashboard',
    'bizmanage_manage_entities',
    'bizmanage_manage_finances',
    'bizmanage_manage_categories'
);

$missing_caps = array();
foreach ($required_caps as $cap) {
    if ($user->has_cap($cap)) {
        echo '<p style="color: green;">✅ ' . $cap . '</p>';
    } else {
        echo '<p style="color: red;">❌ ' . $cap . '</p>';
        $missing_caps[] = $cap;
    }
}

if (!empty($missing_caps)) {
    $issues[] = 'Missing user capabilities';
    
    // Fix capabilities
    if (isset($_GET['fix_capabilities'])) {
        $admin_role = get_role('administrator');
        if ($admin_role) {
            $all_caps = array(
                'bizmanage_view_dashboard',
                'bizmanage_manage_entities',
                'bizmanage_view_entities',
                'bizmanage_edit_entities',
                'bizmanage_delete_entities',
                'bizmanage_manage_documents',
                'bizmanage_view_documents',
                'bizmanage_upload_documents',
                'bizmanage_delete_documents',
                'bizmanage_manage_finances',
                'bizmanage_view_finances',
                'bizmanage_add_transactions',
                'bizmanage_edit_transactions',
                'bizmanage_delete_transactions',
                'bizmanage_manage_categories',
                'bizmanage_view_reports',
                'bizmanage_generate_reports',
                'bizmanage_export_reports',
                'bizmanage_manage_settings'
            );
            
            foreach ($all_caps as $cap) {
                $admin_role->add_cap($cap);
            }
            
            echo '<p style="color: green;">Capabilities added! Please refresh the page.</p>';
            $fixes_applied[] = 'User capabilities added';
        }
    } else {
        echo '<p><a href="?fix_capabilities=1" style="background: #0073aa; color: white; padding: 10px; text-decoration: none;">Fix Capabilities</a></p>';
    }
}

// Check 5: Business Entity
echo '<h2>5. Business Entity Check</h2>';
if (isset($required_tables['business_entities']) && $wpdb->get_var("SHOW TABLES LIKE '{$required_tables['business_entities']}'")) {
    $entity_count = $wpdb->get_var("SELECT COUNT(*) FROM {$required_tables['business_entities']}");
    if ($entity_count > 0) {
        echo '<p style="color: green;">✅ Business entities found (' . $entity_count . ')</p>';
        
        // Show entities
        $entities = $wpdb->get_results("SELECT id, business_name, status FROM {$required_tables['business_entities']} LIMIT 5");
        echo '<ul>';
        foreach ($entities as $entity) {
            echo '<li>ID: ' . $entity->id . ' - ' . $entity->business_name . ' (' . $entity->status . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p style="color: red;">❌ No business entities found</p>';
        $issues[] = 'No business entities';
    }
}

// Check 6: Categories
echo '<h2>6. Categories Check</h2>';
if (isset($required_tables['categories']) && $wpdb->get_var("SHOW TABLES LIKE '{$required_tables['categories']}'")) {
    $category_count = $wpdb->get_var("SELECT COUNT(*) FROM {$required_tables['categories']}");
    if ($category_count > 0) {
        echo '<p style="color: green;">✅ Categories found (' . $category_count . ')</p>';
        
        // Show categories by entity
        $categories = $wpdb->get_results("SELECT entity_id, type, COUNT(*) as count FROM {$required_tables['categories']} GROUP BY entity_id, type");
        echo '<ul>';
        foreach ($categories as $cat) {
            echo '<li>Entity ' . $cat->entity_id . ' - ' . $cat->type . ' categories: ' . $cat->count . '</li>';
        }
        echo '</ul>';
    } else {
        echo '<p style="color: red;">❌ No categories found</p>';
        $issues[] = 'No categories';
    }
}

// Check 7: Debug Logging
echo '<h2>7. Debug Logging Check</h2>';
$debug_enabled = defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG;
if ($debug_enabled) {
    echo '<p style="color: green;">✅ Debug logging enabled</p>';
    
    $log_file = WP_CONTENT_DIR . '/debug.log';
    if (file_exists($log_file)) {
        echo '<p style="color: green;">✅ Debug log file exists (' . filesize($log_file) . ' bytes)</p>';
        echo '<p><a href="check-debug-log.php">View Debug Log</a></p>';
    } else {
        echo '<p style="color: orange;">⚠️ Debug log file not found</p>';
    }
} else {
    echo '<p style="color: red;">❌ Debug logging not enabled</p>';
    $issues[] = 'Debug logging not enabled';
}

// Summary
echo '<h2>Summary</h2>';
if (empty($issues)) {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;">';
    echo '<h3>✅ All checks passed!</h3>';
    echo '<p>BizManage Pro should be working correctly.</p>';
    echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '" style="background: #28a745; color: white; padding: 10px; text-decoration: none;">Go to BizManage Pro Dashboard</a></p>';
    echo '</div>';
} else {
    echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;">';
    echo '<h3>❌ Issues Found:</h3>';
    echo '<ul>';
    foreach ($issues as $issue) {
        echo '<li>' . $issue . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

if (!empty($fixes_applied)) {
    echo '<div style="background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin-top: 10px;">';
    echo '<h3>🔧 Fixes Applied:</h3>';
    echo '<ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . $fix . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// Quick Actions
echo '<h2>Quick Actions</h2>';
echo '<div style="display: flex; gap: 10px; flex-wrap: wrap;">';
echo '<a href="install-plugin.php" style="background: #0073aa; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Install Plugin</a>';
echo '<a href="setup-database.php" style="background: #0073aa; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Setup Database</a>';
echo '<a href="debug-database-check.php" style="background: #0073aa; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Check Database</a>';
echo '<a href="check-debug-log.php" style="background: #0073aa; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Check Debug Log</a>';
echo '<a href="' . admin_url('plugins.php') . '" style="background: #0073aa; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">WordPress Plugins</a>';
echo '</div>';

// Test category creation
echo '<h2>Test Category Creation</h2>';
if (isset($_POST['test_category'])) {
    // Test creating a category via AJAX simulation
    $entity_id = $wpdb->get_var("SELECT id FROM {$required_tables['business_entities']} LIMIT 1");
    
    if ($entity_id) {
        $test_category = array(
            'entity_id' => $entity_id,
            'name' => 'Test Category ' . time(),
            'type' => 'income',
            'description' => 'Test category created by troubleshoot script',
            'color' => '#007cba',
            'status' => 'active',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        );
        
        $result = $wpdb->insert($required_tables['categories'], $test_category);
        
        if ($result) {
            echo '<p style="color: green;">✅ Test category created successfully! ID: ' . $wpdb->insert_id . '</p>';
        } else {
            echo '<p style="color: red;">❌ Failed to create test category. Error: ' . $wpdb->last_error . '</p>';
        }
    } else {
        echo '<p style="color: red;">❌ No business entity found to create test category</p>';
    }
}

echo '<form method="post">';
echo '<button type="submit" name="test_category" value="1" style="background: #28a745; color: white; padding: 10px; border: none; border-radius: 3px;">Create Test Category</button>';
echo '</form>';
?>

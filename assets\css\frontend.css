/**
 * BizManage Pro Frontend Styles
 * Styles for shortcode-generated reports on the frontend
 */

/* Base Report Styles */
.bizmanage-frontend-report {
    max-width: 100%;
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.bizmanage-report-title {
    margin: 0 0 10px 0;
    padding: 20px 20px 0 20px;
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    text-align: center;
}

.bizmanage-report-period {
    margin: 0 0 20px 0;
    padding: 0 20px;
    text-align: center;
    color: #7f8c8d;
    font-size: 14px;
    font-style: italic;
}

/* Error Messages */
.bizmanage-error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 20px 0;
    text-align: center;
}

/* Report Tables */
.bizmanage-report-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    background: #fff;
}

.bizmanage-report-table th,
.bizmanage-report-table td {
    padding: 12px 20px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.bizmanage-report-table th.section-header {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    font-size: 16px;
    border-top: 2px solid #007cba;
}

.bizmanage-report-table .amount {
    text-align: right;
    font-weight: 500;
    font-family: 'Courier New', monospace;
}

.bizmanage-report-table .total-row {
    background: #f8f9fa;
    border-top: 2px solid #dee2e6;
}

.bizmanage-report-table .total-row td {
    font-weight: 600;
}

.bizmanage-report-table .net-profit-row {
    border-top: 3px solid #007cba;
    background: #e3f2fd;
}

.bizmanage-report-table .net-profit-row.profit {
    background: #e8f5e8;
    border-top-color: #28a745;
}

.bizmanage-report-table .net-profit-row.loss {
    background: #ffeaea;
    border-top-color: #dc3545;
}

.bizmanage-report-table .profit .amount {
    color: #28a745;
}

.bizmanage-report-table .loss .amount {
    color: #dc3545;
}

/* Summary Cards */
.bizmanage-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px;
}

.bizmanage-summary-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.bizmanage-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.bizmanage-summary-card h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bizmanage-summary-card .amount {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
}

/* Card Color Variations */
.bizmanage-summary-card.revenue {
    border-left: 4px solid #007cba;
}

.bizmanage-summary-card.revenue .amount {
    color: #007cba;
}

.bizmanage-summary-card.expenses {
    border-left: 4px solid #ffc107;
}

.bizmanage-summary-card.expenses .amount {
    color: #e67e22;
}

.bizmanage-summary-card.net-profit.profit {
    border-left: 4px solid #28a745;
}

.bizmanage-summary-card.net-profit.profit .amount {
    color: #28a745;
}

.bizmanage-summary-card.net-profit.loss {
    border-left: 4px solid #dc3545;
}

.bizmanage-summary-card.net-profit.loss .amount {
    color: #dc3545;
}

.bizmanage-summary-card.transactions {
    border-left: 4px solid #6f42c1;
}

.bizmanage-summary-card.transactions .amount {
    color: #6f42c1;
}

.bizmanage-summary-card.profit-margin.profit {
    border-left: 4px solid #28a745;
}

.bizmanage-summary-card.profit-margin.profit .amount {
    color: #28a745;
}

.bizmanage-summary-card.profit-margin.loss {
    border-left: 4px solid #dc3545;
}

.bizmanage-summary-card.profit-margin.loss .amount {
    color: #dc3545;
}

/* Specific Report Styles */
.bizmanage-income-statement .bizmanage-report-table th.section-header:first-of-type {
    border-top-color: #28a745;
}

.bizmanage-balance-sheet .bizmanage-report-table th.section-header:nth-of-type(1) {
    border-top-color: #007cba;
}

.bizmanage-balance-sheet .bizmanage-report-table th.section-header:nth-of-type(2) {
    border-top-color: #ffc107;
}

.bizmanage-balance-sheet .bizmanage-report-table th.section-header:nth-of-type(3) {
    border-top-color: #28a745;
}

.bizmanage-cashflow .bizmanage-report-table th.section-header {
    border-top-color: #17a2b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bizmanage-frontend-report {
        margin: 10px 0;
        border-radius: 4px;
    }
    
    .bizmanage-report-title {
        font-size: 20px;
        padding: 15px 15px 0 15px;
    }
    
    .bizmanage-report-period {
        padding: 0 15px;
        font-size: 13px;
    }
    
    .bizmanage-report-table th,
    .bizmanage-report-table td {
        padding: 8px 15px;
        font-size: 14px;
    }
    
    .bizmanage-summary-cards {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }
    
    .bizmanage-summary-card {
        padding: 15px;
    }
    
    .bizmanage-summary-card .amount {
        font-size: 20px;
    }
    
    /* Stack table on very small screens */
    @media (max-width: 480px) {
        .bizmanage-report-table,
        .bizmanage-report-table thead,
        .bizmanage-report-table tbody,
        .bizmanage-report-table th,
        .bizmanage-report-table td,
        .bizmanage-report-table tr {
            display: block;
        }
        
        .bizmanage-report-table thead tr {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }
        
        .bizmanage-report-table tr {
            border: 1px solid #ccc;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        
        .bizmanage-report-table td {
            border: none;
            position: relative;
            padding-left: 50% !important;
            text-align: right;
        }
        
        .bizmanage-report-table td:before {
            content: attr(data-label) ": ";
            position: absolute;
            left: 6px;
            width: 45%;
            text-align: left;
            font-weight: 600;
        }
        
        .bizmanage-report-table .section-header {
            background: #007cba;
            color: white;
            text-align: center;
            font-weight: bold;
            padding: 10px;
            margin-bottom: 5px;
        }
    }
}

/* Print Styles */
@media print {
    .bizmanage-frontend-report {
        box-shadow: none;
        border: 1px solid #000;
        break-inside: avoid;
    }
    
    .bizmanage-summary-cards {
        display: block;
    }
    
    .bizmanage-summary-card {
        display: inline-block;
        width: 30%;
        margin: 1%;
        vertical-align: top;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .bizmanage-report-table {
        border: 1px solid #000;
    }
    
    .bizmanage-report-table th,
    .bizmanage-report-table td {
        border: 1px solid #000;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .bizmanage-frontend-report {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .bizmanage-report-title {
        color: #ecf0f1;
    }
    
    .bizmanage-report-period {
        color: #bdc3c7;
    }
    
    .bizmanage-report-table {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .bizmanage-report-table th.section-header {
        background: #34495e;
        color: #ecf0f1;
    }
    
    .bizmanage-report-table .total-row {
        background: #34495e;
    }
    
    .bizmanage-summary-card {
        background: #34495e;
        color: #ecf0f1;
        border-color: #4a5f7a;
    }
    
    .bizmanage-summary-card .amount {
        color: #ecf0f1;
    }
}

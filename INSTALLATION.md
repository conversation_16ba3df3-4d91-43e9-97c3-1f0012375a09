# BizManage Pro Installation Guide

This guide will walk you through the installation and initial setup of BizManage Pro.

## System Requirements

Before installing BizManage Pro, ensure your system meets the following requirements:

### Minimum Requirements
- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher
- **MySQL**: 5.6 or higher
- **Memory Limit**: 128MB (256MB recommended)
- **Disk Space**: 50MB free space

### Recommended Requirements
- **WordPress**: Latest version
- **PHP**: 8.0 or higher
- **MySQL**: 8.0 or higher
- **Memory Limit**: 256MB or higher
- **SSL Certificate**: For secure document handling

## Installation Methods

### Method 1: WordPress Admin Upload

1. **Download the Plugin**
   - Download the `bizmanage-pro.zip` file
   - Do not extract the ZIP file

2. **Upload via WordPress Admin**
   - Log in to your WordPress admin dashboard
   - Navigate to `Plugins > Add New`
   - Click `Upload Plugin`
   - Choose the `bizmanage-pro.zip` file
   - Click `Install Now`

3. **Activate the Plugin**
   - After installation, click `Activate Plugin`
   - You'll see a success message confirming activation

### Method 2: FTP Upload

1. **Extract the Plugin**
   - Extract the `bizmanage-pro.zip` file
   - You should see a `bizmanage-pro` folder

2. **Upload via FTP**
   - Connect to your website via FTP
   - Navigate to `/wp-content/plugins/`
   - Upload the entire `bizmanage-pro` folder

3. **Activate the Plugin**
   - Log in to WordPress admin
   - Navigate to `Plugins > Installed Plugins`
   - Find "BizManage Pro" and click `Activate`

### Method 3: WordPress CLI (WP-CLI)

If you have WP-CLI installed:

```bash
# Navigate to your WordPress directory
cd /path/to/wordpress

# Install the plugin
wp plugin install bizmanage-pro.zip

# Activate the plugin
wp plugin activate bizmanage-pro
```

## Initial Setup

### Step 1: Access BizManage Pro

After activation, you'll see a new menu item "BizManage Pro" in your WordPress admin sidebar.

1. Click on "BizManage Pro" to access the dashboard
2. You'll be prompted to complete the initial setup

### Step 2: Create Your First Business Entity

1. Navigate to `BizManage Pro > Business Entities`
2. Click "Add New Entity"
3. Fill in the required information:
   - **Business Name**: Your company name
   - **Entity Type**: Choose from available options
   - **Tax ID**: Your business tax identification number
   - **Address**: Business address
   - **Currency**: Default currency for transactions

4. Click "Create Entity"

### Step 3: Configure Plugin Settings

1. Navigate to `BizManage Pro > Settings`
2. Configure the following:

#### General Settings
- **Default Currency**: Set your primary currency
- **Date Format**: Choose your preferred date format
- **Time Format**: Choose your preferred time format
- **Timezone**: Set your business timezone

#### Financial Settings
- **Fiscal Year Start**: Set your fiscal year start date
- **Default Tax Rate**: Set your default tax rate
- **Backup Frequency**: Choose how often to backup data

#### Security Settings
- **File Encryption**: Enable/disable document encryption
- **Maximum File Size**: Set upload limits
- **Allowed File Types**: Configure allowed file extensions

### Step 4: Set Up User Roles

1. Navigate to `Users > All Users`
2. Edit existing users or create new ones
3. Assign appropriate BizManage Pro roles:
   - **BizManage Administrator**: Full access
   - **BizManage Accountant**: Financial access
   - **BizManage Manager**: Limited access
   - **BizManage Employee**: Expense submission
   - **BizManage Client**: View-only access

### Step 5: Create Categories

1. Navigate to `BizManage Pro > Settings`
2. Go to the "Categories" tab
3. Add custom income and expense categories
4. Set colors for visual identification

## Post-Installation Configuration

### File Permissions

Ensure the following directories have proper write permissions:

```bash
# WordPress uploads directory
chmod 755 /wp-content/uploads/
chmod 755 /wp-content/uploads/bizmanage-pro/
chmod 755 /wp-content/uploads/bizmanage-pro/documents/
chmod 755 /wp-content/uploads/bizmanage-pro/exports/
chmod 755 /wp-content/uploads/bizmanage-pro/backups/
```

### Security Considerations

1. **SSL Certificate**: Ensure your site has SSL enabled for secure data transmission
2. **File Access**: The plugin automatically creates `.htaccess` files to protect uploaded documents
3. **Database Security**: Use strong database passwords and consider database encryption
4. **Regular Backups**: Set up regular backups of your WordPress site and database

### Performance Optimization

1. **Caching**: Install a caching plugin for better performance
2. **Database Optimization**: Regularly optimize your database
3. **Image Optimization**: Optimize uploaded images to save space
4. **CDN**: Consider using a CDN for better global performance

## Troubleshooting

### Common Issues

#### Plugin Won't Activate
- Check PHP version (minimum 7.4 required)
- Verify WordPress version (minimum 5.0 required)
- Check for plugin conflicts
- Review error logs

#### File Upload Issues
- Check file permissions on upload directories
- Verify PHP upload limits (`upload_max_filesize`, `post_max_size`)
- Ensure allowed file types are configured correctly

#### Database Errors
- Verify database connection
- Check database user permissions
- Ensure sufficient disk space

#### Memory Limit Errors
- Increase PHP memory limit in `wp-config.php`:
  ```php
  ini_set('memory_limit', '256M');
  ```

### Getting Help

If you encounter issues during installation:

1. **Check System Requirements**: Ensure your server meets all requirements
2. **Review Error Logs**: Check WordPress debug logs and server error logs
3. **Plugin Conflicts**: Deactivate other plugins to identify conflicts
4. **Theme Conflicts**: Switch to a default WordPress theme temporarily

### Debug Mode

To enable debug mode for troubleshooting:

1. Add to `wp-config.php`:
   ```php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   define('WP_DEBUG_DISPLAY', false);
   ```

2. Check debug logs in `/wp-content/debug.log`

## Uninstallation

### Safe Uninstallation

1. **Backup Data**: Export all important data before uninstalling
2. **Deactivate Plugin**: Go to `Plugins > Installed Plugins` and deactivate
3. **Delete Plugin**: Click "Delete" to remove plugin files

### Data Removal

By default, the plugin preserves your data when uninstalled. To remove all data:

1. Go to `BizManage Pro > Settings`
2. Check "Remove all data on uninstall"
3. Save settings
4. Then proceed with uninstallation

**Warning**: This action is irreversible. Ensure you have backups before enabling this option.

## Next Steps

After successful installation:

1. **Add Your First Transaction**: Record an income or expense
2. **Upload Documents**: Start organizing your business documents
3. **Explore Reports**: Generate your first financial report
4. **Set Up Recurring Transactions**: Automate regular income/expenses
5. **Invite Team Members**: Add users with appropriate roles

## Support

For additional help with installation:
- Review the plugin documentation
- Check the FAQ section
- Contact support team

---

**Congratulations!** You've successfully installed BizManage Pro. You're now ready to start managing your business finances and documents efficiently.

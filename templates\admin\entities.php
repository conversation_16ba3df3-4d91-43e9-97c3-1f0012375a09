<?php
/**
 * BizManage Pro Business Entities Template
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get entities
$entities_manager = BizManage_Pro_Entities::instance();
$utilities = BizManage_Pro_Utilities::instance();

// Handle actions
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
$entity_id = isset($_GET['entity_id']) ? intval($_GET['entity_id']) : 0;

// Get entity data for editing
$entity_data = null;
if ($action === 'edit' && $entity_id > 0) {
    $entity_data = $entities_manager->get_entity($entity_id);
}

// Get entity types
$entity_types = $utilities->get_business_entity_types();
$entity_type_fields = $entities_manager->get_entity_type_fields();
?>

<div class="wrap bizmanage-admin-wrap">
    <div class="bizmanage-header">
        <h1>
            <?php 
            switch ($action) {
                case 'add':
                    _e('Add Business Entity', 'bizmanage-pro');
                    break;
                case 'edit':
                    _e('Edit Business Entity', 'bizmanage-pro');
                    break;
                default:
                    _e('Business Entities', 'bizmanage-pro');
            }
            ?>
        </h1>
        <p>
            <?php 
            switch ($action) {
                case 'add':
                    _e('Create a new business entity to manage your financial data.', 'bizmanage-pro');
                    break;
                case 'edit':
                    _e('Update your business entity information.', 'bizmanage-pro');
                    break;
                default:
                    _e('Manage your business entities and their information.', 'bizmanage-pro');
            }
            ?>
        </p>
    </div>

    <?php if ($action === 'list'): ?>
        
        <!-- List View -->
        <div style="margin-bottom: 20px;">
            <?php if (current_user_can('bizmanage_manage_entities')): ?>
            <a href="<?php echo admin_url('admin.php?page=bizmanage-entities&action=add'); ?>" class="bizmanage-btn bizmanage-btn-primary">
                <?php _e('Add New Entity', 'bizmanage-pro'); ?>
            </a>
            <?php endif; ?>
        </div>

        <div class="bizmanage-table-wrapper">
            <table class="bizmanage-table">
                <thead>
                    <tr>
                        <th><?php _e('Business Name', 'bizmanage-pro'); ?></th>
                        <th><?php _e('Entity Type', 'bizmanage-pro'); ?></th>
                        <th><?php _e('Email', 'bizmanage-pro'); ?></th>
                        <th><?php _e('Phone', 'bizmanage-pro'); ?></th>
                        <th><?php _e('Status', 'bizmanage-pro'); ?></th>
                        <th><?php _e('Actions', 'bizmanage-pro'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $entities = current_user_can('bizmanage_manage_entities') 
                        ? $entities_manager->get_all_entities() 
                        : $entities_manager->get_user_entities();
                    
                    if (empty($entities)): ?>
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px;">
                                <?php _e('No business entities found.', 'bizmanage-pro'); ?>
                                <?php if (current_user_can('bizmanage_manage_entities')): ?>
                                    <a href="<?php echo admin_url('admin.php?page=bizmanage-entities&action=add'); ?>">
                                        <?php _e('Create your first entity', 'bizmanage-pro'); ?>
                                    </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($entities as $entity): ?>
                        <tr>
                            <td><strong><?php echo esc_html($entity->business_name); ?></strong></td>
                            <td><?php echo esc_html($entity_types[$entity->entity_type] ?? $entity->entity_type); ?></td>
                            <td><?php echo esc_html($entity->email); ?></td>
                            <td><?php echo esc_html($entity->phone); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo esc_attr($entity->status); ?>">
                                    <?php echo esc_html(ucfirst($entity->status)); ?>
                                </span>
                            </td>
                            <td class="actions">
                                <a href="<?php echo admin_url('admin.php?page=bizmanage-entities&action=edit&entity_id=' . $entity->id); ?>" 
                                   class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-secondary">
                                    <?php _e('Edit', 'bizmanage-pro'); ?>
                                </a>
                                
                                <?php if (current_user_can('bizmanage_manage_entities')): ?>
                                <button type="button" 
                                        class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-danger bizmanage-delete-entity" 
                                        data-entity-id="<?php echo esc_attr($entity->id); ?>">
                                    <?php _e('Delete', 'bizmanage-pro'); ?>
                                </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

    <?php else: ?>
        
        <!-- Add/Edit Form -->
        <div class="bizmanage-form">
            <form id="entity-form" method="post">
                <?php wp_nonce_field('bizmanage_save_entity', 'entity_nonce'); ?>
                <input type="hidden" name="entity_id" value="<?php echo esc_attr($entity_id); ?>">

                <div class="form-row">
                    <div class="form-group">
                        <label for="business_name"><?php _e('Business Name', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                        <input type="text" id="business_name" name="business_name" 
                               value="<?php echo esc_attr($entity_data->business_name ?? ''); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="entity_type"><?php _e('Entity Type', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                        <select id="entity_type" name="entity_type" required>
                            <option value=""><?php _e('Select Entity Type', 'bizmanage-pro'); ?></option>
                            <?php foreach ($entity_types as $type => $label): ?>
                                <option value="<?php echo esc_attr($type); ?>" 
                                        <?php selected($entity_data->entity_type ?? '', $type); ?>>
                                    <?php echo esc_html($label); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="registration_number"><?php _e('Registration Number', 'bizmanage-pro'); ?></label>
                        <input type="text" id="registration_number" name="registration_number" 
                               value="<?php echo esc_attr($entity_data->registration_number ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="tax_id"><?php _e('Tax ID', 'bizmanage-pro'); ?></label>
                        <input type="text" id="tax_id" name="tax_id" 
                               value="<?php echo esc_attr($entity_data->tax_id ?? ''); ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="address"><?php _e('Address', 'bizmanage-pro'); ?></label>
                    <textarea id="address" name="address" rows="3"><?php echo esc_textarea($entity_data->address ?? ''); ?></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="city"><?php _e('City', 'bizmanage-pro'); ?></label>
                        <input type="text" id="city" name="city" 
                               value="<?php echo esc_attr($entity_data->city ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="state"><?php _e('State/Province', 'bizmanage-pro'); ?></label>
                        <input type="text" id="state" name="state" 
                               value="<?php echo esc_attr($entity_data->state ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="postal_code"><?php _e('Postal Code', 'bizmanage-pro'); ?></label>
                        <input type="text" id="postal_code" name="postal_code" 
                               value="<?php echo esc_attr($entity_data->postal_code ?? ''); ?>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="country"><?php _e('Country', 'bizmanage-pro'); ?></label>
                        <input type="text" id="country" name="country" 
                               value="<?php echo esc_attr($entity_data->country ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="phone"><?php _e('Phone', 'bizmanage-pro'); ?></label>
                        <input type="tel" id="phone" name="phone" 
                               value="<?php echo esc_attr($entity_data->phone ?? ''); ?>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="email"><?php _e('Email', 'bizmanage-pro'); ?></label>
                        <input type="email" id="email" name="email" 
                               value="<?php echo esc_attr($entity_data->email ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="website"><?php _e('Website', 'bizmanage-pro'); ?></label>
                        <input type="url" id="website" name="website" 
                               value="<?php echo esc_attr($entity_data->website ?? ''); ?>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="currency"><?php _e('Default Currency', 'bizmanage-pro'); ?></label>
                        <select id="currency" name="currency">
                            <?php 
                            $currencies = $utilities->get_currencies();
                            foreach ($currencies as $code => $currency_data): ?>
                                <option value="<?php echo esc_attr($code); ?>" 
                                        <?php selected($entity_data->currency ?? 'USD', $code); ?>>
                                    <?php echo esc_html($code . ' - ' . $currency_data['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="timezone"><?php _e('Timezone', 'bizmanage-pro'); ?></label>
                        <select id="timezone" name="timezone">
                            <?php 
                            $timezones = timezone_identifiers_list();
                            foreach ($timezones as $timezone): ?>
                                <option value="<?php echo esc_attr($timezone); ?>" 
                                        <?php selected($entity_data->timezone ?? 'UTC', $timezone); ?>>
                                    <?php echo esc_html($timezone); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="form-actions" style="margin-top: 30px;">
                    <button type="button" class="bizmanage-btn bizmanage-btn-primary bizmanage-save-entity">
                        <?php echo $action === 'edit' ? __('Update Entity', 'bizmanage-pro') : __('Create Entity', 'bizmanage-pro'); ?>
                    </button>
                    
                    <a href="<?php echo admin_url('admin.php?page=bizmanage-entities'); ?>" 
                       class="bizmanage-btn bizmanage-btn-secondary">
                        <?php _e('Cancel', 'bizmanage-pro'); ?>
                    </a>
                </div>
            </form>
        </div>

    <?php endif; ?>
</div>

<style>
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.required {
    color: #dc3545;
}

.form-actions {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
}
</style>

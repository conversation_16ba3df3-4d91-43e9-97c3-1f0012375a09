<?php
/**
 * Install BizManage Pro Plugin
 * 
 * This script will copy the plugin files to the correct WordPress plugins directory
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>BizManage Pro Plugin Installation</h1>';

// Define paths
$source_dir = __DIR__;
$plugins_dir = WP_PLUGIN_DIR;
$target_dir = $plugins_dir . '/bizmanage-pro';

echo '<p><strong>Source Directory:</strong> ' . $source_dir . '</p>';
echo '<p><strong>Target Directory:</strong> ' . $target_dir . '</p>';

// Check if source files exist
$required_files = array(
    'bizmanage-pro.php',
    'includes/class-bizmanage-pro-database.php',
    'includes/class-bizmanage-pro-installer.php',
    'admin/class-bizmanage-pro-admin.php'
);

$missing_files = array();
foreach ($required_files as $file) {
    if (!file_exists($source_dir . '/' . $file)) {
        $missing_files[] = $file;
    }
}

if (!empty($missing_files)) {
    echo '<p style="color: red;">Missing required files:</p>';
    echo '<ul>';
    foreach ($missing_files as $file) {
        echo '<li>' . $file . '</li>';
    }
    echo '</ul>';
    echo '<p>Please ensure all plugin files are in the current directory.</p>';
    exit;
}

// Create target directory if it doesn't exist
if (!is_dir($target_dir)) {
    if (!mkdir($target_dir, 0755, true)) {
        die('<p style="color: red;">Failed to create target directory: ' . $target_dir . '</p>');
    }
    echo '<p>Created target directory: ' . $target_dir . '</p>';
}

// Function to copy directory recursively
function copy_directory($src, $dst) {
    $dir = opendir($src);
    @mkdir($dst);
    
    while (($file = readdir($dir)) !== false) {
        if ($file != '.' && $file != '..') {
            if (is_dir($src . '/' . $file)) {
                copy_directory($src . '/' . $file, $dst . '/' . $file);
            } else {
                copy($src . '/' . $file, $dst . '/' . $file);
            }
        }
    }
    
    closedir($dir);
}

// Copy plugin files
try {
    // Copy main plugin file
    copy($source_dir . '/bizmanage-pro.php', $target_dir . '/bizmanage-pro.php');
    echo '<p>Copied: bizmanage-pro.php</p>';
    
    // Copy directories
    $directories = array('includes', 'admin', 'assets', 'templates', 'languages', 'public');
    
    foreach ($directories as $dir) {
        if (is_dir($source_dir . '/' . $dir)) {
            copy_directory($source_dir . '/' . $dir, $target_dir . '/' . $dir);
            echo '<p>Copied directory: ' . $dir . '</p>';
        }
    }
    
    // Copy documentation files
    $docs = array('README.md', 'INSTALLATION.md', 'QUICK-START.md');
    foreach ($docs as $doc) {
        if (file_exists($source_dir . '/' . $doc)) {
            copy($source_dir . '/' . $doc, $target_dir . '/' . $doc);
            echo '<p>Copied: ' . $doc . '</p>';
        }
    }
    
    echo '<p style="color: green;">Plugin files copied successfully!</p>';
    
} catch (Exception $e) {
    echo '<p style="color: red;">Error copying files: ' . $e->getMessage() . '</p>';
    exit;
}

// Activate plugin
$plugin_file = 'bizmanage-pro/bizmanage-pro.php';

// Check if plugin file exists in plugins directory
if (!file_exists($plugins_dir . '/' . $plugin_file)) {
    echo '<p style="color: red;">Plugin file not found after copy: ' . $plugins_dir . '/' . $plugin_file . '</p>';
    exit;
}

// Activate the plugin
$result = activate_plugin($plugin_file);

if (is_wp_error($result)) {
    echo '<p style="color: red;">Plugin activation failed: ' . $result->get_error_message() . '</p>';
} else {
    echo '<p style="color: green;">Plugin activated successfully!</p>';
}

// Verify activation
if (is_plugin_active($plugin_file)) {
    echo '<p style="color: green;">Plugin is now active and ready to use!</p>';
} else {
    echo '<p style="color: orange;">Plugin copied but activation verification failed. Try activating manually from WordPress admin.</p>';
}

// Add administrator capabilities
$admin_role = get_role('administrator');
if ($admin_role) {
    $capabilities = array(
        'bizmanage_view_dashboard',
        'bizmanage_manage_entities',
        'bizmanage_view_entities',
        'bizmanage_edit_entities',
        'bizmanage_delete_entities',
        'bizmanage_manage_documents',
        'bizmanage_view_documents',
        'bizmanage_upload_documents',
        'bizmanage_delete_documents',
        'bizmanage_manage_finances',
        'bizmanage_view_finances',
        'bizmanage_add_transactions',
        'bizmanage_edit_transactions',
        'bizmanage_delete_transactions',
        'bizmanage_manage_categories',
        'bizmanage_view_reports',
        'bizmanage_generate_reports',
        'bizmanage_export_reports',
        'bizmanage_manage_settings',
        'bizmanage_view_settings',
        'bizmanage_manage_users',
        'bizmanage_backup_restore',
        'bizmanage_system_info'
    );
    
    foreach ($capabilities as $cap) {
        $admin_role->add_cap($cap);
    }
    
    echo '<p>Administrator capabilities added.</p>';
}

echo '<h2>Installation Complete!</h2>';
echo '<p><strong>Next Steps:</strong></p>';
echo '<ol>';
echo '<li><a href="setup-database.php">Run Database Setup</a> (if not done already)</li>';
echo '<li><a href="' . admin_url('plugins.php') . '">Check Plugins Page</a></li>';
echo '<li><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">Go to BizManage Pro Dashboard</a></li>';
echo '</ol>';

// Clean up - remove installation files from WordPress root
$cleanup_files = array('install-plugin.php', 'setup-database.php', 'debug-database-check.php', 'reactivate-plugin.php');
echo '<h3>Cleanup</h3>';
echo '<p>You can now safely delete these installation files from WordPress root:</p>';
echo '<ul>';
foreach ($cleanup_files as $file) {
    if (file_exists($file)) {
        echo '<li>' . $file . '</li>';
    }
}
echo '</ul>';
?>

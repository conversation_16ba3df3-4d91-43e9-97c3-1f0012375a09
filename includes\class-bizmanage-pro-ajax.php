<?php
/**
 * BizManage Pro AJAX Class
 *
 * Handles AJAX requests and responses
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro AJAX Class
 */
class BizManage_Pro_Ajax {

    /**
     * Instance of this class
     * @var BizManage_Pro_Ajax
     */
    private static $instance = null;

    /**
     * Security instance
     * @var BizManage_Pro_Security
     */
    private $security;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Get instance
     * @return BizManage_Pro_Ajax
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->security = BizManage_Pro_Security::instance();
        $this->db = BizManage_Pro_Database::instance();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Entity management
        add_action('wp_ajax_bizmanage_save_entity', array($this, 'save_entity'));
        add_action('wp_ajax_bizmanage_delete_entity', array($this, 'delete_entity'));
        add_action('wp_ajax_bizmanage_get_entity', array($this, 'get_entity'));

        // Document management
        add_action('wp_ajax_bizmanage_upload_document', array($this, 'upload_document'));
        add_action('wp_ajax_bizmanage_delete_document', array($this, 'delete_document'));
        add_action('wp_ajax_bizmanage_get_documents', array($this, 'get_documents'));

        // Financial management
        add_action('wp_ajax_bizmanage_save_transaction', array($this, 'save_transaction'));
        add_action('wp_ajax_bizmanage_delete_transaction', array($this, 'delete_transaction'));
        add_action('wp_ajax_bizmanage_get_transactions', array($this, 'get_transactions'));
        add_action('wp_ajax_bizmanage_get_financial_summary', array($this, 'get_financial_summary'));

        // Categories
        add_action('wp_ajax_bizmanage_save_category', array($this, 'save_category'));
        add_action('wp_ajax_bizmanage_delete_category', array($this, 'delete_category'));
        add_action('wp_ajax_bizmanage_get_categories', array($this, 'get_categories'));

        // Reports
        add_action('wp_ajax_bizmanage_generate_report', array($this, 'generate_report'));
        add_action('wp_ajax_bizmanage_export_report', array($this, 'export_report'));

        // Dashboard
        add_action('wp_ajax_bizmanage_get_dashboard_data', array($this, 'get_dashboard_data'));

        // Categories
        add_action('wp_ajax_bizmanage_save_category', array($this, 'save_category'));
        add_action('wp_ajax_bizmanage_delete_category', array($this, 'delete_category'));
        add_action('wp_ajax_bizmanage_get_categories', array($this, 'get_categories'));

        // Reports
        add_action('wp_ajax_bizmanage_generate_report', array($this, 'generate_report'));
        add_action('wp_ajax_bizmanage_export_report', array($this, 'export_report'));

        // Settings
        add_action('wp_ajax_bizmanage_save_settings', array($this, 'save_settings'));

        // Settings
        add_action('wp_ajax_bizmanage_save_settings', array($this, 'save_settings'));
    }

    /**
     * Send JSON response
     * @param bool $success
     * @param mixed $data
     * @param string $message
     */
    private function send_response($success, $data = null, $message = '') {
        wp_send_json(array(
            'success' => $success,
            'data' => $data,
            'message' => $message
        ));
    }

    /**
     * Verify AJAX request
     * @param string $action
     * @param string $capability
     * @return bool
     */
    private function verify_request($action, $capability = '') {
        // Check nonce
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', $action)) {
            $this->send_response(false, null, __('Security check failed.', 'bizmanage-pro'));
            return false;
        }

        // Check capability
        if (!empty($capability) && !current_user_can($capability)) {
            $this->send_response(false, null, __('You do not have permission to perform this action.', 'bizmanage-pro'));
            return false;
        }

        return true;
    }

    /**
     * Save entity
     */
    public function save_entity() {
        if (!$this->verify_request('bizmanage_save_entity', 'bizmanage_manage_entities')) {
            return;
        }

        $entity_id = intval($_POST['entity_id'] ?? 0);
        $entity_data = array(
            'user_id' => get_current_user_id(),
            'entity_type' => $this->security->sanitize_input($_POST['entity_type'] ?? '', 'text'),
            'business_name' => $this->security->sanitize_input($_POST['business_name'] ?? '', 'text'),
            'registration_number' => $this->security->sanitize_input($_POST['registration_number'] ?? '', 'text'),
            'tax_id' => $this->security->sanitize_input($_POST['tax_id'] ?? '', 'text'),
            'address' => $this->security->sanitize_input($_POST['address'] ?? '', 'textarea'),
            'city' => $this->security->sanitize_input($_POST['city'] ?? '', 'text'),
            'state' => $this->security->sanitize_input($_POST['state'] ?? '', 'text'),
            'postal_code' => $this->security->sanitize_input($_POST['postal_code'] ?? '', 'text'),
            'country' => $this->security->sanitize_input($_POST['country'] ?? '', 'text'),
            'phone' => $this->security->sanitize_input($_POST['phone'] ?? '', 'text'),
            'email' => $this->security->sanitize_input($_POST['email'] ?? '', 'email'),
            'website' => $this->security->sanitize_input($_POST['website'] ?? '', 'url'),
            'currency' => $this->security->sanitize_input($_POST['currency'] ?? 'USD', 'text'),
            'timezone' => $this->security->sanitize_input($_POST['timezone'] ?? '', 'text'),
        );

        // Validate required fields
        $errors = array();
        if (empty($entity_data['business_name'])) {
            $errors[] = __('Business name is required.', 'bizmanage-pro');
        }
        if (empty($entity_data['entity_type'])) {
            $errors[] = __('Entity type is required.', 'bizmanage-pro');
        }

        if (!empty($errors)) {
            $this->send_response(false, null, implode(' ', $errors));
            return;
        }

        if ($entity_id > 0) {
            // Update existing entity
            $result = $this->db->update('business_entities', $entity_data, array('id' => $entity_id));
            $message = __('Entity updated successfully.', 'bizmanage-pro');
        } else {
            // Create new entity
            $result = $this->db->insert('business_entities', $entity_data);
            $entity_id = $this->db->get_insert_id();
            $message = __('Entity created successfully.', 'bizmanage-pro');
        }

        if ($result !== false) {
            $this->send_response(true, array('entity_id' => $entity_id), $message);
        } else {
            $this->send_response(false, null, __('Failed to save entity.', 'bizmanage-pro'));
        }
    }

    /**
     * Delete entity
     */
    public function delete_entity() {
        if (!$this->verify_request('bizmanage_delete_entity', 'bizmanage_manage_entities')) {
            return;
        }

        $entity_id = intval($_POST['entity_id'] ?? 0);

        if ($entity_id <= 0) {
            $this->send_response(false, null, __('Invalid entity ID.', 'bizmanage-pro'));
            return;
        }

        $result = $this->db->update('business_entities', 
            array('status' => 'deleted'), 
            array('id' => $entity_id)
        );

        if ($result !== false) {
            $this->send_response(true, null, __('Entity deleted successfully.', 'bizmanage-pro'));
        } else {
            $this->send_response(false, null, __('Failed to delete entity.', 'bizmanage-pro'));
        }
    }

    /**
     * Get entity
     */
    public function get_entity() {
        if (!$this->verify_request('bizmanage_get_entity', 'bizmanage_view_entities')) {
            return;
        }

        $entity_id = intval($_POST['entity_id'] ?? 0);

        if ($entity_id <= 0) {
            $this->send_response(false, null, __('Invalid entity ID.', 'bizmanage-pro'));
            return;
        }

        $entity = $this->db->get_row('business_entities', array('id' => $entity_id));

        if ($entity) {
            $this->send_response(true, $entity);
        } else {
            $this->send_response(false, null, __('Entity not found.', 'bizmanage-pro'));
        }
    }

    /**
     * Upload document
     */
    public function upload_document() {
        if (!$this->verify_request('bizmanage_upload_document', 'bizmanage_upload_documents')) {
            return;
        }

        if (empty($_FILES['document'])) {
            $this->send_response(false, null, __('No file uploaded.', 'bizmanage-pro'));
            return;
        }

        $file = $_FILES['document'];
        $validation_errors = $this->security->validate_file_upload($file);

        if (!empty($validation_errors)) {
            $this->send_response(false, null, implode(' ', $validation_errors));
            return;
        }

        // Process file upload
        $upload_dir = wp_upload_dir();
        $bizmanage_dir = $upload_dir['basedir'] . '/bizmanage-pro/documents';
        
        if (!file_exists($bizmanage_dir)) {
            wp_mkdir_p($bizmanage_dir);
        }

        $filename = sanitize_file_name($file['name']);
        $file_path = $bizmanage_dir . '/' . $filename;

        // Generate unique filename if file exists
        $utilities = BizManage_Pro_Utilities::instance();
        $filename = $utilities->generate_unique_filename($filename, $bizmanage_dir);
        $file_path = $bizmanage_dir . '/' . $filename;

        if (move_uploaded_file($file['tmp_name'], $file_path)) {
            // Encrypt file if enabled
            $encrypted = false;
            $encryption_key = null;
            
            if (get_option('bizmanage_pro_encryption_enabled', '1') === '1') {
                $file_content = file_get_contents($file_path);
                $encrypted_content = $this->security->encrypt_data($file_content);
                
                if ($encrypted_content) {
                    file_put_contents($file_path, $encrypted_content);
                    $encrypted = true;
                    $encryption_key = get_option('bizmanage_pro_encryption_key');
                }
            }

            // Save document record
            $document_data = array(
                'entity_id' => intval($_POST['entity_id'] ?? 0),
                'user_id' => get_current_user_id(),
                'title' => $this->security->sanitize_input($_POST['title'] ?? $filename, 'text'),
                'description' => $this->security->sanitize_input($_POST['description'] ?? '', 'textarea'),
                'category' => $this->security->sanitize_input($_POST['category'] ?? 'other', 'text'),
                'file_name' => $filename,
                'file_path' => $file_path,
                'file_size' => $file['size'],
                'file_type' => pathinfo($filename, PATHINFO_EXTENSION),
                'mime_type' => $file['type'],
                'encrypted' => $encrypted ? 1 : 0,
                'encryption_key' => $encryption_key,
            );

            $document_id = $this->db->insert('documents', $document_data);

            if ($document_id) {
                $this->send_response(true, array('document_id' => $document_id), __('Document uploaded successfully.', 'bizmanage-pro'));
            } else {
                unlink($file_path); // Remove uploaded file if database insert failed
                $this->send_response(false, null, __('Failed to save document record.', 'bizmanage-pro'));
            }
        } else {
            $this->send_response(false, null, __('Failed to upload file.', 'bizmanage-pro'));
        }
    }

    /**
     * Get dashboard data
     */
    public function get_dashboard_data() {
        if (!$this->verify_request('bizmanage_get_dashboard_data')) {
            return;
        }

        $entity_id = intval($_POST['entity_id'] ?? 0);
        $period = $this->security->sanitize_input($_POST['period'] ?? 'month', 'text');

        // Calculate date range
        $end_date = current_time('Y-m-d');
        switch ($period) {
            case 'week':
                $start_date = date('Y-m-d', strtotime('-7 days'));
                break;
            case 'month':
                $start_date = date('Y-m-d', strtotime('-30 days'));
                break;
            case 'quarter':
                $start_date = date('Y-m-d', strtotime('-90 days'));
                break;
            case 'year':
                $start_date = date('Y-m-d', strtotime('-365 days'));
                break;
            default:
                $start_date = date('Y-m-d', strtotime('-30 days'));
        }

        $where_conditions = array('status' => 'completed');
        if ($entity_id > 0) {
            $where_conditions['entity_id'] = $entity_id;
        }

        // Get income and expense totals
        $income_total = $this->db->get_var(
            $this->db->prepare(
                "SELECT SUM(amount) FROM {$this->db->get_table('transactions')} 
                WHERE transaction_type = 'income' 
                AND transaction_date BETWEEN %s AND %s 
                AND status = 'completed'" . 
                ($entity_id > 0 ? " AND entity_id = %d" : ""),
                $start_date, $end_date, $entity_id
            )
        );

        $expense_total = $this->db->get_var(
            $this->db->prepare(
                "SELECT SUM(amount) FROM {$this->db->get_table('transactions')} 
                WHERE transaction_type = 'expense' 
                AND transaction_date BETWEEN %s AND %s 
                AND status = 'completed'" . 
                ($entity_id > 0 ? " AND entity_id = %d" : ""),
                $start_date, $end_date, $entity_id
            )
        );

        $dashboard_data = array(
            'income_total' => floatval($income_total),
            'expense_total' => floatval($expense_total),
            'profit' => floatval($income_total) - floatval($expense_total),
            'period' => $period,
            'start_date' => $start_date,
            'end_date' => $end_date,
        );

        $this->send_response(true, $dashboard_data);
    }

    /**
     * Delete document
     */
    public function delete_document() {
        if (!$this->verify_request('bizmanage_delete_document', 'bizmanage_manage_documents')) {
            return;
        }

        $document_id = intval($_POST['document_id'] ?? 0);
        $documents = BizManage_Pro_Documents::instance();

        if ($documents->delete_document($document_id)) {
            $this->send_response(true, __('Document deleted successfully.', 'bizmanage-pro'));
        } else {
            $this->send_response(false, __('Failed to delete document.', 'bizmanage-pro'));
        }
    }

    /**
     * Get documents
     */
    public function get_documents() {
        if (!$this->verify_request('bizmanage_get_documents', 'bizmanage_view_documents')) {
            return;
        }

        $entity_id = intval($_POST['entity_id'] ?? 0);
        $search = sanitize_text_field($_POST['search'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');

        $documents = BizManage_Pro_Documents::instance();
        $results = $documents->get_documents(array(
            'entity_id' => $entity_id,
            'search' => $search,
            'category' => $category
        ));

        $this->send_response(true, $results);
    }

    /**
     * Save transaction
     */
    public function save_transaction() {
        if (!$this->verify_request('bizmanage_save_transaction', 'bizmanage_manage_finances')) {
            return;
        }

        $transaction_id = intval($_POST['transaction_id'] ?? 0);
        $finances = BizManage_Pro_Finances::instance();

        if ($transaction_id > 0) {
            // Update existing transaction
            if ($finances->update_transaction($transaction_id, $_POST)) {
                $this->send_response(true, __('Transaction updated successfully.', 'bizmanage-pro'));
            } else {
                $this->send_response(false, __('Failed to update transaction.', 'bizmanage-pro'));
            }
        } else {
            // Create new transaction
            $result = $finances->create_transaction($_POST);
            if ($result) {
                $this->send_response(true, __('Transaction saved successfully.', 'bizmanage-pro'), array('transaction_id' => $result));
            } else {
                $this->send_response(false, __('Failed to save transaction.', 'bizmanage-pro'));
            }
        }
    }

    /**
     * Delete transaction
     */
    public function delete_transaction() {
        if (!$this->verify_request('bizmanage_delete_transaction', 'bizmanage_manage_finances')) {
            return;
        }

        $transaction_id = intval($_POST['transaction_id'] ?? 0);
        $finances = BizManage_Pro_Finances::instance();

        if ($finances->delete_transaction($transaction_id)) {
            $this->send_response(true, __('Transaction deleted successfully.', 'bizmanage-pro'));
        } else {
            $this->send_response(false, __('Failed to delete transaction.', 'bizmanage-pro'));
        }
    }

    /**
     * Get transactions
     */
    public function get_transactions() {
        if (!$this->verify_request('bizmanage_get_transactions', 'bizmanage_view_finances')) {
            return;
        }

        $entity_id = intval($_POST['entity_id'] ?? 0);
        $transaction_type = sanitize_text_field($_POST['transaction_type'] ?? '');
        $start_date = sanitize_text_field($_POST['start_date'] ?? '');
        $end_date = sanitize_text_field($_POST['end_date'] ?? '');

        $finances = BizManage_Pro_Finances::instance();
        $results = $finances->get_transactions(array(
            'entity_id' => $entity_id,
            'transaction_type' => $transaction_type,
            'start_date' => $start_date,
            'end_date' => $end_date
        ));

        $this->send_response(true, $results);
    }

    /**
     * Get financial summary
     */
    public function get_financial_summary() {
        if (!$this->verify_request('bizmanage_get_financial_summary', 'bizmanage_view_finances')) {
            return;
        }

        $entity_id = intval($_POST['entity_id'] ?? 0);
        $start_date = sanitize_text_field($_POST['start_date'] ?? date('Y-m-01'));
        $end_date = sanitize_text_field($_POST['end_date'] ?? date('Y-m-t'));

        $finances = BizManage_Pro_Finances::instance();
        $summary = $finances->get_financial_summary($entity_id, $start_date, $end_date);

        $this->send_response(true, $summary);
    }

    /**
     * Save category
     */
    public function save_category() {
        if (!$this->verify_request('bizmanage_save_category', 'bizmanage_manage_categories')) {
            return;
        }

        // This would be implemented when category management is added
        $this->send_response(false, __('Category management not yet implemented.', 'bizmanage-pro'));
    }

    /**
     * Delete category
     */
    public function delete_category() {
        if (!$this->verify_request('bizmanage_delete_category', 'bizmanage_manage_categories')) {
            return;
        }

        // This would be implemented when category management is added
        $this->send_response(false, __('Category management not yet implemented.', 'bizmanage-pro'));
    }

    /**
     * Get categories
     */
    public function get_categories() {
        if (!$this->verify_request('bizmanage_get_categories', 'bizmanage_view_categories')) {
            return;
        }

        $type = sanitize_text_field($_POST['type'] ?? '');
        $entity_id = intval($_POST['entity_id'] ?? 0);

        // For now, return default categories
        $utilities = BizManage_Pro_Utilities::instance();
        $categories = array();

        if ($type === 'income') {
            $categories = array(
                array('name' => 'Product Sales', 'type' => 'income'),
                array('name' => 'Service Revenue', 'type' => 'income'),
                array('name' => 'Consulting Fees', 'type' => 'income'),
                array('name' => 'Other Income', 'type' => 'income'),
            );
        } elseif ($type === 'expense') {
            $categories = array(
                array('name' => 'Office Rent', 'type' => 'expense'),
                array('name' => 'Utilities', 'type' => 'expense'),
                array('name' => 'Office Supplies', 'type' => 'expense'),
                array('name' => 'Travel Expenses', 'type' => 'expense'),
                array('name' => 'Marketing', 'type' => 'expense'),
                array('name' => 'Other Expenses', 'type' => 'expense'),
            );
        }

        $this->send_response(true, $categories);
    }

    /**
     * Generate report
     */
    public function generate_report() {
        if (!$this->verify_request('bizmanage_generate_report', 'bizmanage_view_reports')) {
            return;
        }

        $reports = BizManage_Pro_Reports::instance();
        $reports->ajax_generate_report();
    }

    /**
     * Export report
     */
    public function export_report() {
        if (!$this->verify_request('bizmanage_export_report', 'bizmanage_export_reports')) {
            return;
        }

        $reports = BizManage_Pro_Reports::instance();
        $reports->ajax_export_report();
    }

    /**
     * Save settings
     */
    public function save_settings() {
        if (!$this->verify_request('bizmanage_save_settings', 'bizmanage_manage_settings')) {
            return;
        }

        $settings = BizManage_Pro_Settings::instance();
        $settings->ajax_save_settings();
    }
}

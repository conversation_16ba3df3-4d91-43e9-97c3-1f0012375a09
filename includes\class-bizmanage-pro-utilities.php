<?php
/**
 * BizManage Pro Utilities Class
 *
 * Provides utility functions and helpers
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Utilities Class
 */
class BizManage_Pro_Utilities {

    /**
     * Instance of this class
     * @var BizManage_Pro_Utilities
     */
    private static $instance = null;

    /**
     * Currency data
     * @var array
     */
    private $currencies;

    /**
     * Get instance
     * @return BizManage_Pro_Utilities
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_currencies();
    }

    /**
     * Initialize currencies
     */
    private function init_currencies() {
        $this->currencies = array(
            'USD' => array('name' => 'US Dollar', 'symbol' => '$', 'position' => 'before'),
            'EUR' => array('name' => 'Euro', 'symbol' => '€', 'position' => 'before'),
            'GBP' => array('name' => 'British Pound', 'symbol' => '£', 'position' => 'before'),
            'JPY' => array('name' => 'Japanese Yen', 'symbol' => '¥', 'position' => 'before'),
            'CAD' => array('name' => 'Canadian Dollar', 'symbol' => 'C$', 'position' => 'before'),
            'AUD' => array('name' => 'Australian Dollar', 'symbol' => 'A$', 'position' => 'before'),
            'CHF' => array('name' => 'Swiss Franc', 'symbol' => 'CHF', 'position' => 'after'),
            'CNY' => array('name' => 'Chinese Yuan', 'symbol' => '¥', 'position' => 'before'),
            'INR' => array('name' => 'Indian Rupee', 'symbol' => '₹', 'position' => 'before'),
            'BDT' => array('name' => 'Bangladeshi Taka', 'symbol' => '৳', 'position' => 'before'),
        );
    }

    /**
     * Format currency amount
     * @param float $amount
     * @param string $currency
     * @param bool $show_symbol
     * @return string
     */
    public function format_currency($amount, $currency = null, $show_symbol = true) {
        if (is_null($currency)) {
            $currency = get_option('bizmanage_pro_default_currency', 'USD');
        }

        $formatted_amount = number_format($amount, 2, '.', ',');

        if (!$show_symbol) {
            return $formatted_amount;
        }

        $currency_data = $this->get_currency_data($currency);
        $symbol = $currency_data['symbol'];
        $position = $currency_data['position'];

        if ($position === 'before') {
            return $symbol . $formatted_amount;
        } else {
            return $formatted_amount . ' ' . $symbol;
        }
    }

    /**
     * Get currency data
     * @param string $currency
     * @return array
     */
    public function get_currency_data($currency) {
        return isset($this->currencies[$currency]) ? $this->currencies[$currency] : $this->currencies['USD'];
    }

    /**
     * Get all currencies
     * @return array
     */
    public function get_currencies() {
        return $this->currencies;
    }

    /**
     * Convert currency
     * @param float $amount
     * @param string $from_currency
     * @param string $to_currency
     * @return float
     */
    public function convert_currency($amount, $from_currency, $to_currency) {
        if ($from_currency === $to_currency) {
            return $amount;
        }

        // Get exchange rate (you would implement API integration here)
        $exchange_rate = $this->get_exchange_rate($from_currency, $to_currency);
        
        return $amount * $exchange_rate;
    }

    /**
     * Get exchange rate
     * @param string $from_currency
     * @param string $to_currency
     * @return float
     */
    private function get_exchange_rate($from_currency, $to_currency) {
        // This is a placeholder. In a real implementation, you would:
        // 1. Check cached rates
        // 2. Call external API (like exchangerate-api.com)
        // 3. Cache the result
        
        $cached_rate = get_transient("bizmanage_exchange_rate_{$from_currency}_{$to_currency}");
        
        if ($cached_rate !== false) {
            return $cached_rate;
        }

        // Default rate (implement API call here)
        $rate = 1.0;
        
        // Cache for 1 hour
        set_transient("bizmanage_exchange_rate_{$from_currency}_{$to_currency}", $rate, HOUR_IN_SECONDS);
        
        return $rate;
    }

    /**
     * Format date
     * @param string $date
     * @param string $format
     * @return string
     */
    public function format_date($date, $format = null) {
        if (is_null($format)) {
            $format = get_option('bizmanage_pro_date_format', 'Y-m-d');
        }

        if (empty($date)) {
            return '';
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return date($format, $timestamp);
    }

    /**
     * Format time
     * @param string $time
     * @param string $format
     * @return string
     */
    public function format_time($time, $format = null) {
        if (is_null($format)) {
            $format = get_option('bizmanage_pro_time_format', 'H:i:s');
        }

        if (empty($time)) {
            return '';
        }

        $timestamp = is_numeric($time) ? $time : strtotime($time);
        return date($format, $timestamp);
    }

    /**
     * Get file size in human readable format
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    public function format_file_size($bytes, $precision = 2) {
        return size_format($bytes, $precision);
    }

    /**
     * Generate unique filename
     * @param string $filename
     * @param string $directory
     * @return string
     */
    public function generate_unique_filename($filename, $directory) {
        $info = pathinfo($filename);
        $name = $info['filename'];
        $extension = isset($info['extension']) ? '.' . $info['extension'] : '';
        
        $counter = 1;
        $new_filename = $filename;
        
        while (file_exists($directory . '/' . $new_filename)) {
            $new_filename = $name . '_' . $counter . $extension;
            $counter++;
        }
        
        return $new_filename;
    }

    /**
     * Get MIME type from file extension
     * @param string $extension
     * @return string
     */
    public function get_mime_type($extension) {
        $mime_types = array(
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'txt' => 'text/plain',
            'csv' => 'text/csv',
        );

        return isset($mime_types[$extension]) ? $mime_types[$extension] : 'application/octet-stream';
    }

    /**
     * Calculate percentage
     * @param float $value
     * @param float $total
     * @param int $precision
     * @return float
     */
    public function calculate_percentage($value, $total, $precision = 2) {
        if ($total == 0) {
            return 0;
        }
        
        return round(($value / $total) * 100, $precision);
    }

    /**
     * Calculate tax amount
     * @param float $amount
     * @param float $tax_rate
     * @return float
     */
    public function calculate_tax($amount, $tax_rate) {
        return round($amount * ($tax_rate / 100), 2);
    }

    /**
     * Generate random color
     * @return string
     */
    public function generate_random_color() {
        $colors = array(
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
            '#4BC0C0', '#FF6384', '#36A2EB', '#FFCE56'
        );
        
        return $colors[array_rand($colors)];
    }

    /**
     * Slugify string
     * @param string $text
     * @return string
     */
    public function slugify($text) {
        return sanitize_title($text);
    }

    /**
     * Truncate text
     * @param string $text
     * @param int $length
     * @param string $suffix
     * @return string
     */
    public function truncate_text($text, $length = 100, $suffix = '...') {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . $suffix;
    }

    /**
     * Get business entity types
     * @return array
     */
    public function get_business_entity_types() {
        return array(
            'sole_proprietorship' => __('Sole Proprietorship', 'bizmanage-pro'),
            'partnership' => __('Partnership', 'bizmanage-pro'),
            'limited_company' => __('Limited Company', 'bizmanage-pro'),
            'corporation' => __('Corporation', 'bizmanage-pro'),
            'llc' => __('Limited Liability Company (LLC)', 'bizmanage-pro'),
        );
    }

    /**
     * Get transaction types
     * @return array
     */
    public function get_transaction_types() {
        return array(
            'income' => __('Income', 'bizmanage-pro'),
            'expense' => __('Expense', 'bizmanage-pro'),
        );
    }

    /**
     * Get payment methods
     * @return array
     */
    public function get_payment_methods() {
        return array(
            'cash' => __('Cash', 'bizmanage-pro'),
            'check' => __('Check', 'bizmanage-pro'),
            'credit_card' => __('Credit Card', 'bizmanage-pro'),
            'debit_card' => __('Debit Card', 'bizmanage-pro'),
            'bank_transfer' => __('Bank Transfer', 'bizmanage-pro'),
            'paypal' => __('PayPal', 'bizmanage-pro'),
            'other' => __('Other', 'bizmanage-pro'),
        );
    }

    /**
     * Get document categories
     * @return array
     */
    public function get_document_categories() {
        return array(
            'invoice' => __('Invoice', 'bizmanage-pro'),
            'receipt' => __('Receipt', 'bizmanage-pro'),
            'contract' => __('Contract', 'bizmanage-pro'),
            'tax_document' => __('Tax Document', 'bizmanage-pro'),
            'bank_statement' => __('Bank Statement', 'bizmanage-pro'),
            'legal_document' => __('Legal Document', 'bizmanage-pro'),
            'other' => __('Other', 'bizmanage-pro'),
        );
    }

    /**
     * Get recurring frequencies
     * @return array
     */
    public function get_recurring_frequencies() {
        return array(
            'daily' => __('Daily', 'bizmanage-pro'),
            'weekly' => __('Weekly', 'bizmanage-pro'),
            'monthly' => __('Monthly', 'bizmanage-pro'),
            'quarterly' => __('Quarterly', 'bizmanage-pro'),
            'yearly' => __('Yearly', 'bizmanage-pro'),
        );
    }

    /**
     * Debug log
     * @param mixed $data
     * @param string $title
     */
    public function debug_log($data, $title = 'BizManage Pro Debug') {
        if (WP_DEBUG && WP_DEBUG_LOG) {
            error_log($title . ': ' . print_r($data, true));
        }
    }
}

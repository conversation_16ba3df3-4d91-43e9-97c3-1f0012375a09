<?php
/**
 * BizManage Pro Dashboard Template
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current user's entities
$admin = BizManage_Pro_Admin::instance();
$entities = $admin->get_user_entities_dropdown();
$selected_entity_id = isset($_GET['entity_id']) ? intval($_GET['entity_id']) : 0;

// If no entity selected and user has entities, select the first one
if (!$selected_entity_id && !empty($entities)) {
    $selected_entity_id = array_keys($entities)[0];
}
?>

<div class="wrap bizmanage-admin-wrap">
    <div class="bizmanage-header">
        <h1><?php _e('BizManage Pro Dashboard', 'bizmanage-pro'); ?></h1>
        <p><?php _e('Overview of your business financial data and key metrics.', 'bizmanage-pro'); ?></p>
    </div>

    <?php if (empty($entities)): ?>
        <div class="notice notice-warning">
            <p>
                <?php _e('No business entities found. Please create a business entity first.', 'bizmanage-pro'); ?>
                <a href="<?php echo admin_url('admin.php?page=bizmanage-entities'); ?>" class="button button-primary">
                    <?php _e('Create Business Entity', 'bizmanage-pro'); ?>
                </a>
            </p>
        </div>
    <?php else: ?>
        
        <!-- Entity Selector -->
        <div class="bizmanage-entity-selector">
            <label for="bizmanage-entity-select"><?php _e('Select Business Entity:', 'bizmanage-pro'); ?></label>
            <select id="bizmanage-entity-select" name="entity_id" class="form-select">
                <?php foreach ($entities as $id => $name): ?>
                    <option value="<?php echo esc_attr($id); ?>" <?php selected($selected_entity_id, $id); ?>>
                        <?php echo esc_html($name); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            
            <div class="dashboard-controls" style="margin-top: 10px;">
                <label for="dashboard-period-select"><?php _e('Period:', 'bizmanage-pro'); ?></label>
                <select id="dashboard-period-select" class="form-select" style="width: auto; display: inline-block; margin-left: 10px;">
                    <option value="week"><?php _e('Last 7 Days', 'bizmanage-pro'); ?></option>
                    <option value="month" selected><?php _e('Last 30 Days', 'bizmanage-pro'); ?></option>
                    <option value="quarter"><?php _e('Last 90 Days', 'bizmanage-pro'); ?></option>
                    <option value="year"><?php _e('Last Year', 'bizmanage-pro'); ?></option>
                </select>
                
                <button type="button" class="button bizmanage-refresh-dashboard" style="margin-left: 10px;">
                    <?php _e('Refresh', 'bizmanage-pro'); ?>
                </button>
            </div>
        </div>

        <!-- Dashboard Content -->
        <div id="bizmanage-dashboard">
            
            <!-- Summary Cards -->
            <div class="bizmanage-dashboard-cards">
                <div class="bizmanage-card">
                    <h3><?php _e('Total Income', 'bizmanage-pro'); ?></h3>
                    <div class="card-value positive" id="income-total">$0.00</div>
                    <div class="card-description"><?php _e('Revenue for selected period', 'bizmanage-pro'); ?></div>
                </div>
                
                <div class="bizmanage-card">
                    <h3><?php _e('Total Expenses', 'bizmanage-pro'); ?></h3>
                    <div class="card-value negative" id="expense-total">$0.00</div>
                    <div class="card-description"><?php _e('Expenses for selected period', 'bizmanage-pro'); ?></div>
                </div>
                
                <div class="bizmanage-card">
                    <h3><?php _e('Net Profit', 'bizmanage-pro'); ?></h3>
                    <div class="card-value" id="profit-total">$0.00</div>
                    <div class="card-description"><?php _e('Income minus expenses', 'bizmanage-pro'); ?></div>
                </div>
                
                <div class="bizmanage-card">
                    <h3><?php _e('Transactions', 'bizmanage-pro'); ?></h3>
                    <div class="card-value" id="transaction-count">0</div>
                    <div class="card-description"><?php _e('Total transactions', 'bizmanage-pro'); ?></div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                <div class="bizmanage-chart-container">
                    <h3><?php _e('Income vs Expenses', 'bizmanage-pro'); ?></h3>
                    <canvas id="income-expense-chart" width="400" height="200"></canvas>
                </div>
                
                <div class="bizmanage-chart-container">
                    <h3><?php _e('Expense Categories', 'bizmanage-pro'); ?></h3>
                    <canvas id="expense-categories-chart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="bizmanage-table-wrapper">
                <h3 style="padding: 15px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                    <?php _e('Recent Transactions', 'bizmanage-pro'); ?>
                </h3>
                <table class="bizmanage-table">
                    <thead>
                        <tr>
                            <th><?php _e('Date', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Description', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Category', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Type', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Amount', 'bizmanage-pro'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="recent-transactions">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px;">
                                <?php _e('Loading transactions...', 'bizmanage-pro'); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Quick Actions -->
            <div class="bizmanage-quick-actions" style="margin-top: 30px;">
                <h3><?php _e('Quick Actions', 'bizmanage-pro'); ?></h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    
                    <?php if (current_user_can('bizmanage_manage_finances')): ?>
                    <a href="<?php echo admin_url('admin.php?page=bizmanage-finances&action=add-income'); ?>" class="bizmanage-btn bizmanage-btn-success">
                        <?php _e('Add Income', 'bizmanage-pro'); ?>
                    </a>
                    
                    <a href="<?php echo admin_url('admin.php?page=bizmanage-finances&action=add-expense'); ?>" class="bizmanage-btn bizmanage-btn-danger">
                        <?php _e('Add Expense', 'bizmanage-pro'); ?>
                    </a>
                    <?php endif; ?>
                    
                    <?php if (current_user_can('bizmanage_upload_documents')): ?>
                    <a href="<?php echo admin_url('admin.php?page=bizmanage-documents&action=upload'); ?>" class="bizmanage-btn bizmanage-btn-primary">
                        <?php _e('Upload Document', 'bizmanage-pro'); ?>
                    </a>
                    <?php endif; ?>
                    
                    <?php if (current_user_can('bizmanage_view_reports')): ?>
                    <a href="<?php echo admin_url('admin.php?page=bizmanage-reports'); ?>" class="bizmanage-btn bizmanage-btn-secondary">
                        <?php _e('View Reports', 'bizmanage-pro'); ?>
                    </a>
                    <?php endif; ?>
                    
                </div>
            </div>

        </div>

    <?php endif; ?>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Load dashboard data on page load
    if ($('#bizmanage-dashboard').length) {
        BizManagePro.loadDashboardData();
    }
    
    // Refresh dashboard when period changes
    $('#dashboard-period-select').on('change', function() {
        BizManagePro.loadDashboardData();
    });
});
</script>

<?php
/**
 * Check WordPress Debug Log
 * 
 * This script will check the WordPress debug log for BizManage Pro related errors
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>WordPress Debug Log Check</h1>';

// Check if debug is enabled
echo '<h2>Debug Configuration</h2>';
echo '<p><strong>WP_DEBUG:</strong> ' . (defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled') . '</p>';
echo '<p><strong>WP_DEBUG_LOG:</strong> ' . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled') . '</p>';
echo '<p><strong>WP_DEBUG_DISPLAY:</strong> ' . (defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY ? 'Enabled' : 'Disabled') . '</p>';
echo '<p><strong>SCRIPT_DEBUG:</strong> ' . (defined('SCRIPT_DEBUG') && SCRIPT_DEBUG ? 'Enabled' : 'Disabled') . '</p>';

// Check debug log file locations
$possible_log_files = array(
    WP_CONTENT_DIR . '/debug.log',
    ABSPATH . 'wp-content/debug.log',
    ABSPATH . 'debug.log',
    ini_get('error_log')
);

echo '<h2>Debug Log File Locations</h2>';
$log_file = null;

foreach ($possible_log_files as $file) {
    if ($file && file_exists($file)) {
        echo '<p style="color: green;"><strong>Found:</strong> ' . $file . ' (Size: ' . filesize($file) . ' bytes)</p>';
        if (!$log_file) {
            $log_file = $file; // Use the first found log file
        }
    } else {
        echo '<p style="color: red;"><strong>Not found:</strong> ' . ($file ?: 'Not set') . '</p>';
    }
}

if (!$log_file) {
    echo '<h3 style="color: red;">No debug log file found!</h3>';
    echo '<p>Debug logging might not be working. Try creating a test error:</p>';
    
    // Create a test error
    error_log('BizManagePro: Test error log entry - ' . date('Y-m-d H:i:s'));
    
    echo '<p>Test error logged. Refresh this page to see if it appears.</p>';
    echo '<p>If still no log file, check your wp-config.php file and server error logs.</p>';
} else {
    echo '<h2>Recent Log Entries (Last 50 lines)</h2>';
    
    // Read the log file
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -50);
    
    echo '<div style="background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;">';
    foreach ($recent_lines as $line) {
        if (trim($line)) {
            $color = 'black';
            if (strpos($line, 'BizManagePro') !== false) {
                $color = 'blue';
            } elseif (strpos($line, 'Fatal error') !== false || strpos($line, 'PHP Fatal error') !== false) {
                $color = 'red';
            } elseif (strpos($line, 'Warning') !== false || strpos($line, 'PHP Warning') !== false) {
                $color = 'orange';
            } elseif (strpos($line, 'Notice') !== false || strpos($line, 'PHP Notice') !== false) {
                $color = 'gray';
            }
            
            echo '<div style="color: ' . $color . '; margin-bottom: 2px;">' . htmlspecialchars($line) . '</div>';
        }
    }
    echo '</div>';
    
    // Filter BizManage Pro specific errors
    echo '<h2>BizManage Pro Specific Errors</h2>';
    $bizmanage_errors = array();
    
    foreach ($lines as $line) {
        if (strpos($line, 'BizManagePro') !== false || 
            strpos($line, 'bizmanage') !== false || 
            strpos($line, 'BizManage') !== false) {
            $bizmanage_errors[] = $line;
        }
    }
    
    if (!empty($bizmanage_errors)) {
        echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">';
        foreach (array_slice($bizmanage_errors, -20) as $error) {
            echo '<div style="margin-bottom: 2px;">' . htmlspecialchars($error) . '</div>';
        }
        echo '</div>';
        echo '<p><strong>Total BizManage Pro related log entries:</strong> ' . count($bizmanage_errors) . '</p>';
    } else {
        echo '<p style="color: green;">No BizManage Pro specific errors found in the log.</p>';
    }
}

// Test error logging
echo '<h2>Test Error Logging</h2>';
echo '<form method="post">';
echo '<button type="submit" name="test_error" value="1">Create Test Error Log Entry</button>';
echo '</form>';

if (isset($_POST['test_error'])) {
    error_log('BizManagePro: Manual test error created at ' . date('Y-m-d H:i:s') . ' by user ID: ' . get_current_user_id());
    echo '<p style="color: green;">Test error logged! Refresh the page to see it.</p>';
}

// Plugin status
echo '<h2>Plugin Status</h2>';
$plugin_file = 'bizmanage-pro/bizmanage-pro.php';
echo '<p><strong>Plugin File:</strong> ' . $plugin_file . '</p>';
echo '<p><strong>Plugin Active:</strong> ' . (is_plugin_active($plugin_file) ? 'YES' : 'NO') . '</p>';

if (is_plugin_active($plugin_file)) {
    echo '<p><strong>Plugin Path:</strong> ' . WP_PLUGIN_DIR . '/' . $plugin_file . '</p>';
    echo '<p><strong>Plugin File Exists:</strong> ' . (file_exists(WP_PLUGIN_DIR . '/' . $plugin_file) ? 'YES' : 'NO') . '</p>';
}

// Current user capabilities
echo '<h2>Current User Capabilities</h2>';
$user = wp_get_current_user();
$bizmanage_caps = array();

foreach ($user->allcaps as $cap => $has_cap) {
    if (strpos($cap, 'bizmanage') !== false && $has_cap) {
        $bizmanage_caps[] = $cap;
    }
}

if (!empty($bizmanage_caps)) {
    echo '<ul>';
    foreach ($bizmanage_caps as $cap) {
        echo '<li>' . $cap . '</li>';
    }
    echo '</ul>';
} else {
    echo '<p style="color: red;">No BizManage Pro capabilities found for current user.</p>';
}

// Quick actions
echo '<h2>Quick Actions</h2>';
echo '<p><a href="install-plugin.php">Install/Reinstall Plugin</a></p>';
echo '<p><a href="setup-database.php">Setup Database</a></p>';
echo '<p><a href="debug-database-check.php">Check Database</a></p>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">Go to BizManage Pro Dashboard</a></p>';
echo '<p><a href="' . admin_url('plugins.php') . '">WordPress Plugins Page</a></p>';

// Auto-refresh option
echo '<h2>Auto Refresh</h2>';
echo '<p><a href="?auto_refresh=1">Enable Auto Refresh (10 seconds)</a> | <a href="?">Disable Auto Refresh</a></p>';

if (isset($_GET['auto_refresh'])) {
    echo '<meta http-equiv="refresh" content="10">';
    echo '<p style="color: blue;">Page will auto-refresh every 10 seconds...</p>';
}
?>

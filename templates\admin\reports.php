<?php
/**
 * BizManage Pro Reports Template
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get instances
$reports_manager = BizManage_Pro_Reports::instance();
$admin = BizManage_Pro_Admin::instance();
$security = BizManage_Pro_Security::instance();

// Get current entity
$entities = $admin->get_user_entities_dropdown();
$selected_entity_id = isset($_GET['entity_id']) ? intval($_GET['entity_id']) : 0;

if (!$selected_entity_id && !empty($entities)) {
    $selected_entity_id = array_keys($entities)[0];
}

// Available report types
$report_types = array(
    'income_statement' => __('Income Statement (Profit & Loss)', 'bizmanage-pro'),
    'balance_sheet' => __('Balance Sheet', 'bizmanage-pro'),
    'cash_flow' => __('Cash Flow Statement', 'bizmanage-pro'),
    'tax_summary' => __('Tax Summary Report', 'bizmanage-pro'),
);
?>

<div class="wrap bizmanage-admin-wrap">
    <div class="bizmanage-header">
        <h1><?php _e('Financial Reports', 'bizmanage-pro'); ?></h1>
        <p><?php _e('Generate comprehensive financial reports for your business analysis and compliance.', 'bizmanage-pro'); ?></p>
    </div>

    <?php if (empty($entities)): ?>
        <div class="notice notice-warning">
            <p>
                <?php _e('No business entities found. Please create a business entity first.', 'bizmanage-pro'); ?>
                <a href="<?php echo admin_url('admin.php?page=bizmanage-entities'); ?>" class="button button-primary">
                    <?php _e('Create Business Entity', 'bizmanage-pro'); ?>
                </a>
            </p>
        </div>
    <?php else: ?>

        <!-- Entity Selector -->
        <div class="bizmanage-entity-selector">
            <label for="bizmanage-entity-select"><?php _e('Select Business Entity:', 'bizmanage-pro'); ?></label>
            <select id="bizmanage-entity-select" name="entity_id" class="form-select">
                <?php foreach ($entities as $id => $name): ?>
                    <option value="<?php echo esc_attr($id); ?>" <?php selected($selected_entity_id, $id); ?>>
                        <?php echo esc_html($name); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <!-- Report Generation Form -->
        <div class="bizmanage-form">
            <h3><?php _e('Generate Report', 'bizmanage-pro'); ?></h3>
            
            <form id="report-generation-form">
                <?php wp_nonce_field('bizmanage_generate_report', 'report_nonce'); ?>
                <input type="hidden" name="entity_id" id="report-entity-id" value="<?php echo esc_attr($selected_entity_id); ?>">

                <div class="form-row">
                    <div class="form-group">
                        <label for="report_type"><?php _e('Report Type', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                        <select id="report_type" name="report_type" required>
                            <option value=""><?php _e('Select Report Type', 'bizmanage-pro'); ?></option>
                            <?php foreach ($report_types as $key => $label): ?>
                                <option value="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="date_range"><?php _e('Date Range', 'bizmanage-pro'); ?></label>
                        <select id="date_range" name="date_range">
                            <option value="custom"><?php _e('Custom Range', 'bizmanage-pro'); ?></option>
                            <option value="current_month"><?php _e('Current Month', 'bizmanage-pro'); ?></option>
                            <option value="last_month"><?php _e('Last Month', 'bizmanage-pro'); ?></option>
                            <option value="current_quarter"><?php _e('Current Quarter', 'bizmanage-pro'); ?></option>
                            <option value="last_quarter"><?php _e('Last Quarter', 'bizmanage-pro'); ?></option>
                            <option value="current_year"><?php _e('Current Year', 'bizmanage-pro'); ?></option>
                            <option value="last_year"><?php _e('Last Year', 'bizmanage-pro'); ?></option>
                        </select>
                    </div>
                </div>

                <div class="form-row" id="custom-date-range">
                    <div class="form-group">
                        <label for="start_date"><?php _e('Start Date', 'bizmanage-pro'); ?></label>
                        <input type="date" id="start_date" name="start_date" value="<?php echo date('Y-m-01'); ?>">
                    </div>

                    <div class="form-group">
                        <label for="end_date"><?php _e('End Date', 'bizmanage-pro'); ?></label>
                        <input type="date" id="end_date" name="end_date" value="<?php echo date('Y-m-t'); ?>">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" id="generate-report" class="bizmanage-btn bizmanage-btn-primary">
                        <?php _e('Generate Report', 'bizmanage-pro'); ?>
                    </button>
                    
                    <button type="button" id="export-pdf" class="bizmanage-btn bizmanage-btn-secondary" disabled>
                        <?php _e('Export PDF', 'bizmanage-pro'); ?>
                    </button>
                    
                    <button type="button" id="export-excel" class="bizmanage-btn bizmanage-btn-secondary" disabled>
                        <?php _e('Export Excel', 'bizmanage-pro'); ?>
                    </button>
                </div>
            </form>
        </div>

        <!-- Report Display Area -->
        <div id="report-display" class="bizmanage-report-display" style="display: none;">
            <div class="bizmanage-report-header">
                <h3 id="report-title"></h3>
                <div class="report-meta">
                    <span id="report-entity"></span>
                    <span id="report-period"></span>
                    <span id="report-generated"></span>
                </div>
            </div>
            
            <div id="report-content" class="bizmanage-report-content">
                <!-- Report content will be loaded here -->
            </div>
        </div>

        <!-- Quick Report Links -->
        <div class="bizmanage-quick-reports" style="margin-top: 30px;">
            <h3><?php _e('Quick Reports', 'bizmanage-pro'); ?></h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                
                <div class="bizmanage-card quick-report-card" data-report-type="income_statement">
                    <h4><?php _e('Income Statement', 'bizmanage-pro'); ?></h4>
                    <p><?php _e('View your profit and loss for the current month', 'bizmanage-pro'); ?></p>
                    <button type="button" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-primary quick-report-btn">
                        <?php _e('Generate', 'bizmanage-pro'); ?>
                    </button>
                </div>
                
                <div class="bizmanage-card quick-report-card" data-report-type="balance_sheet">
                    <h4><?php _e('Balance Sheet', 'bizmanage-pro'); ?></h4>
                    <p><?php _e('View your assets, liabilities, and equity', 'bizmanage-pro'); ?></p>
                    <button type="button" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-primary quick-report-btn">
                        <?php _e('Generate', 'bizmanage-pro'); ?>
                    </button>
                </div>
                
                <div class="bizmanage-card quick-report-card" data-report-type="cash_flow">
                    <h4><?php _e('Cash Flow', 'bizmanage-pro'); ?></h4>
                    <p><?php _e('Track cash inflows and outflows', 'bizmanage-pro'); ?></p>
                    <button type="button" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-primary quick-report-btn">
                        <?php _e('Generate', 'bizmanage-pro'); ?>
                    </button>
                </div>
                
                <div class="bizmanage-card quick-report-card" data-report-type="tax_summary">
                    <h4><?php _e('Tax Summary', 'bizmanage-pro'); ?></h4>
                    <p><?php _e('Summary of tax-related transactions', 'bizmanage-pro'); ?></p>
                    <button type="button" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-primary quick-report-btn">
                        <?php _e('Generate', 'bizmanage-pro'); ?>
                    </button>
                </div>
                
            </div>
        </div>

    <?php endif; ?>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    var currentReportData = null;
    
    // Entity change handler
    $('#bizmanage-entity-select').on('change', function() {
        var entityId = $(this).val();
        $('#report-entity-id').val(entityId);
        var currentUrl = new URL(window.location);
        currentUrl.searchParams.set('entity_id', entityId);
        window.location.href = currentUrl.toString();
    });
    
    // Date range change handler
    $('#date_range').on('change', function() {
        var range = $(this).val();
        
        if (range === 'custom') {
            $('#custom-date-range').show();
        } else {
            $('#custom-date-range').hide();
            setDateRange(range);
        }
    });
    
    // Generate report handler
    $('#generate-report').on('click', function() {
        generateReport();
    });
    
    // Quick report handlers
    $('.quick-report-btn').on('click', function() {
        var reportType = $(this).closest('.quick-report-card').data('report-type');
        $('#report_type').val(reportType);
        $('#date_range').val('current_month').trigger('change');
        generateReport();
    });
    
    // Export handlers
    $('#export-pdf').on('click', function() {
        exportReport('pdf');
    });
    
    $('#export-excel').on('click', function() {
        exportReport('excel');
    });
    
    function setDateRange(range) {
        var startDate, endDate;
        var today = new Date();
        
        switch (range) {
            case 'current_month':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                break;
            case 'last_month':
                startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                break;
            case 'current_quarter':
                var quarter = Math.floor(today.getMonth() / 3);
                startDate = new Date(today.getFullYear(), quarter * 3, 1);
                endDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
                break;
            case 'last_quarter':
                var quarter = Math.floor(today.getMonth() / 3) - 1;
                if (quarter < 0) {
                    quarter = 3;
                    startDate = new Date(today.getFullYear() - 1, quarter * 3, 1);
                    endDate = new Date(today.getFullYear(), 0, 0);
                } else {
                    startDate = new Date(today.getFullYear(), quarter * 3, 1);
                    endDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
                }
                break;
            case 'current_year':
                startDate = new Date(today.getFullYear(), 0, 1);
                endDate = new Date(today.getFullYear(), 11, 31);
                break;
            case 'last_year':
                startDate = new Date(today.getFullYear() - 1, 0, 1);
                endDate = new Date(today.getFullYear() - 1, 11, 31);
                break;
        }
        
        if (startDate && endDate) {
            $('#start_date').val(formatDate(startDate));
            $('#end_date').val(formatDate(endDate));
        }
    }
    
    function formatDate(date) {
        return date.getFullYear() + '-' + 
               String(date.getMonth() + 1).padStart(2, '0') + '-' + 
               String(date.getDate()).padStart(2, '0');
    }
    
    function generateReport() {
        var reportType = $('#report_type').val();
        var entityId = $('#report-entity-id').val();
        var startDate = $('#start_date').val();
        var endDate = $('#end_date').val();
        
        if (!reportType) {
            BizManagePro.showAlert('<?php _e("Please select a report type.", "bizmanage-pro"); ?>', 'error');
            return;
        }
        
        BizManagePro.ajaxRequest('bizmanage_generate_report', {
            report_type: reportType,
            entity_id: entityId,
            start_date: startDate,
            end_date: endDate
        }, function(response) {
            if (response.success) {
                currentReportData = response.data;
                displayReport(response.data);
                $('#export-pdf, #export-excel').prop('disabled', false);
            } else {
                BizManagePro.showAlert(response.message || '<?php _e("Error generating report.", "bizmanage-pro"); ?>', 'error');
            }
        });
    }
    
    function displayReport(reportData) {
        var reportTypeNames = {
            'income_statement': '<?php _e("Income Statement", "bizmanage-pro"); ?>',
            'balance_sheet': '<?php _e("Balance Sheet", "bizmanage-pro"); ?>',
            'cash_flow': '<?php _e("Cash Flow Statement", "bizmanage-pro"); ?>',
            'tax_summary': '<?php _e("Tax Summary", "bizmanage-pro"); ?>'
        };
        
        $('#report-title').text(reportTypeNames[reportData.report_type] || reportData.report_type);
        
        var entityName = $('#bizmanage-entity-select option:selected').text();
        $('#report-entity').text('<?php _e("Entity:", "bizmanage-pro"); ?> ' + entityName);
        
        if (reportData.start_date && reportData.end_date) {
            $('#report-period').text('<?php _e("Period:", "bizmanage-pro"); ?> ' + reportData.start_date + ' to ' + reportData.end_date);
        } else if (reportData.as_of_date) {
            $('#report-period').text('<?php _e("As of:", "bizmanage-pro"); ?> ' + reportData.as_of_date);
        }
        
        $('#report-generated').text('<?php _e("Generated:", "bizmanage-pro"); ?> ' + new Date().toLocaleString());
        
        var content = generateReportContent(reportData);
        $('#report-content').html(content);
        $('#report-display').show();
        
        // Scroll to report
        $('html, body').animate({
            scrollTop: $('#report-display').offset().top
        }, 500);
    }
    
    function generateReportContent(reportData) {
        switch (reportData.report_type) {
            case 'income_statement':
                return generateIncomeStatementContent(reportData);
            case 'balance_sheet':
                return generateBalanceSheetContent(reportData);
            case 'cash_flow':
                return generateCashFlowContent(reportData);
            case 'tax_summary':
                return generateTaxSummaryContent(reportData);
            default:
                return '<p><?php _e("Report content not available.", "bizmanage-pro"); ?></p>';
        }
    }
    
    function generateIncomeStatementContent(data) {
        var html = '<table class="bizmanage-table">';
        html += '<thead><tr><th><?php _e("Category", "bizmanage-pro"); ?></th><th style="text-align: right;"><?php _e("Amount", "bizmanage-pro"); ?></th></tr></thead>';
        html += '<tbody>';
        
        // Income section
        html += '<tr class="section-header"><td colspan="2"><strong><?php _e("INCOME", "bizmanage-pro"); ?></strong></td></tr>';
        if (data.income_categories && data.income_categories.length > 0) {
            data.income_categories.forEach(function(category) {
                html += '<tr><td>' + category.category + '</td><td style="text-align: right;">' + BizManagePro.formatCurrency(category.total) + '</td></tr>';
            });
        } else {
            html += '<tr><td colspan="2" style="text-align: center; font-style: italic;"><?php _e("No income transactions found", "bizmanage-pro"); ?></td></tr>';
        }
        html += '<tr class="total-row"><td><strong><?php _e("Total Income", "bizmanage-pro"); ?></strong></td><td style="text-align: right;"><strong>' + BizManagePro.formatCurrency(data.total_income || 0) + '</strong></td></tr>';
        
        // Expenses section
        html += '<tr class="section-header"><td colspan="2"><strong><?php _e("EXPENSES", "bizmanage-pro"); ?></strong></td></tr>';
        if (data.expense_categories && data.expense_categories.length > 0) {
            data.expense_categories.forEach(function(category) {
                html += '<tr><td>' + category.category + '</td><td style="text-align: right;">' + BizManagePro.formatCurrency(category.total) + '</td></tr>';
            });
        } else {
            html += '<tr><td colspan="2" style="text-align: center; font-style: italic;"><?php _e("No expense transactions found", "bizmanage-pro"); ?></td></tr>';
        }
        html += '<tr class="total-row"><td><strong><?php _e("Total Expenses", "bizmanage-pro"); ?></strong></td><td style="text-align: right;"><strong>' + BizManagePro.formatCurrency(data.total_expenses || 0) + '</strong></td></tr>';
        
        // Net profit
        var profitClass = (data.net_profit || 0) >= 0 ? 'positive' : 'negative';
        html += '<tr class="net-profit-row"><td><strong><?php _e("Net Profit", "bizmanage-pro"); ?></strong></td><td style="text-align: right;" class="' + profitClass + '"><strong>' + BizManagePro.formatCurrency(data.net_profit || 0) + '</strong></td></tr>';
        
        html += '</tbody></table>';
        return html;
    }
    
    function generateBalanceSheetContent(data) {
        return '<div class="report-placeholder"><p><?php _e("Balance Sheet report is under development. This feature will be available in a future update.", "bizmanage-pro"); ?></p></div>';
    }
    
    function generateCashFlowContent(data) {
        return '<div class="report-placeholder"><p><?php _e("Cash Flow Statement is under development. This feature will be available in a future update.", "bizmanage-pro"); ?></p></div>';
    }
    
    function generateTaxSummaryContent(data) {
        return '<div class="report-placeholder"><p><?php _e("Tax Summary report is under development. This feature will be available in a future update.", "bizmanage-pro"); ?></p></div>';
    }
    
    function exportReport(format) {
        if (!currentReportData) {
            BizManagePro.showAlert('<?php _e("No report data to export.", "bizmanage-pro"); ?>', 'error');
            return;
        }
        
        BizManagePro.ajaxRequest('bizmanage_export_report', {
            report_data: JSON.stringify(currentReportData),
            export_format: format
        }, function(response) {
            if (response.success && response.data.download_url) {
                // Create temporary download link
                var link = document.createElement('a');
                link.href = response.data.download_url;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                BizManagePro.showAlert('<?php _e("Report exported successfully.", "bizmanage-pro"); ?>', 'success');
            } else {
                BizManagePro.showAlert(response.message || '<?php _e("Error exporting report.", "bizmanage-pro"); ?>', 'error');
            }
        });
    }
    
    // Initialize with current month
    $('#date_range').val('current_month').trigger('change');
});
</script>

<style>
.bizmanage-report-display {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 30px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.bizmanage-report-header {
    border-bottom: 2px solid #007cba;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.bizmanage-report-header h3 {
    margin: 0 0 10px 0;
    color: #23282d;
    font-size: 24px;
}

.report-meta {
    color: #666;
    font-size: 14px;
}

.report-meta span {
    margin-right: 20px;
}

.section-header td {
    background-color: #f8f9fa !important;
    font-weight: 600;
    color: #495057;
}

.total-row td {
    background-color: #e9ecef !important;
    font-weight: 600;
}

.net-profit-row td {
    background-color: #007cba !important;
    color: white !important;
    font-weight: 600;
}

.quick-report-card {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.quick-report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,.15);
}

.quick-report-card h4 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.quick-report-card p {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
}

.report-placeholder {
    text-align: center;
    padding: 40px;
    background-color: #f8f9fa;
    border-radius: 4px;
    color: #6c757d;
}

.positive {
    color: #28a745 !important;
}

.negative {
    color: #dc3545 !important;
}

#custom-date-range {
    display: none;
}
</style>

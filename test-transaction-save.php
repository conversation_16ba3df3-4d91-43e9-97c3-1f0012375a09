<?php
/**
 * Test Transaction Save Functionality
 * 
 * This script will test transaction saving and category management
 */

// Find WordPress root directory
$wp_root = dirname(__FILE__);
$max_depth = 5;
$depth = 0;

while ($depth < $max_depth) {
    if (file_exists($wp_root . '/wp-config.php') && file_exists($wp_root . '/wp-load.php')) {
        break;
    }
    $wp_root = dirname($wp_root);
    $depth++;
}

if (!file_exists($wp_root . '/wp-load.php')) {
    die('WordPress installation not found.');
}

// WordPress environment
define('WP_USE_THEMES', false);
require_once($wp_root . '/wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Test Transaction Save & Category Management</h1>';

// Check required tables and data
echo '<h2>1. Prerequisites Check</h2>';
global $wpdb;

$entities_table = $wpdb->prefix . 'bizmanage_business_entities';
$categories_table = $wpdb->prefix . 'bizmanage_categories';
$transactions_table = $wpdb->prefix . 'bizmanage_transactions';

// Check entities
$entity = $wpdb->get_row("SELECT * FROM $entities_table WHERE status = 'active' LIMIT 1");
if (!$entity) {
    echo '<p style="color: red;">❌ No active business entity found</p>';
    exit;
}
echo '<p style="color: green;">✅ Entity: ' . $entity->business_name . ' (ID: ' . $entity->id . ')</p>';

// Check categories
$categories = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM $categories_table WHERE entity_id = %d AND status = 'active'",
    $entity->id
));
echo '<p style="color: green;">✅ Categories: ' . count($categories) . ' found</p>';

if (count($categories) < 2) {
    echo '<p style="color: orange;">⚠️ Creating test categories...</p>';
    
    $test_categories = array(
        array('name' => 'Sales Revenue', 'type' => 'income', 'color' => '#28a745'),
        array('name' => 'Office Supplies', 'type' => 'expense', 'color' => '#dc3545'),
    );
    
    foreach ($test_categories as $cat_data) {
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $categories_table WHERE entity_id = %d AND name = %s",
            $entity->id, $cat_data['name']
        ));
        
        if ($exists == 0) {
            $wpdb->insert(
                $categories_table,
                array(
                    'entity_id' => $entity->id,
                    'name' => $cat_data['name'],
                    'type' => $cat_data['type'],
                    'color' => $cat_data['color'],
                    'status' => 'active',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                )
            );
            echo '<p style="color: green;">✅ Created: ' . $cat_data['name'] . '</p>';
        }
    }
    
    // Refresh categories
    $categories = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $categories_table WHERE entity_id = %d AND status = 'active'",
        $entity->id
    ));
}

// Test transaction save
echo '<h2>2. Test Transaction Save</h2>';

if (isset($_POST['test_transaction_save'])) {
    $test_data = array(
        'entity_id' => $entity->id,
        'type' => sanitize_text_field($_POST['test_type']),
        'amount' => floatval($_POST['test_amount']),
        'category_id' => intval($_POST['test_category_id']),
        'description' => sanitize_text_field($_POST['test_description']),
        'transaction_date' => sanitize_text_field($_POST['test_date'])
    );
    
    // Simulate AJAX request
    $_POST = array(
        'action' => 'bizmanage_save_transaction',
        'nonce' => wp_create_nonce('bizmanage_save_transaction')
    );
    $_POST = array_merge($_POST, $test_data);
    
    echo '<h3>Testing Transaction Save</h3>';
    echo '<p><strong>Data:</strong></p>';
    echo '<pre>' . json_encode($test_data, JSON_PRETTY_PRINT) . '</pre>';
    
    // Check if transaction AJAX handler exists
    if (class_exists('BizManage_Pro_Transactions')) {
        $transactions_class = BizManage_Pro_Transactions::instance();
        
        if (method_exists($transactions_class, 'ajax_save_transaction')) {
            // Capture output
            ob_start();
            try {
                $transactions_class->ajax_save_transaction();
                $output = ob_get_clean();
                
                echo '<p style="color: green;">✅ AJAX method executed</p>';
                echo '<p><strong>Response:</strong></p>';
                echo '<pre>' . htmlspecialchars($output) . '</pre>';
                
                // Check if transaction was created
                $latest_transaction = $wpdb->get_row("SELECT * FROM $transactions_table ORDER BY id DESC LIMIT 1");
                if ($latest_transaction) {
                    echo '<p style="color: green;">✅ Transaction found in database!</p>';
                    echo '<pre>' . print_r($latest_transaction, true) . '</pre>';
                }
                
            } catch (Exception $e) {
                ob_end_clean();
                echo '<p style="color: red;">❌ Error: ' . $e->getMessage() . '</p>';
            }
        } else {
            echo '<p style="color: red;">❌ ajax_save_transaction method not found</p>';
        }
    } else {
        echo '<p style="color: red;">❌ BizManage_Pro_Transactions class not found</p>';
    }
}

// Test form
if (!empty($categories)) {
    echo '<form method="post">';
    echo '<table>';
    echo '<tr><td>Type:</td><td><select name="test_type" required>';
    echo '<option value="income">Income</option>';
    echo '<option value="expense">Expense</option>';
    echo '</select></td></tr>';
    echo '<tr><td>Amount:</td><td><input type="number" name="test_amount" value="100.00" step="0.01" required></td></tr>';
    echo '<tr><td>Category:</td><td><select name="test_category_id" required>';
    foreach ($categories as $cat) {
        echo '<option value="' . $cat->id . '">' . $cat->name . ' (' . $cat->type . ')</option>';
    }
    echo '</select></td></tr>';
    echo '<tr><td>Description:</td><td><input type="text" name="test_description" value="Test transaction ' . time() . '" required></td></tr>';
    echo '<tr><td>Date:</td><td><input type="date" name="test_date" value="' . date('Y-m-d') . '" required></td></tr>';
    echo '<tr><td colspan="2"><button type="submit" name="test_transaction_save" value="1">Test Transaction Save</button></td></tr>';
    echo '</table>';
    echo '</form>';
}

// JavaScript test
echo '<h2>3. JavaScript Transaction Save Test</h2>';
echo '<div id="js-transaction-result"></div>';

if (!empty($categories)) {
    $category = $categories[0];
    echo '<button onclick="testTransactionSaveJS()">Test Transaction Save via JavaScript</button>';
    
    echo '<script>
    function testTransactionSaveJS() {
        var data = {
            action: "bizmanage_save_transaction",
            nonce: "' . wp_create_nonce('bizmanage_save_transaction') . '",
            entity_id: ' . $entity->id . ',
            type: "' . $category->type . '",
            amount: 150.00,
            category_id: ' . $category->id . ',
            description: "JS Test Transaction " + Date.now(),
            transaction_date: "' . date('Y-m-d') . '"
        };
        
        console.log("Testing transaction save with data:", data);
        
        jQuery.post(ajaxurl, data, function(response) {
            console.log("Transaction save response:", response);
            document.getElementById("js-transaction-result").innerHTML = 
                "<h4>JavaScript Test Result:</h4><pre>" + JSON.stringify(response, null, 2) + "</pre>";
        }).fail(function(xhr, status, error) {
            console.log("Transaction save error:", error);
            document.getElementById("js-transaction-result").innerHTML = 
                "<h4 style=\"color: red;\">JavaScript Test Failed:</h4><pre>" + xhr.responseText + "</pre>";
        });
    }
    </script>';
}

// Check AJAX handlers
echo '<h2>4. AJAX Handlers Check</h2>';

$ajax_actions = array(
    'bizmanage_save_transaction',
    'bizmanage_get_transactions',
    'bizmanage_delete_transaction'
);

foreach ($ajax_actions as $action) {
    $has_action = has_action('wp_ajax_' . $action);
    echo '<p><strong>' . $action . ':</strong> ';
    if ($has_action) {
        echo '<span style="color: green;">✅ Registered</span>';
    } else {
        echo '<span style="color: red;">❌ Not registered</span>';
    }
    echo '</p>';
}

// Recent transactions
echo '<h2>5. Recent Transactions</h2>';
$recent_transactions = $wpdb->get_results("SELECT * FROM $transactions_table ORDER BY id DESC LIMIT 5");

if (!empty($recent_transactions)) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Entity ID</th><th>Type</th><th>Amount</th><th>Description</th><th>Date</th></tr>';
    foreach ($recent_transactions as $trans) {
        echo '<tr>';
        echo '<td>' . $trans->id . '</td>';
        echo '<td>' . $trans->entity_id . '</td>';
        echo '<td>' . $trans->type . '</td>';
        echo '<td>$' . number_format($trans->amount, 2) . '</td>';
        echo '<td>' . $trans->description . '</td>';
        echo '<td>' . $trans->transaction_date . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p>No transactions found.</p>';
}

// Instructions
echo '<h2>6. Next Steps</h2>';
echo '<ol>';
echo '<li><strong>Test transaction save</strong> using the form above</li>';
echo '<li><strong>Test JavaScript</strong> using the button above</li>';
echo '<li><strong>Go to BizManage Pro Finances</strong> and try saving a transaction</li>';
echo '<li><strong>Check browser console</strong> for detailed logs</li>';
echo '</ol>';

echo '<h2>7. Fixes Applied</h2>';
echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;">';
echo '<h4>✅ Transaction Save Fixes:</h4>';
echo '<ul>';
echo '<li>Added saveTransaction() JavaScript function</li>';
echo '<li>Added form validation for required fields</li>';
echo '<li>Added transaction save button click handler</li>';
echo '<li>Added success/error message handling</li>';
echo '<li>Added redirect after successful save</li>';
echo '</ul>';
echo '<h4>✅ Category Management Fixes:</h4>';
echo '<ul>';
echo '<li>Added "Add New" category button handler</li>';
echo '<li>Added "Manage Categories" button handler</li>';
echo '<li>Updated showCategoryModal to accept transaction type</li>';
echo '<li>Enhanced category modal functionality</li>';
echo '</ul>';
echo '</div>';

echo '<h2>8. Quick Links</h2>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances&action=add-income&entity_id=' . $entity->id) . '">Add Income Transaction</a></p>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances&action=add-expense&entity_id=' . $entity->id) . '">Add Expense Transaction</a></p>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances&entity_id=' . $entity->id) . '">View Transactions</a></p>';
?>

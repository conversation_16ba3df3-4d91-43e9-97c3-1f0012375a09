<?php
/**
 * BizManage Pro Installer Class
 *
 * Handles plugin installation, database table creation, and setup
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Installer Class
 */
class BizManage_Pro_Installer {

    /**
     * Create database tables
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Business entities table
        $table_business_entities = $wpdb->prefix . 'bizmanage_business_entities';
        $sql_business_entities = "CREATE TABLE $table_business_entities (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            entity_type varchar(50) NOT NULL DEFAULT 'sole_proprietorship',
            business_name varchar(255) NOT NULL,
            registration_number varchar(100) DEFAULT NULL,
            tax_id varchar(100) DEFAULT NULL,
            address text DEFAULT NULL,
            city varchar(100) DEFAULT NULL,
            state varchar(100) DEFAULT NULL,
            postal_code varchar(20) DEFAULT NULL,
            country varchar(100) DEFAULT NULL,
            phone varchar(20) DEFAULT NULL,
            email varchar(100) DEFAULT NULL,
            website varchar(255) DEFAULT NULL,
            fiscal_year_start date DEFAULT NULL,
            currency varchar(10) DEFAULT 'USD',
            timezone varchar(50) DEFAULT NULL,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY entity_type (entity_type),
            KEY status (status)
        ) $charset_collate;";

        // Documents table
        $table_documents = $wpdb->prefix . 'bizmanage_documents';
        $sql_documents = "CREATE TABLE $table_documents (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            entity_id bigint(20) unsigned NOT NULL,
            user_id bigint(20) unsigned NOT NULL,
            title varchar(255) NOT NULL,
            description text DEFAULT NULL,
            category varchar(100) NOT NULL,
            file_name varchar(255) NOT NULL,
            file_path varchar(500) NOT NULL,
            file_size bigint(20) unsigned DEFAULT 0,
            file_type varchar(50) NOT NULL,
            mime_type varchar(100) NOT NULL,
            encrypted tinyint(1) DEFAULT 1,
            encryption_key varchar(255) DEFAULT NULL,
            version varchar(20) DEFAULT '1.0',
            parent_id bigint(20) unsigned DEFAULT NULL,
            tags text DEFAULT NULL,
            upload_date datetime DEFAULT CURRENT_TIMESTAMP,
            modified_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            status varchar(20) DEFAULT 'active',
            PRIMARY KEY (id),
            KEY entity_id (entity_id),
            KEY user_id (user_id),
            KEY category (category),
            KEY parent_id (parent_id),
            KEY status (status),
            KEY upload_date (upload_date)
        ) $charset_collate;";

        // Financial transactions table
        $table_transactions = $wpdb->prefix . 'bizmanage_transactions';
        $sql_transactions = "CREATE TABLE $table_transactions (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            entity_id bigint(20) unsigned NOT NULL,
            user_id bigint(20) unsigned NOT NULL,
            transaction_type varchar(20) NOT NULL,
            category varchar(100) NOT NULL,
            subcategory varchar(100) DEFAULT NULL,
            amount decimal(15,2) NOT NULL,
            currency varchar(10) DEFAULT 'USD',
            exchange_rate decimal(10,4) DEFAULT 1.0000,
            base_amount decimal(15,2) NOT NULL,
            description text DEFAULT NULL,
            reference_number varchar(100) DEFAULT NULL,
            transaction_date date NOT NULL,
            due_date date DEFAULT NULL,
            payment_method varchar(50) DEFAULT NULL,
            bank_account varchar(100) DEFAULT NULL,
            tax_rate decimal(5,2) DEFAULT 0.00,
            tax_amount decimal(15,2) DEFAULT 0.00,
            recurring_id bigint(20) unsigned DEFAULT NULL,
            document_id bigint(20) unsigned DEFAULT NULL,
            reconciled tinyint(1) DEFAULT 0,
            reconciled_date datetime DEFAULT NULL,
            status varchar(20) DEFAULT 'pending',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY entity_id (entity_id),
            KEY user_id (user_id),
            KEY transaction_type (transaction_type),
            KEY category (category),
            KEY transaction_date (transaction_date),
            KEY recurring_id (recurring_id),
            KEY document_id (document_id),
            KEY status (status)
        ) $charset_collate;";

        // Recurring transactions table
        $table_recurring = $wpdb->prefix . 'bizmanage_recurring_transactions';
        $sql_recurring = "CREATE TABLE $table_recurring (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            entity_id bigint(20) unsigned NOT NULL,
            user_id bigint(20) unsigned NOT NULL,
            template_name varchar(255) NOT NULL,
            transaction_type varchar(20) NOT NULL,
            category varchar(100) NOT NULL,
            subcategory varchar(100) DEFAULT NULL,
            amount decimal(15,2) NOT NULL,
            currency varchar(10) DEFAULT 'USD',
            description text DEFAULT NULL,
            frequency varchar(20) NOT NULL,
            interval_value int(11) DEFAULT 1,
            start_date date NOT NULL,
            end_date date DEFAULT NULL,
            next_date date NOT NULL,
            last_generated datetime DEFAULT NULL,
            total_occurrences int(11) DEFAULT NULL,
            generated_count int(11) DEFAULT 0,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY entity_id (entity_id),
            KEY user_id (user_id),
            KEY next_date (next_date),
            KEY status (status)
        ) $charset_collate;";

        // Bank accounts table
        $table_bank_accounts = $wpdb->prefix . 'bizmanage_bank_accounts';
        $sql_bank_accounts = "CREATE TABLE $table_bank_accounts (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            entity_id bigint(20) unsigned NOT NULL,
            account_name varchar(255) NOT NULL,
            account_number varchar(100) NOT NULL,
            bank_name varchar(255) NOT NULL,
            account_type varchar(50) NOT NULL,
            currency varchar(10) DEFAULT 'USD',
            opening_balance decimal(15,2) DEFAULT 0.00,
            current_balance decimal(15,2) DEFAULT 0.00,
            last_reconciled_date date DEFAULT NULL,
            last_reconciled_balance decimal(15,2) DEFAULT NULL,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY entity_id (entity_id),
            KEY status (status)
        ) $charset_collate;";

        // Categories table
        $table_categories = $wpdb->prefix . 'bizmanage_categories';
        $sql_categories = "CREATE TABLE $table_categories (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            entity_id bigint(20) unsigned DEFAULT NULL,
            name varchar(255) NOT NULL,
            type varchar(20) NOT NULL,
            parent_id bigint(20) unsigned DEFAULT NULL,
            description text DEFAULT NULL,
            color varchar(7) DEFAULT NULL,
            is_default tinyint(1) DEFAULT 0,
            sort_order int(11) DEFAULT 0,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY entity_id (entity_id),
            KEY type (type),
            KEY parent_id (parent_id),
            KEY status (status)
        ) $charset_collate;";

        // Settings table
        $table_settings = $wpdb->prefix . 'bizmanage_settings';
        $sql_settings = "CREATE TABLE $table_settings (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            entity_id bigint(20) unsigned DEFAULT NULL,
            setting_key varchar(255) NOT NULL,
            setting_value longtext DEFAULT NULL,
            autoload varchar(20) DEFAULT 'yes',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY entity_setting (entity_id, setting_key),
            KEY setting_key (setting_key),
            KEY autoload (autoload)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($sql_business_entities);
        dbDelta($sql_documents);
        dbDelta($sql_transactions);
        dbDelta($sql_recurring);
        dbDelta($sql_bank_accounts);
        dbDelta($sql_categories);
        dbDelta($sql_settings);

        // Update database version
        update_option('bizmanage_pro_db_version', BIZMANAGE_PRO_DB_VERSION);

        // Insert default categories
        self::insert_default_categories();
    }

    /**
     * Insert default categories
     */
    private static function insert_default_categories() {
        global $wpdb;

        $table_categories = $wpdb->prefix . 'bizmanage_categories';

        $default_categories = array(
            // Income categories
            array('name' => 'Sales Revenue', 'type' => 'income', 'is_default' => 1, 'color' => '#28a745'),
            array('name' => 'Service Revenue', 'type' => 'income', 'is_default' => 1, 'color' => '#17a2b8'),
            array('name' => 'Investment Income', 'type' => 'income', 'is_default' => 1, 'color' => '#ffc107'),
            array('name' => 'Other Income', 'type' => 'income', 'is_default' => 1, 'color' => '#6c757d'),
            
            // Expense categories
            array('name' => 'Office Supplies', 'type' => 'expense', 'is_default' => 1, 'color' => '#dc3545'),
            array('name' => 'Utilities', 'type' => 'expense', 'is_default' => 1, 'color' => '#fd7e14'),
            array('name' => 'Travel & Transportation', 'type' => 'expense', 'is_default' => 1, 'color' => '#e83e8c'),
            array('name' => 'Marketing & Advertising', 'type' => 'expense', 'is_default' => 1, 'color' => '#6f42c1'),
            array('name' => 'Professional Services', 'type' => 'expense', 'is_default' => 1, 'color' => '#20c997'),
            array('name' => 'Insurance', 'type' => 'expense', 'is_default' => 1, 'color' => '#fd7e14'),
            array('name' => 'Rent & Lease', 'type' => 'expense', 'is_default' => 1, 'color' => '#6c757d'),
            array('name' => 'Equipment & Software', 'type' => 'expense', 'is_default' => 1, 'color' => '#007bff'),
        );

        foreach ($default_categories as $category) {
            $wpdb->insert(
                $table_categories,
                $category,
                array('%s', '%s', '%d', '%s')
            );
        }
    }

    /**
     * Create user roles and capabilities
     */
    public static function create_roles() {
        // Remove existing roles first
        self::remove_roles();

        // BizManage Administrator
        add_role('bizmanage_admin', __('BizManage Administrator', 'bizmanage-pro'), array(
            'read' => true,
            'bizmanage_manage_entities' => true,
            'bizmanage_manage_documents' => true,
            'bizmanage_manage_finances' => true,
            'bizmanage_view_reports' => true,
            'bizmanage_manage_users' => true,
            'bizmanage_manage_settings' => true,
            'bizmanage_export_data' => true,
            'bizmanage_import_data' => true,
        ));

        // BizManage Accountant
        add_role('bizmanage_accountant', __('BizManage Accountant', 'bizmanage-pro'), array(
            'read' => true,
            'bizmanage_manage_finances' => true,
            'bizmanage_view_reports' => true,
            'bizmanage_manage_documents' => true,
            'bizmanage_export_data' => true,
        ));

        // BizManage Manager
        add_role('bizmanage_manager', __('BizManage Manager', 'bizmanage-pro'), array(
            'read' => true,
            'bizmanage_view_reports' => true,
            'bizmanage_view_documents' => true,
            'bizmanage_limited_finances' => true,
        ));

        // BizManage Employee
        add_role('bizmanage_employee', __('BizManage Employee', 'bizmanage-pro'), array(
            'read' => true,
            'bizmanage_submit_expenses' => true,
            'bizmanage_view_own_data' => true,
        ));

        // BizManage Client
        add_role('bizmanage_client', __('BizManage Client', 'bizmanage-pro'), array(
            'read' => true,
            'bizmanage_view_client_reports' => true,
        ));

        // List of all BizManage Pro capabilities needed for full access
        $capabilities = array(
            // Entity management
            'bizmanage_manage_entities',
            'bizmanage_view_entities',

            // Document management
            'bizmanage_manage_documents',
            'bizmanage_view_documents',
            'bizmanage_upload_documents',
            'bizmanage_delete_documents',

            // Financial management
            'bizmanage_manage_finances',
            'bizmanage_view_finances',
            'bizmanage_limited_finances',
            'bizmanage_submit_expenses',

            // Reports
            'bizmanage_view_reports',
            'bizmanage_view_client_reports',
            'bizmanage_export_reports',

            // User management
            'bizmanage_manage_users',
            'bizmanage_view_users',

            // Settings
            'bizmanage_manage_settings',
            'bizmanage_view_settings',

            // Data management
            'bizmanage_export_data',
            'bizmanage_import_data',
            'bizmanage_backup_data',

            // Personal data
            'bizmanage_view_own_data',
        );

        // Add all capabilities to administrator
        $admin_role = get_role('administrator');
        if ($admin_role) {
            foreach ($capabilities as $cap) {
                $admin_role->add_cap($cap);
            }
        }

        // Add all capabilities to BizManage Admin
        $bm_admin = get_role('bizmanage_admin');
        if ($bm_admin) {
            foreach ($capabilities as $cap) {
                $bm_admin->add_cap($cap);
            }
        }

        // Accountant: all except manage_users, manage_settings, import/export data
        $bm_accountant = get_role('bizmanage_accountant');
        if ($bm_accountant) {
            $accountant_caps = array(
                'bizmanage_view_entities',
                'bizmanage_manage_entities',
                'bizmanage_manage_documents',
                'bizmanage_manage_finances',
                'bizmanage_view_reports',
                'bizmanage_view_documents',
                'bizmanage_limited_finances',
                'bizmanage_submit_expenses',
                'bizmanage_view_own_data',
                'bizmanage_view_client_reports',
            );
            foreach ($accountant_caps as $cap) {
                $bm_accountant->add_cap($cap);
            }
        }

        // Manager: view/manage entities, finances, documents, reports
        $bm_manager = get_role('bizmanage_manager');
        if ($bm_manager) {
            $manager_caps = array(
                'bizmanage_view_entities',
                'bizmanage_manage_entities',
                'bizmanage_manage_finances',
                'bizmanage_view_reports',
                'bizmanage_view_documents',
                'bizmanage_limited_finances',
                'bizmanage_submit_expenses',
                'bizmanage_view_own_data',
                'bizmanage_view_client_reports',
            );
            foreach ($manager_caps as $cap) {
                $bm_manager->add_cap($cap);
            }
        }
    }

    /**
     * Remove user roles
     */
    public static function remove_roles() {
        remove_role('bizmanage_admin');
        remove_role('bizmanage_accountant');
        remove_role('bizmanage_manager');
        remove_role('bizmanage_employee');
        remove_role('bizmanage_client');

        // Remove all BizManage Pro capabilities from administrator
        $admin_role = get_role('administrator');
        if ($admin_role) {
            $capabilities = array(
                'bizmanage_view_entities',
                'bizmanage_manage_entities',
                'bizmanage_manage_documents',
                'bizmanage_manage_finances',
                'bizmanage_view_reports',
                'bizmanage_manage_users',
                'bizmanage_manage_settings',
                'bizmanage_export_data',
                'bizmanage_import_data',
                'bizmanage_view_documents',
                'bizmanage_limited_finances',
                'bizmanage_submit_expenses',
                'bizmanage_view_own_data',
                'bizmanage_view_client_reports',
            );
            foreach ($capabilities as $cap) {
                $admin_role->remove_cap($cap);
            }
        }
    }

    /**
     * Create upload directories
     */
    public static function create_directories() {
        $upload_dir = wp_upload_dir();
        $bizmanage_dir = $upload_dir['basedir'] . '/bizmanage-pro';

        $directories = array(
            $bizmanage_dir,
            $bizmanage_dir . '/documents',
            $bizmanage_dir . '/documents/encrypted',
            $bizmanage_dir . '/exports',
            $bizmanage_dir . '/backups',
            $bizmanage_dir . '/temp',
        );

        foreach ($directories as $dir) {
            if (!file_exists($dir)) {
                wp_mkdir_p($dir);

                // Create .htaccess file for security
                $htaccess_file = $dir . '/.htaccess';
                if (!file_exists($htaccess_file)) {
                    file_put_contents($htaccess_file, "deny from all\n");
                }

                // Create index.php file for security
                $index_file = $dir . '/index.php';
                if (!file_exists($index_file)) {
                    file_put_contents($index_file, "<?php\n// Silence is golden.\n");
                }
            }
        }
    }

    /**
     * Remove upload directories
     */
    public static function remove_directories() {
        $upload_dir = wp_upload_dir();
        $bizmanage_dir = $upload_dir['basedir'] . '/bizmanage-pro';

        if (file_exists($bizmanage_dir)) {
            self::delete_directory($bizmanage_dir);
        }
    }

    /**
     * Recursively delete directory
     */
    private static function delete_directory($dir) {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), array('.', '..'));

        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                self::delete_directory($path);
            } else {
                unlink($path);
            }
        }

        return rmdir($dir);
    }

    /**
     * Set default options
     */
    public static function set_default_options() {
        $default_options = array(
            'bizmanage_pro_version' => BIZMANAGE_PRO_VERSION,
            'bizmanage_pro_db_version' => BIZMANAGE_PRO_DB_VERSION,
            'bizmanage_pro_default_currency' => 'USD',
            'bizmanage_pro_date_format' => 'Y-m-d',
            'bizmanage_pro_time_format' => 'H:i:s',
            'bizmanage_pro_timezone' => 'UTC',
            'bizmanage_pro_fiscal_year_start' => '01-01',
            'bizmanage_pro_tax_rate' => '0.00',
            'bizmanage_pro_backup_frequency' => 'weekly',
            'bizmanage_pro_max_file_size' => '10485760', // 10MB
            'bizmanage_pro_allowed_file_types' => 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png',
            'bizmanage_pro_encryption_enabled' => '1',
            'bizmanage_pro_remove_data_on_uninstall' => '0',
        );

        foreach ($default_options as $option_name => $option_value) {
            if (get_option($option_name) === false) {
                add_option($option_name, $option_value);
            }
        }
    }

    /**
     * Remove all plugin options
     */
    public static function remove_options() {
        global $wpdb;

        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE 'bizmanage_pro_%'");
    }

    /**
     * Remove database tables
     */
    public static function remove_tables() {
        global $wpdb;

        $tables = array(
            $wpdb->prefix . 'bizmanage_business_entities',
            $wpdb->prefix . 'bizmanage_documents',
            $wpdb->prefix . 'bizmanage_transactions',
            $wpdb->prefix . 'bizmanage_recurring_transactions',
            $wpdb->prefix . 'bizmanage_bank_accounts',
            $wpdb->prefix . 'bizmanage_categories',
            $wpdb->prefix . 'bizmanage_settings',
        );

        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
    }
}

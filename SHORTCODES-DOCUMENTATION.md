# BizManage Pro - Shortcodes Documentation

BizManage Pro provides powerful shortcodes to display financial reports on your website frontend. These shortcodes allow you to share financial information publicly or with specific audiences.

## 📊 **Available Shortcodes**

### 1. Income Statement (Profit & Loss)
```
[bizmanage_income_statement]
```

### 2. Balance Sheet
```
[bizmanage_balance_sheet]
```

### 3. Cash Flow Statement
```
[bizmanage_cashflow]
```

### 4. Financial Summary
```
[bizmanage_financial_summary]
```

## 🔧 **Shortcode Parameters**

All shortcodes support the following parameters:

### Required Parameters

| Parameter | Description | Example |
|-----------|-------------|---------|
| `entity_id` | The ID of the business entity to display | `entity_id="1"` |

### Optional Parameters

| Parameter | Description | Default | Options |
|-----------|-------------|---------|---------|
| `period` | Date range for the report | `last_month` | See period options below |
| `style` | Display style | `table` | `table`, `summary`, `cards` |
| `show_title` | Show report title | `true` | `true`, `false` |
| `currency` | Currency to display | `USD` | `USD`, `EUR`, `GBP`, `JPY`, `BDT` |

### Period Options

| Value | Description |
|-------|-------------|
| `last_week` | Last 7 days |
| `last_month` | Last 30 days |
| `last_quarter` | Last 90 days |
| `last_year` | Last 365 days |
| `this_month` | Current month (1st to last day) |
| `this_year` | Current year (Jan 1 to Dec 31) |
| `2024-01-01,2024-12-31` | Custom date range (start,end) |

## 📋 **Shortcode Examples**

### Basic Usage

#### Simple Income Statement
```
[bizmanage_income_statement entity_id="1"]
```

#### Balance Sheet for Last Quarter
```
[bizmanage_balance_sheet entity_id="1" period="last_quarter"]
```

#### Cash Flow with Custom Currency
```
[bizmanage_cashflow entity_id="1" currency="EUR"]
```

#### Financial Summary as Cards
```
[bizmanage_financial_summary entity_id="1" style="cards"]
```

### Advanced Usage

#### Income Statement with Custom Date Range
```
[bizmanage_income_statement entity_id="1" period="2024-01-01,2024-03-31" style="table" currency="USD" show_title="true"]
```

#### Summary Cards without Title
```
[bizmanage_financial_summary entity_id="1" period="this_year" style="cards" show_title="false" currency="BDT"]
```

#### Multiple Reports on One Page
```
<h2>Q1 2024 Financial Reports</h2>

[bizmanage_financial_summary entity_id="1" period="2024-01-01,2024-03-31" style="cards"]

<h3>Detailed Income Statement</h3>
[bizmanage_income_statement entity_id="1" period="2024-01-01,2024-03-31" style="table"]

<h3>Balance Sheet</h3>
[bizmanage_balance_sheet entity_id="1" period="2024-03-31,2024-03-31"]
```

## 🎨 **Display Styles**

### Table Style (`style="table"`)
- Professional tabular format
- Detailed breakdown by category
- Suitable for comprehensive reports
- Best for desktop viewing

### Summary Style (`style="summary"`)
- Condensed card layout
- Key metrics only
- Good for overview pages
- Mobile-friendly

### Cards Style (`style="cards"`)
- Modern card-based layout
- Visual appeal with color coding
- Perfect for dashboards
- Responsive design

## 🔒 **Security & Access Control**

### Public Access
By default, shortcodes require the entity to be marked as "public" in settings. To enable public access:

1. Go to `BizManage Pro > Settings`
2. Navigate to "Security" tab
3. Enable "Public Reports" for specific entities
4. Save settings

### Logged-in User Access
If public access is disabled, only logged-in users with appropriate permissions can view the reports.

### Entity Privacy
Each business entity can have individual privacy settings:
- **Private**: Only entity users can view
- **Public**: Anyone can view via shortcodes
- **Restricted**: Only specific user roles can view

## 📱 **Responsive Design**

All shortcode reports are fully responsive:

### Desktop (1200px+)
- Full table layouts
- Multi-column card grids
- Complete data display

### Tablet (768px - 1199px)
- Optimized table spacing
- 2-column card layouts
- Touch-friendly interface

### Mobile (< 768px)
- Stacked table rows
- Single-column cards
- Simplified navigation

## 🎯 **Use Cases**

### 1. Investor Relations Page
```html
<h2>Financial Performance</h2>
<p>Our latest financial results demonstrate strong growth and profitability.</p>

[bizmanage_financial_summary entity_id="1" period="last_quarter" style="cards"]

<h3>Detailed Income Statement</h3>
[bizmanage_income_statement entity_id="1" period="last_quarter"]
```

### 2. Client Portal
```html
<h2>Your Project Financials</h2>
[bizmanage_income_statement entity_id="2" period="this_month" show_title="false"]
```

### 3. Annual Report Page
```html
<h1>2024 Annual Financial Report</h1>

<h2>Executive Summary</h2>
[bizmanage_financial_summary entity_id="1" period="this_year" style="cards"]

<h2>Income Statement</h2>
[bizmanage_income_statement entity_id="1" period="this_year" style="table"]

<h2>Balance Sheet</h2>
[bizmanage_balance_sheet entity_id="1" period="2024-12-31,2024-12-31"]

<h2>Cash Flow Statement</h2>
[bizmanage_cashflow entity_id="1" period="this_year"]
```

### 4. Dashboard Widget
```html
<div class="financial-widget">
    <h3>This Month's Performance</h3>
    [bizmanage_financial_summary entity_id="1" period="this_month" style="cards" show_title="false"]
</div>
```

## 🛠️ **Customization**

### CSS Customization
You can customize the appearance by adding CSS to your theme:

```css
/* Customize report tables */
.bizmanage-report-table {
    border: 2px solid #your-color;
}

/* Customize summary cards */
.bizmanage-summary-card {
    background: #your-background;
    border-radius: 10px;
}

/* Customize profit/loss colors */
.bizmanage-summary-card.profit .amount {
    color: #your-profit-color;
}

.bizmanage-summary-card.loss .amount {
    color: #your-loss-color;
}
```

### Custom Wrapper
Wrap shortcodes in custom HTML for additional styling:

```html
<div class="my-financial-reports">
    <div class="report-header">
        <h2>Financial Overview</h2>
        <p>Updated: <?php echo date('F j, Y'); ?></p>
    </div>
    
    [bizmanage_financial_summary entity_id="1" style="cards"]
    
    <div class="report-footer">
        <p><small>All figures in USD. For detailed information, contact our finance team.</small></p>
    </div>
</div>
```

## 🔍 **Troubleshooting**

### Common Issues

#### Shortcode Not Displaying
- Check that entity_id exists and is correct
- Verify entity has public access enabled
- Ensure user has appropriate permissions

#### No Data Showing
- Confirm transactions exist for the specified period
- Check that transactions are marked as "completed"
- Verify date format in custom periods

#### Styling Issues
- Clear cache if using caching plugins
- Check for theme CSS conflicts
- Verify frontend.css is loading

### Error Messages

#### "Invalid parameters provided"
- Check entity_id is a valid number
- Verify period format is correct
- Ensure all required parameters are included

#### "Access denied"
- Enable public reports in settings
- Check user permissions
- Verify entity privacy settings

#### "No data available"
- Add transactions for the specified period
- Check transaction status (should be "completed")
- Verify entity has financial data

## 📞 **Support**

For additional help with shortcodes:

1. Check the plugin documentation
2. Review the testing guide
3. Contact support with specific error messages
4. Provide shortcode examples that aren't working

## 🚀 **Best Practices**

1. **Test shortcodes** on a staging site first
2. **Use appropriate periods** for your data
3. **Consider mobile users** when choosing styles
4. **Implement caching** for better performance
5. **Secure sensitive data** with proper access controls
6. **Update regularly** to ensure data accuracy
7. **Monitor performance** with large datasets

---

**Need help?** Check our comprehensive documentation or contact support for assistance with implementing these shortcodes on your website.

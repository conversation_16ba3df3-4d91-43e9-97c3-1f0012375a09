<?php
/**
 * BizManage Pro Admin Class
 *
 * Handles admin interface and functionality
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Admin Class
 */
class BizManage_Pro_Admin {

    /**
     * Instance of this class
     * @var BizManage_Pro_Admin
     */
    private static $instance = null;

    /**
     * Security instance
     * @var BizManage_Pro_Security
     */
    private $security;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Utilities instance
     * @var BizManage_Pro_Utilities
     */
    private $utilities;

    /**
     * Get instance
     * @return BizManage_Pro_Admin
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->security = BizManage_Pro_Security::instance();
        $this->db = BizManage_Pro_Database::instance();
        $this->utilities = BizManage_Pro_Utilities::instance();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_init', array($this, 'admin_init'));
        add_filter('plugin_action_links_' . BIZMANAGE_PRO_PLUGIN_BASENAME, array($this, 'add_plugin_action_links'));
    }

    /**
     * Admin initialization
     */
    public function admin_init() {
        // Check if user has access to BizManage Pro
        if (!$this->user_has_bizmanage_access()) {
            return;
        }

        // Register settings
        $this->register_settings();
    }

    /**
     * Check if user has BizManage access
     * @return bool
     */
    private function user_has_bizmanage_access() {
        $bizmanage_capabilities = array(
            'bizmanage_manage_entities',
            'bizmanage_view_entities',
            'bizmanage_manage_documents',
            'bizmanage_view_documents',
            'bizmanage_manage_finances',
            'bizmanage_view_finances',
            'bizmanage_view_reports',
            'bizmanage_submit_expenses',
            'bizmanage_view_own_data',
        );

        foreach ($bizmanage_capabilities as $capability) {
            if (current_user_can($capability)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        if (!$this->user_has_bizmanage_access()) {
            return;
        }

        // Main menu
        add_menu_page(
            __('BizManage Pro', 'bizmanage-pro'),
            __('BizManage Pro', 'bizmanage-pro'),
            'bizmanage_view_entities',
            'bizmanage-pro',
            array($this, 'dashboard_page'),
            'dashicons-chart-line',
            30
        );

        // Dashboard
        add_submenu_page(
            'bizmanage-pro',
            __('Dashboard', 'bizmanage-pro'),
            __('Dashboard', 'bizmanage-pro'),
            'bizmanage_view_entities',
            'bizmanage-pro',
            array($this, 'dashboard_page')
        );

        // Business Entities
        if (current_user_can('bizmanage_view_entities')) {
            add_submenu_page(
                'bizmanage-pro',
                __('Business Entities', 'bizmanage-pro'),
                __('Business Entities', 'bizmanage-pro'),
                'bizmanage_view_entities',
                'bizmanage-entities',
                array($this, 'entities_page')
            );
        }

        // Documents
        if (current_user_can('bizmanage_view_documents')) {
            add_submenu_page(
                'bizmanage-pro',
                __('Documents', 'bizmanage-pro'),
                __('Documents', 'bizmanage-pro'),
                'bizmanage_view_documents',
                'bizmanage-documents',
                array($this, 'documents_page')
            );
        }

        // Finances
        if (current_user_can('bizmanage_view_finances')) {
            add_submenu_page(
                'bizmanage-pro',
                __('Finances', 'bizmanage-pro'),
                __('Finances', 'bizmanage-pro'),
                'bizmanage_view_finances',
                'bizmanage-finances',
                array($this, 'finances_page')
            );
        }

        // Reports
        if (current_user_can('bizmanage_view_reports')) {
            add_submenu_page(
                'bizmanage-pro',
                __('Reports', 'bizmanage-pro'),
                __('Reports', 'bizmanage-pro'),
                'bizmanage_view_reports',
                'bizmanage-reports',
                array($this, 'reports_page')
            );
        }

        // Settings
        if (current_user_can('bizmanage_manage_settings')) {
            add_submenu_page(
                'bizmanage-pro',
                __('Settings', 'bizmanage-pro'),
                __('Settings', 'bizmanage-pro'),
                'bizmanage_manage_settings',
                'bizmanage-settings',
                array($this, 'settings_page')
            );
        }
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on BizManage Pro pages
        if (strpos($hook, 'bizmanage') === false) {
            return;
        }

        // Enqueue WordPress media scripts
        wp_enqueue_media();

        // Enqueue styles
        wp_enqueue_style(
            'bizmanage-pro-admin',
            BIZMANAGE_PRO_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            BIZMANAGE_PRO_VERSION
        );

        // Enqueue Bootstrap CSS
        wp_enqueue_style(
            'bizmanage-pro-bootstrap',
            'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
            array(),
            '5.1.3'
        );

        // Enqueue Chart.js
        wp_enqueue_script(
            'bizmanage-pro-chartjs',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            array(),
            '3.9.1',
            true
        );

        // Enqueue admin scripts
        wp_enqueue_script(
            'bizmanage-pro-admin',
            BIZMANAGE_PRO_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'bizmanage-pro-chartjs'),
            BIZMANAGE_PRO_VERSION,
            true
        );

        // Localize script
        wp_localize_script('bizmanage-pro-admin', 'bizmanageAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonces' => array(
                // Entity management
                'save_entity' => $this->security->create_nonce('bizmanage_save_entity'),
                'delete_entity' => $this->security->create_nonce('bizmanage_delete_entity'),
                'get_entities' => $this->security->create_nonce('bizmanage_get_entities'),

                // Document management
                'upload_document' => $this->security->create_nonce('bizmanage_upload_document'),
                'delete_document' => $this->security->create_nonce('bizmanage_delete_document'),
                'get_documents' => $this->security->create_nonce('bizmanage_get_documents'),

                // Financial management
                'save_transaction' => $this->security->create_nonce('bizmanage_save_transaction'),
                'delete_transaction' => $this->security->create_nonce('bizmanage_delete_transaction'),
                'get_transactions' => $this->security->create_nonce('bizmanage_get_transactions'),
                'get_financial_summary' => $this->security->create_nonce('bizmanage_get_financial_summary'),

                // Category management
                'save_category' => $this->security->create_nonce('bizmanage_save_category'),
                'delete_category' => $this->security->create_nonce('bizmanage_delete_category'),
                'get_categories' => $this->security->create_nonce('bizmanage_get_categories'),

                // Reports
                'generate_report' => $this->security->create_nonce('bizmanage_generate_report'),
                'export_report' => $this->security->create_nonce('bizmanage_export_report'),

                // Settings
                'save_settings' => $this->security->create_nonce('bizmanage_save_settings'),

                // Dashboard
                'get_dashboard_data' => $this->security->create_nonce('bizmanage_get_dashboard_data'),
            ),
            'strings' => array(
                'confirm_delete' => __('Are you sure you want to delete this item?', 'bizmanage-pro'),
                'error_occurred' => __('An error occurred. Please try again.', 'bizmanage-pro'),
                'success' => __('Operation completed successfully.', 'bizmanage-pro'),
                'loading' => __('Loading...', 'bizmanage-pro'),
                'no_data' => __('No data available.', 'bizmanage-pro'),
                'invalid_file' => __('Please select a valid file.', 'bizmanage-pro'),
                'required_field' => __('This field is required.', 'bizmanage-pro'),
            )
        ));
    }

    /**
     * Register settings
     */
    private function register_settings() {
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_default_currency');
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_date_format');
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_time_format');
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_timezone');
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_fiscal_year_start');
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_tax_rate');
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_backup_frequency');
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_max_file_size');
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_allowed_file_types');
        register_setting('bizmanage_pro_settings', 'bizmanage_pro_encryption_enabled');
    }

    /**
     * Dashboard page
     */
    public function dashboard_page() {
        $this->render_page('dashboard');
    }

    /**
     * Entities page
     */
    public function entities_page() {
        $this->render_page('entities');
    }

    /**
     * Documents page
     */
    public function documents_page() {
        $this->render_page('documents');
    }

    /**
     * Finances page
     */
    public function finances_page() {
        $this->render_page('finances');
    }

    /**
     * Reports page
     */
    public function reports_page() {
        $this->render_page('reports');
    }

    /**
     * Settings page
     */
    public function settings_page() {
        $this->render_page('settings');
    }

    /**
     * Render page template
     * @param string $page
     */
    private function render_page($page) {
        $template_file = BIZMANAGE_PRO_PLUGIN_DIR . "templates/admin/{$page}.php";
        
        if (file_exists($template_file)) {
            include $template_file;
        } else {
            echo '<div class="wrap">';
            echo '<h1>' . esc_html__('BizManage Pro', 'bizmanage-pro') . '</h1>';
            echo '<p>' . sprintf(esc_html__('Template file not found: %s', 'bizmanage-pro'), $template_file) . '</p>';
            echo '</div>';
        }
    }

    /**
     * Add plugin action links
     * @param array $links
     * @return array
     */
    public function add_plugin_action_links($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=bizmanage-settings') . '">' . __('Settings', 'bizmanage-pro') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * Get user entities for dropdown
     * @return array
     */
    public function get_user_entities_dropdown() {
        $roles = BizManage_Pro_Roles::instance();
        $entities = $roles->get_user_entities();
        
        $options = array();
        foreach ($entities as $entity) {
            $options[$entity->id] = $entity->business_name;
        }
        
        return $options;
    }

    /**
     * Render entity selector
     */
    public function render_entity_selector() {
        $entities = $this->get_user_entities_dropdown();
        
        if (empty($entities)) {
            echo '<div class="notice notice-warning"><p>' . __('No business entities found. Please create one first.', 'bizmanage-pro') . '</p></div>';
            return;
        }

        echo '<div class="bizmanage-entity-selector">';
        echo '<label for="bizmanage-entity-select">' . __('Select Business Entity:', 'bizmanage-pro') . '</label>';
        echo '<select id="bizmanage-entity-select" name="entity_id" class="form-select">';
        
        foreach ($entities as $id => $name) {
            echo '<option value="' . esc_attr($id) . '">' . esc_html($name) . '</option>';
        }
        
        echo '</select>';
        echo '</div>';
    }
}

<?php
/**
 * Database Debug Check for BizManage Pro
 *
 * Add this to your WordPress root and access via browser to check database tables
 */

// WordPress environment - fix the path
if (file_exists('wp-config.php')) {
    require_once('wp-config.php');
    require_once('wp-load.php');
} elseif (file_exists('../wp-config.php')) {
    require_once('../wp-config.php');
    require_once('../wp-load.php');
} elseif (file_exists('../../wp-config.php')) {
    require_once('../../wp-config.php');
    require_once('../../wp-load.php');
} else {
    // Try WordPress standard way
    define('WP_USE_THEMES', false);
    if (file_exists('wp-load.php')) {
        require_once('wp-load.php');
    } else {
        die('WordPress config file not found. Please place this file in WordPress root directory.');
    }
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>BizManage Pro Database Debug Check</h1>';

global $wpdb;

// Define table names
$tables = array(
    'business_entities' => $wpdb->prefix . 'bizmanage_business_entities',
    'documents' => $wpdb->prefix . 'bizmanage_documents',
    'transactions' => $wpdb->prefix . 'bizmanage_transactions',
    'recurring_transactions' => $wpdb->prefix . 'bizmanage_recurring_transactions',
    'bank_accounts' => $wpdb->prefix . 'bizmanage_bank_accounts',
    'categories' => $wpdb->prefix . 'bizmanage_categories',
    'settings' => $wpdb->prefix . 'bizmanage_settings'
);

echo '<h2>Table Existence Check</h2>';
echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
echo '<tr><th>Table Key</th><th>Table Name</th><th>Exists</th><th>Row Count</th></tr>';

foreach ($tables as $key => $table_name) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    $count = $exists ? $wpdb->get_var("SELECT COUNT(*) FROM $table_name") : 0;
    
    $status_color = $exists ? 'green' : 'red';
    echo "<tr>";
    echo "<td>$key</td>";
    echo "<td>$table_name</td>";
    echo "<td style='color: $status_color;'>" . ($exists ? 'YES' : 'NO') . "</td>";
    echo "<td>$count</td>";
    echo "</tr>";
}

echo '</table>';

// Check categories table structure
echo '<h2>Categories Table Structure</h2>';
if ($wpdb->get_var("SHOW TABLES LIKE '{$tables['categories']}'") === $tables['categories']) {
    $columns = $wpdb->get_results("DESCRIBE {$tables['categories']}");
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>{$column->Default}</td>";
        echo "</tr>";
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">Categories table does not exist!</p>';
}

// Check transactions table structure
echo '<h2>Transactions Table Structure</h2>';
if ($wpdb->get_var("SHOW TABLES LIKE '{$tables['transactions']}'") === $tables['transactions']) {
    $columns = $wpdb->get_results("DESCRIBE {$tables['transactions']}");
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>{$column->Default}</td>";
        echo "</tr>";
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">Transactions table does not exist!</p>';
}

// Check business entities
echo '<h2>Business Entities</h2>';
if ($wpdb->get_var("SHOW TABLES LIKE '{$tables['business_entities']}'") === $tables['business_entities']) {
    $entities = $wpdb->get_results("SELECT id, business_name, entity_type, status FROM {$tables['business_entities']}");
    
    if ($entities) {
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>ID</th><th>Business Name</th><th>Entity Type</th><th>Status</th></tr>';
        
        foreach ($entities as $entity) {
            echo "<tr>";
            echo "<td>{$entity->id}</td>";
            echo "<td>{$entity->business_name}</td>";
            echo "<td>{$entity->entity_type}</td>";
            echo "<td>{$entity->status}</td>";
            echo "</tr>";
        }
        echo '</table>';
    } else {
        echo '<p>No business entities found.</p>';
    }
} else {
    echo '<p style="color: red;">Business entities table does not exist!</p>';
}

// Check categories
echo '<h2>Categories</h2>';
if ($wpdb->get_var("SHOW TABLES LIKE '{$tables['categories']}'") === $tables['categories']) {
    $categories = $wpdb->get_results("SELECT id, entity_id, name, type, status FROM {$tables['categories']} ORDER BY entity_id, type, name");
    
    if ($categories) {
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>ID</th><th>Entity ID</th><th>Name</th><th>Type</th><th>Status</th></tr>';
        
        foreach ($categories as $category) {
            echo "<tr>";
            echo "<td>{$category->id}</td>";
            echo "<td>{$category->entity_id}</td>";
            echo "<td>{$category->name}</td>";
            echo "<td>{$category->type}</td>";
            echo "<td>{$category->status}</td>";
            echo "</tr>";
        }
        echo '</table>';
    } else {
        echo '<p>No categories found.</p>';
    }
} else {
    echo '<p style="color: red;">Categories table does not exist!</p>';
}

// Check recent transactions
echo '<h2>Recent Transactions</h2>';
if ($wpdb->get_var("SHOW TABLES LIKE '{$tables['transactions']}'") === $tables['transactions']) {
    $transactions = $wpdb->get_results("SELECT id, entity_id, transaction_type, amount, description, category, status FROM {$tables['transactions']} ORDER BY created_at DESC LIMIT 10");
    
    if ($transactions) {
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>ID</th><th>Entity ID</th><th>Type</th><th>Amount</th><th>Description</th><th>Category</th><th>Status</th></tr>';
        
        foreach ($transactions as $transaction) {
            echo "<tr>";
            echo "<td>{$transaction->id}</td>";
            echo "<td>{$transaction->entity_id}</td>";
            echo "<td>{$transaction->transaction_type}</td>";
            echo "<td>{$transaction->amount}</td>";
            echo "<td>{$transaction->description}</td>";
            echo "<td>{$transaction->category}</td>";
            echo "<td>{$transaction->status}</td>";
            echo "</tr>";
        }
        echo '</table>';
    } else {
        echo '<p>No transactions found.</p>';
    }
} else {
    echo '<p style="color: red;">Transactions table does not exist!</p>';
}

// Check WordPress error log
echo '<h2>Recent WordPress Errors (BizManage Pro related)</h2>';
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $bizmanage_errors = array();
    
    foreach ($lines as $line) {
        if (strpos($line, 'BizManagePro') !== false) {
            $bizmanage_errors[] = $line;
        }
    }
    
    if (!empty($bizmanage_errors)) {
        echo '<div style="background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: auto;">';
        echo '<pre>' . implode("\n", array_slice($bizmanage_errors, -20)) . '</pre>';
        echo '</div>';
    } else {
        echo '<p>No BizManage Pro related errors found in debug.log</p>';
    }
} else {
    echo '<p>Debug log file not found. Enable WP_DEBUG_LOG in wp-config.php</p>';
}

echo '<h2>Plugin Status</h2>';
echo '<p><strong>Plugin Active:</strong> ' . (is_plugin_active('bizmanage-pro/bizmanage-pro.php') ? 'YES' : 'NO') . '</p>';
echo '<p><strong>Current User ID:</strong> ' . get_current_user_id() . '</p>';
echo '<p><strong>Current User Capabilities:</strong></p>';
echo '<ul>';
$user = wp_get_current_user();
foreach ($user->allcaps as $cap => $has_cap) {
    if (strpos($cap, 'bizmanage') !== false && $has_cap) {
        echo "<li>$cap</li>";
    }
}
echo '</ul>';

echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">Go to BizManage Pro Dashboard</a></p>';
?>

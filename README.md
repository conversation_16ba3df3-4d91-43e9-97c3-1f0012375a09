# BizManage Pro - Smart Accounting & Document Management for Small Businesses

A comprehensive WordPress plugin designed specifically for small businesses to manage their finances, documents, and business operations efficiently.

## Features

### 🏢 Business Entity Management
- Support for multiple business entity types:
  - Sole Proprietorships
  - Partnerships
  - Limited Companies
  - Corporations
  - LLCs
- Entity-specific fields and reporting requirements
- Multi-entity support for users managing multiple businesses

### 📄 Document Management System
- **Secure File Storage**: AES-256 encryption for sensitive documents
- **Supported File Types**: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG
- **Document Categorization**: Invoices, receipts, contracts, tax documents, etc.
- **Version Control**: Track document updates and maintain version history
- **Advanced Search**: Filter by date, category, keywords, and tags
- **Document Preview**: Built-in preview capabilities for supported file types

### 💰 Financial Management
- **Income & Expense Tracking**: Comprehensive transaction management
- **Multi-Currency Support**: Real-time currency conversion
- **Automated Tax Calculations**: Configurable tax rates by region/category
- **Recurring Transactions**: Set up automatic recurring income/expenses
- **Bank Reconciliation**: Tools to match transactions with bank statements
- **Category Management**: Customizable income and expense categories

### 📊 Financial Reports (Auto-generated)
- **Income Statement** (Profit & Loss)
- **Balance Sheet**
- **Cash Flow Statement**
- **Tax Summary Reports**
- **Custom Date Range Reporting**
- **Export Options**: PDF and Excel formats

### 👥 User Role Management
- **Administrator**: Full access to all features
- **Accountant**: Financial data access and document management
- **Manager**: View reports with limited editing capabilities
- **Employee**: Expense submission and personal data access
- **Client**: View-only access to specific reports

### 📈 Dashboard & Analytics
- **Visual Charts**: Powered by Chart.js
- **Key Performance Indicators (KPIs)**
- **Financial Trend Analysis**
- **Expense Breakdown Charts**
- **Revenue vs. Expense Comparisons**
- **Mobile-Responsive Design**

## Technical Specifications

### Requirements
- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher
- **MySQL**: 5.6 or higher

### Security Features
- WordPress nonces for CSRF protection
- Data sanitization and validation
- Capability-based access control
- AES-256 file encryption
- Secure file upload validation
- SQL injection prevention

### Performance
- Optimized database queries with proper indexing
- AJAX-powered interface for smooth user experience
- Lightweight codebase with minimal external dependencies
- Caching support for improved performance

## Installation

1. Upload the plugin files to `/wp-content/plugins/bizmanage-pro/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to 'BizManage Pro' in your WordPress admin menu
4. Create your first business entity to get started

## Configuration

### Initial Setup
1. **Create Business Entity**: Set up your business information
2. **Configure Settings**: Set currency, tax rates, and fiscal year
3. **Set User Roles**: Assign appropriate roles to team members
4. **Upload Documents**: Start organizing your business documents

### Currency Settings
- Default currency selection
- Multi-currency transaction support
- Real-time exchange rate conversion
- Currency symbol positioning

### Tax Configuration
- Configurable tax rates by region/category
- Automated tax calculations
- Tax summary reporting
- Fiscal year settings (calendar vs. custom)

## Usage

### Managing Business Entities
```php
// Create a new business entity
$entity_data = array(
    'business_name' => 'My Business',
    'entity_type' => 'limited_company',
    'tax_id' => '*********',
    'currency' => 'USD'
);
$entity_id = BizManage_Pro_Entities::instance()->create_entity($entity_data);
```

### Recording Transactions
```php
// Add an income transaction
$transaction_data = array(
    'entity_id' => 1,
    'transaction_type' => 'income',
    'amount' => 1000.00,
    'currency' => 'USD',
    'category' => 'Sales Revenue',
    'description' => 'Product sales',
    'transaction_date' => '2024-01-15'
);
$transaction_id = BizManage_Pro_Finances::instance()->create_transaction($transaction_data);
```

### Document Upload
```php
// Upload a document
$document_data = array(
    'entity_id' => 1,
    'title' => 'Invoice #001',
    'category' => 'invoice',
    'description' => 'Client invoice for services'
);
$document_id = BizManage_Pro_Documents::instance()->upload_document($_FILES['document'], $document_data);
```

## Shortcodes

### Dashboard Shortcode
```
[bizmanage_dashboard entity_id="1" period="month" show_charts="true"]
```

### Reports Shortcode
```
[bizmanage_reports entity_id="1" report_type="income_statement" period="month"]
```

### Expense Form Shortcode
```
[bizmanage_expense_form entity_id="1" redirect_url="/thank-you/"]
```

### Client Portal Shortcode
```
[bizmanage_client_portal entity_id="1"]
```

## Hooks and Filters

### Action Hooks
- `bizmanage_pro_loaded` - Fired when plugin is fully loaded
- `bizmanage_entity_created` - Fired when a new entity is created
- `bizmanage_transaction_created` - Fired when a new transaction is created
- `bizmanage_document_uploaded` - Fired when a document is uploaded

### Filter Hooks
- `bizmanage_pro_currencies` - Filter available currencies
- `bizmanage_pro_transaction_types` - Filter transaction types
- `bizmanage_pro_document_categories` - Filter document categories

## Database Schema

### Main Tables
- `wp_bizmanage_business_entities` - Business entity information
- `wp_bizmanage_documents` - Document metadata and file information
- `wp_bizmanage_transactions` - Financial transactions
- `wp_bizmanage_recurring_transactions` - Recurring transaction templates
- `wp_bizmanage_bank_accounts` - Bank account information
- `wp_bizmanage_categories` - Income and expense categories
- `wp_bizmanage_settings` - Plugin and entity-specific settings

## Internationalization

The plugin is fully internationalized and includes:
- Bengali translation (included)
- Translation-ready for other languages
- Proper text domain implementation
- RTL language support

## Support

For support and documentation, please visit:
- Plugin Documentation: [Coming Soon]
- Support Forum: [Coming Soon]
- GitHub Repository: [Coming Soon]

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Complete business entity management
- Document management with encryption
- Financial transaction tracking
- Multi-currency support
- User role management
- Dashboard and reporting
- Mobile-responsive design

## Contributing

We welcome contributions! Please see our contributing guidelines for more information.

## Credits

- Developed by the BizManage Pro Team
- Built with WordPress best practices
- Uses Chart.js for data visualization
- Bootstrap for responsive design

<?php
/**
 * BizManage Pro Categories Class
 *
 * Handles category management for financial transactions
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Categories Class
 */
class BizManage_Pro_Categories {

    /**
     * Instance of this class
     * @var BizManage_Pro_Categories
     */
    private static $instance = null;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Security instance
     * @var BizManage_Pro_Security
     */
    private $security;

    /**
     * Get instance
     * @return BizManage_Pro_Categories
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->db = BizManage_Pro_Database::instance();
        $this->security = BizManage_Pro_Security::instance();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_ajax_bizmanage_save_category', array($this, 'ajax_save_category'));
        add_action('wp_ajax_bizmanage_delete_category', array($this, 'ajax_delete_category'));
        add_action('wp_ajax_bizmanage_get_categories', array($this, 'ajax_get_categories'));
    }

    /**
     * Get categories for an entity and type
     * @param int $entity_id
     * @param string $type
     * @return array
     */
    public function get_categories($entity_id = 0, $type = '') {
        $where = array('status' => 'active');
        
        if ($entity_id > 0) {
            $where['entity_id'] = $entity_id;
        }
        
        if (!empty($type)) {
            $where['type'] = $type;
        }

        $categories = $this->db->get_results('categories', array(
            'where' => $where,
            'order_by' => 'name',
            'order' => 'ASC'
        ));

        // Ensure we have an array
        if (!is_array($categories)) {
            $categories = array();
        }

        // Add default categories if none exist
        if (empty($categories) && $entity_id > 0) {
            $this->create_default_categories($entity_id, $type);
            $categories = $this->db->get_results('categories', array(
                'where' => $where,
                'order_by' => 'name',
                'order' => 'ASC'
            ));

            // Ensure we still have an array after retry
            if (!is_array($categories)) {
                $categories = array();
            }
        }

        return $categories;
    }

    /**
     * Create a new category
     * @param array $data
     * @return int|false
     */
    public function create_category($data) {
        $required_fields = array('name', 'type', 'entity_id');
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                error_log('BizManagePro: Category creation failed - missing field: ' . $field);
                return false;
            }
        }

        // Check if category already exists
        $existing = $this->db->get_row('categories', array(
            'where' => array(
                'name' => $data['name'],
                'type' => $data['type'],
                'entity_id' => $data['entity_id']
            )
        ));

        if ($existing) {
            error_log('BizManagePro: Category creation failed - category already exists: ' . $data['name'] . ' for entity: ' . $data['entity_id']);
            return false; // Category already exists
        }

        $category_data = array(
            'entity_id' => intval($data['entity_id']),
            'name' => $this->security->sanitize_input($data['name'], 'text'),
            'type' => $this->security->sanitize_input($data['type'], 'text'),
            'description' => $this->security->sanitize_input($data['description'] ?? '', 'textarea'),
            'parent_id' => !empty($data['parent_id']) ? intval($data['parent_id']) : null,
            'color' => $this->security->sanitize_input($data['color'] ?? '#007cba', 'text'),
            'status' => 'active',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        );

        $result = $this->db->insert('categories', $category_data);

        if ($result) {
            error_log('BizManagePro: Category created successfully with ID: ' . $result . ' - Name: ' . $data['name']);
        } else {
            error_log('BizManagePro: Category creation failed - database insert error for: ' . $data['name']);
        }

        return $result;
    }

    /**
     * Get single category by ID
     * @param int $category_id
     * @return object|null
     */
    public function get_category($category_id) {
        return $this->db->get_row('categories', array(
            'where' => array('id' => intval($category_id))
        ));
    }

    /**
     * Update a category
     * @param int $category_id
     * @param array $data
     * @return bool
     */
    public function update_category($category_id, $data) {
        $category_data = array();

        if (isset($data['name'])) {
            $category_data['name'] = $this->security->sanitize_input($data['name'], 'text');
        }

        if (isset($data['description'])) {
            $category_data['description'] = $this->security->sanitize_input($data['description'], 'textarea');
        }

        if (isset($data['color'])) {
            $category_data['color'] = $this->security->sanitize_input($data['color'], 'text');
        }

        if (isset($data['status'])) {
            $category_data['status'] = $this->security->sanitize_input($data['status'], 'text');
        }

        $category_data['updated_at'] = current_time('mysql');

        return $this->db->update('categories', $category_data, array('id' => $category_id));
    }

    /**
     * Delete a category
     * @param int $category_id
     * @return bool
     */
    public function delete_category($category_id) {
        // Check if category is in use
        $transactions_count = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->db->get_table('transactions')} WHERE category = (SELECT name FROM {$this->db->get_table('categories')} WHERE id = %d)",
            $category_id
        );

        if ($transactions_count > 0) {
            // Soft delete if category is in use
            return $this->update_category($category_id, array('status' => 'deleted'));
        } else {
            // Hard delete if not in use
            return $this->db->delete('categories', array('id' => $category_id));
        }
    }

    /**
     * Create default categories for an entity
     * @param int $entity_id
     * @param string $type
     */
    private function create_default_categories($entity_id, $type = '') {
        $default_categories = array();

        if ($type === 'income' || empty($type)) {
            $default_categories = array_merge($default_categories, array(
                array('name' => 'Product Sales', 'type' => 'income', 'color' => '#28a745'),
                array('name' => 'Service Revenue', 'type' => 'income', 'color' => '#17a2b8'),
                array('name' => 'Consulting Fees', 'type' => 'income', 'color' => '#6f42c1'),
                array('name' => 'Interest Income', 'type' => 'income', 'color' => '#fd7e14'),
                array('name' => 'Other Income', 'type' => 'income', 'color' => '#6c757d'),
            ));
        }

        if ($type === 'expense' || empty($type)) {
            $default_categories = array_merge($default_categories, array(
                array('name' => 'Office Rent', 'type' => 'expense', 'color' => '#dc3545'),
                array('name' => 'Utilities', 'type' => 'expense', 'color' => '#ffc107'),
                array('name' => 'Office Supplies', 'type' => 'expense', 'color' => '#e83e8c'),
                array('name' => 'Travel Expenses', 'type' => 'expense', 'color' => '#20c997'),
                array('name' => 'Marketing', 'type' => 'expense', 'color' => '#fd7e14'),
                array('name' => 'Professional Services', 'type' => 'expense', 'color' => '#6f42c1'),
                array('name' => 'Insurance', 'type' => 'expense', 'color' => '#17a2b8'),
                array('name' => 'Equipment', 'type' => 'expense', 'color' => '#28a745'),
                array('name' => 'Other Expenses', 'type' => 'expense', 'color' => '#6c757d'),
            ));
        }

        foreach ($default_categories as $category) {
            $category['entity_id'] = $entity_id;
            $this->create_category($category);
        }
    }

    /**
     * AJAX handler for saving categories
     */
    public function ajax_save_category() {
        // Log the request for debugging (fix the logging issue)
        error_log('BizManagePro: ajax_save_category called with POST keys: ' . implode(', ', array_keys($_POST)));
        error_log('BizManagePro: ajax_save_category POST data: ' . json_encode($_POST));

        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'bizmanage_save_category')) {
            error_log('BizManagePro: ajax_save_category - nonce verification failed');
            wp_send_json_error('Security check failed.');
            return;
        }

        if (!current_user_can('bizmanage_manage_finances')) {
            error_log('BizManagePro: ajax_save_category - permission denied for user: ' . get_current_user_id());
            wp_send_json_error('Permission denied.');
            return;
        }

        // Handle potential data parsing issues
        $raw_post = $_POST;

        // If $_POST is somehow corrupted, try to get raw input
        if (!isset($_POST['name']) || empty($_POST['name'])) {
            $raw_input = file_get_contents('php://input');
            if (!empty($raw_input)) {
                parse_str($raw_input, $parsed_data);
                if (!empty($parsed_data)) {
                    $raw_post = array_merge($_POST, $parsed_data);
                    error_log('BizManagePro: Used raw input parsing. Data: ' . json_encode($parsed_data));
                }
            }
        }

        $category_id = intval($raw_post['category_id'] ?? 0);
        $data = array(
            'name' => trim($raw_post['name'] ?? ''),
            'type' => $raw_post['type'] ?? '',
            'entity_id' => intval($raw_post['entity_id'] ?? 0),
            'description' => $raw_post['description'] ?? '',
            'color' => $raw_post['color'] ?? '#007cba'
        );

        // Additional logging for debugging
        error_log('BizManagePro: Parsed data - name: "' . $data['name'] . '", type: "' . $data['type'] . '", entity_id: ' . $data['entity_id']);

        // Validate required fields
        if (empty($data['name'])) {
            error_log('BizManagePro: ajax_save_category - missing category name');
            wp_send_json_error('Category name is required.');
            return;
        }

        if (empty($data['type']) || !in_array($data['type'], array('income', 'expense'))) {
            error_log('BizManagePro: ajax_save_category - invalid category type: ' . $data['type']);
            wp_send_json_error('Valid category type is required (income or expense).');
            return;
        }

        if ($data['entity_id'] <= 0) {
            error_log('BizManagePro: ajax_save_category - invalid entity ID: ' . $data['entity_id']);

            // Try to get a default entity for the user
            global $wpdb;
            $entities_table = $wpdb->prefix . 'bizmanage_business_entities';
            $default_entity = $wpdb->get_var("SELECT id FROM $entities_table WHERE status = 'active' ORDER BY id ASC LIMIT 1");

            if ($default_entity) {
                $data['entity_id'] = intval($default_entity);
                error_log('BizManagePro: ajax_save_category - using default entity ID: ' . $data['entity_id']);
            } else {
                wp_send_json_error('Valid entity ID is required. Please select a business entity.');
                return;
            }
        }

        if ($category_id > 0) {
            // Update existing category
            if ($this->update_category($category_id, $data)) {
                wp_send_json_success('Category updated successfully.');
            } else {
                wp_send_json_error('Failed to update category.');
            }
        } else {
            // Create new category
            $result = $this->create_category($data);
            if ($result) {
                $category = $this->get_category($result);
                wp_send_json_success(array(
                    'message' => 'Category created successfully.',
                    'category_id' => $result,
                    'category' => $category
                ));
            } else {
                // Check if it's a duplicate
                $existing = $this->get_categories($data['entity_id'], $data['type']);
                $duplicate_found = false;

                foreach ($existing as $cat) {
                    if (strtolower($cat->name) === strtolower($data['name'])) {
                        $duplicate_found = true;
                        break;
                    }
                }

                if ($duplicate_found) {
                    wp_send_json_error('A category with this name already exists for this entity.');
                } else {
                    wp_send_json_error('Failed to create category. Please check the data and try again.');
                }
            }
        }
    }

    /**
     * AJAX handler for deleting categories
     */
    public function ajax_delete_category() {
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'bizmanage_delete_category')) {
            wp_send_json_error('Security check failed.');
            return;
        }

        if (!current_user_can('bizmanage_manage_finances')) {
            wp_send_json_error('Permission denied.');
            return;
        }

        $category_id = intval($_POST['category_id'] ?? 0);
        
        if ($this->delete_category($category_id)) {
            wp_send_json_success('Category deleted successfully.');
        } else {
            wp_send_json_error('Failed to delete category.');
        }
    }

    /**
     * AJAX handler for getting categories
     */
    public function ajax_get_categories() {
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'bizmanage_get_categories')) {
            wp_send_json_error('Security check failed.');
            return;
        }

        if (!current_user_can('bizmanage_view_finances')) {
            wp_send_json_error('Permission denied.');
            return;
        }

        $entity_id = intval($_POST['entity_id'] ?? 0);
        $type = sanitize_text_field($_POST['type'] ?? '');

        // Validate entity_id
        if ($entity_id <= 0) {
            wp_send_json_error('Invalid entity ID.');
            return;
        }

        // Validate type
        if (!empty($type) && !in_array($type, array('income', 'expense'))) {
            wp_send_json_error('Invalid category type.');
            return;
        }

        try {
            $categories = $this->get_categories($entity_id, $type);
            wp_send_json_success($categories);
        } catch (Exception $e) {
            error_log('BizManagePro: Error getting categories: ' . $e->getMessage());
            wp_send_json_error('Failed to retrieve categories.');
        }
    }
}

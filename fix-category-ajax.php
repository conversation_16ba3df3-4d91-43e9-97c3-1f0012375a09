<?php
/**
 * Fix Category AJAX Issues
 * 
 * This script will fix category AJAX problems
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Fix Category AJAX Issues</h1>';

// Test AJAX category save with proper data
echo '<h2>1. Test Category AJAX Save</h2>';

if (isset($_POST['test_ajax_save'])) {
    // Simulate proper AJAX request
    $entity_id = intval($_POST['test_entity_id']);
    $category_name = sanitize_text_field($_POST['test_category_name']);
    $category_type = sanitize_text_field($_POST['test_category_type']);
    
    if ($entity_id && $category_name && $category_type) {
        // Create nonce
        $nonce = wp_create_nonce('bizmanage_save_category');
        
        // Prepare data exactly as AJAX would send
        $ajax_data = array(
            'action' => 'bizmanage_save_category',
            'nonce' => $nonce,
            'entity_id' => $entity_id,
            'name' => $category_name,
            'type' => $category_type,
            'description' => 'Test category via AJAX fix',
            'color' => '#007cba'
        );
        
        echo '<h3>AJAX Data Being Sent:</h3>';
        echo '<pre>' . print_r($ajax_data, true) . '</pre>';
        
        // Simulate $_POST data
        $_POST = $ajax_data;
        
        // Test if BizManage_Pro_Categories class exists and has ajax_save_category method
        if (class_exists('BizManage_Pro_Categories')) {
            $categories_instance = BizManage_Pro_Categories::instance();
            
            if (method_exists($categories_instance, 'ajax_save_category')) {
                echo '<h3>Calling ajax_save_category method...</h3>';
                
                // Capture output
                ob_start();
                $categories_instance->ajax_save_category();
                $output = ob_get_clean();
                
                echo '<h3>AJAX Method Output:</h3>';
                echo '<pre>' . htmlspecialchars($output) . '</pre>';
                
                // Check if category was created
                global $wpdb;
                $categories_table = $wpdb->prefix . 'bizmanage_categories';
                $created_category = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $categories_table WHERE entity_id = %d AND name = %s AND type = %s ORDER BY id DESC LIMIT 1",
                    $entity_id, $category_name, $category_type
                ));
                
                if ($created_category) {
                    echo '<p style="color: green;">✅ Category created successfully in database!</p>';
                    echo '<pre>' . print_r($created_category, true) . '</pre>';
                } else {
                    echo '<p style="color: red;">❌ Category not found in database</p>';
                }
            } else {
                echo '<p style="color: red;">❌ ajax_save_category method not found</p>';
            }
        } else {
            echo '<p style="color: red;">❌ BizManage_Pro_Categories class not found</p>';
        }
    } else {
        echo '<p style="color: red;">❌ Missing required fields</p>';
    }
}

// Get entities for testing
global $wpdb;
$entities_table = $wpdb->prefix . 'bizmanage_business_entities';
$entities = $wpdb->get_results("SELECT id, business_name FROM $entities_table WHERE status = 'active'");

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<table>';
    echo '<tr><td>Entity:</td><td><select name="test_entity_id" required>';
    foreach ($entities as $entity) {
        echo '<option value="' . $entity->id . '">' . $entity->business_name . '</option>';
    }
    echo '</select></td></tr>';
    echo '<tr><td>Category Name:</td><td><input type="text" name="test_category_name" value="AJAX Test ' . time() . '" required></td></tr>';
    echo '<tr><td>Type:</td><td><select name="test_category_type" required>';
    echo '<option value="income">Income</option>';
    echo '<option value="expense">Expense</option>';
    echo '</select></td></tr>';
    echo '<tr><td colspan="2"><button type="submit" name="test_ajax_save" value="1">Test AJAX Category Save</button></td></tr>';
    echo '</table>';
    echo '</form>';
}

// Check AJAX handlers registration
echo '<h2>2. AJAX Handlers Check</h2>';

$ajax_actions = array(
    'bizmanage_save_category',
    'bizmanage_get_categories',
    'bizmanage_delete_category'
);

foreach ($ajax_actions as $action) {
    $has_action = has_action('wp_ajax_' . $action);
    $has_nopriv = has_action('wp_ajax_nopriv_' . $action);
    
    echo '<p><strong>' . $action . ':</strong> ';
    if ($has_action) {
        echo '<span style="color: green;">✅ Registered for logged-in users</span>';
    } else {
        echo '<span style="color: red;">❌ Not registered for logged-in users</span>';
    }
    
    if ($has_nopriv) {
        echo ' | <span style="color: green;">✅ Registered for non-logged-in users</span>';
    } else {
        echo ' | <span style="color: orange;">⚠️ Not registered for non-logged-in users</span>';
    }
    echo '</p>';
}

// Check nonce system
echo '<h2>3. Nonce System Check</h2>';

$test_nonce = wp_create_nonce('bizmanage_save_category');
echo '<p><strong>Generated Nonce:</strong> ' . $test_nonce . '</p>';

$verify_result = wp_verify_nonce($test_nonce, 'bizmanage_save_category');
echo '<p><strong>Nonce Verification:</strong> ' . ($verify_result ? '✅ Valid' : '❌ Invalid') . '</p>';

// Check security class
if (class_exists('BizManage_Pro_Security')) {
    $security = BizManage_Pro_Security::instance();
    if (method_exists($security, 'verify_nonce')) {
        $security_verify = $security->verify_nonce($test_nonce, 'bizmanage_save_category');
        echo '<p><strong>Security Class Verification:</strong> ' . ($security_verify ? '✅ Valid' : '❌ Invalid') . '</p>';
    } else {
        echo '<p><strong>Security Class:</strong> ❌ verify_nonce method not found</p>';
    }
} else {
    echo '<p><strong>Security Class:</strong> ❌ BizManage_Pro_Security not found</p>';
}

// JavaScript debugging
echo '<h2>4. JavaScript Debugging</h2>';
echo '<p>Open browser console (F12) and run this JavaScript to test AJAX:</p>';

if (!empty($entities)) {
    $entity_id = $entities[0]->id;
    echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;">';
    echo '<pre>';
    echo "// Test category creation via JavaScript\n";
    echo "var testData = {\n";
    echo "    action: 'bizmanage_save_category',\n";
    echo "    nonce: '" . wp_create_nonce('bizmanage_save_category') . "',\n";
    echo "    entity_id: " . $entity_id . ",\n";
    echo "    name: 'JS Test Category " . time() . "',\n";
    echo "    type: 'income',\n";
    echo "    description: 'Test from JavaScript',\n";
    echo "    color: '#28a745'\n";
    echo "};\n\n";
    echo "jQuery.post(ajaxurl, testData, function(response) {\n";
    echo "    console.log('AJAX Response:', response);\n";
    echo "}).fail(function(xhr, status, error) {\n";
    echo "    console.log('AJAX Error:', error);\n";
    echo "    console.log('Response Text:', xhr.responseText);\n";
    echo "});";
    echo '</pre>';
    echo '</div>';
}

// Check current categories
echo '<h2>5. Current Categories</h2>';
$categories_table = $wpdb->prefix . 'bizmanage_categories';
$categories = $wpdb->get_results("SELECT * FROM $categories_table ORDER BY entity_id, type, name");

if (!empty($categories)) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Entity ID</th><th>Name</th><th>Type</th><th>Status</th><th>Created</th></tr>';
    foreach ($categories as $cat) {
        echo '<tr>';
        echo '<td>' . $cat->id . '</td>';
        echo '<td>' . $cat->entity_id . '</td>';
        echo '<td>' . $cat->name . '</td>';
        echo '<td>' . $cat->type . '</td>';
        echo '<td>' . $cat->status . '</td>';
        echo '<td>' . $cat->created_at . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p>No categories found in database.</p>';
}

// Debug log check
echo '<h2>6. Recent Debug Log Entries</h2>';
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -20);
    
    $bizmanage_lines = array();
    foreach ($recent_lines as $line) {
        if (strpos($line, 'BizManagePro') !== false || strpos($line, 'bizmanage') !== false) {
            $bizmanage_lines[] = $line;
        }
    }
    
    if (!empty($bizmanage_lines)) {
        echo '<div style="background: #f0f0f0; padding: 10px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">';
        foreach ($bizmanage_lines as $line) {
            echo htmlspecialchars($line) . '<br>';
        }
        echo '</div>';
    } else {
        echo '<p>No recent BizManage Pro related log entries.</p>';
    }
} else {
    echo '<p>Debug log file not found.</p>';
}

// Quick fixes
echo '<h2>7. Quick Fixes</h2>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '" target="_blank">Open BizManage Pro Dashboard</a></p>';
echo '<p><a href="check-debug-log.php">Check Debug Log</a></p>';
echo '<p><a href="fix-category-system.php">Run Category System Fix</a></p>';
?>

<?php
/**
 * Fix Database Connection for WordPress
 * 
 * This script will help diagnose and fix database connection issues
 */

echo '<h1>Database Connection Troubleshoot</h1>';

// Test MySQL connection without WordPress
echo '<h2>1. MySQL Connection Test</h2>';

// Common XAMPP database settings
$test_configs = array(
    array(
        'host' => 'localhost',
        'username' => 'root',
        'password' => '',
        'database' => 'wordpress'
    ),
    array(
        'host' => 'localhost',
        'username' => 'root',
        'password' => 'root',
        'database' => 'wordpress'
    ),
    array(
        'host' => '127.0.0.1',
        'username' => 'root',
        'password' => '',
        'database' => 'wordpress'
    ),
    array(
        'host' => 'localhost',
        'username' => 'musfikur',
        'password' => '',
        'database' => 'wordpress'
    )
);

$working_config = null;

foreach ($test_configs as $index => $config) {
    echo '<h3>Testing Configuration ' . ($index + 1) . '</h3>';
    echo '<p><strong>Host:</strong> ' . $config['host'] . '</p>';
    echo '<p><strong>Username:</strong> ' . $config['username'] . '</p>';
    echo '<p><strong>Password:</strong> ' . ($config['password'] ? '[SET]' : '[EMPTY]') . '</p>';
    echo '<p><strong>Database:</strong> ' . $config['database'] . '</p>';
    
    // Test connection
    $connection = @mysqli_connect($config['host'], $config['username'], $config['password']);
    
    if ($connection) {
        echo '<p style="color: green;">✅ MySQL connection successful!</p>';
        
        // Test database selection
        $db_selected = @mysqli_select_db($connection, $config['database']);
        if ($db_selected) {
            echo '<p style="color: green;">✅ Database "' . $config['database'] . '" exists and accessible!</p>';
            $working_config = $config;
            mysqli_close($connection);
            break;
        } else {
            echo '<p style="color: orange;">⚠️ Database "' . $config['database'] . '" not found. Will create it.</p>';
            
            // Try to create database
            $create_db = @mysqli_query($connection, "CREATE DATABASE IF NOT EXISTS `" . $config['database'] . "`");
            if ($create_db) {
                echo '<p style="color: green;">✅ Database "' . $config['database'] . '" created successfully!</p>';
                $working_config = $config;
                mysqli_close($connection);
                break;
            } else {
                echo '<p style="color: red;">❌ Failed to create database: ' . mysqli_error($connection) . '</p>';
            }
        }
        
        mysqli_close($connection);
    } else {
        echo '<p style="color: red;">❌ MySQL connection failed: ' . mysqli_connect_error() . '</p>';
    }
    
    echo '<hr>';
}

if ($working_config) {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>✅ Working Database Configuration Found!</h3>';
    echo '<p><strong>Host:</strong> ' . $working_config['host'] . '</p>';
    echo '<p><strong>Username:</strong> ' . $working_config['username'] . '</p>';
    echo '<p><strong>Password:</strong> ' . ($working_config['password'] ? $working_config['password'] : '[EMPTY]') . '</p>';
    echo '<p><strong>Database:</strong> ' . $working_config['database'] . '</p>';
    echo '</div>';
    
    // Generate wp-config.php database section
    echo '<h2>2. wp-config.php Database Settings</h2>';
    echo '<p>Copy and paste this into your wp-config.php file (replace existing database settings):</p>';
    
    echo '<div style="background: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace;">';
    echo '<pre>';
    echo "// ** MySQL settings - You can get this info from your web host ** //\n";
    echo "/** The name of the database for WordPress */\n";
    echo "define( 'DB_NAME', '" . $working_config['database'] . "' );\n\n";
    echo "/** MySQL database username */\n";
    echo "define( 'DB_USER', '" . $working_config['username'] . "' );\n\n";
    echo "/** MySQL database password */\n";
    echo "define( 'DB_PASSWORD', '" . $working_config['password'] . "' );\n\n";
    echo "/** MySQL hostname */\n";
    echo "define( 'DB_HOST', '" . $working_config['host'] . "' );\n\n";
    echo "/** Database Charset to use in creating database tables. */\n";
    echo "define( 'DB_CHARSET', 'utf8mb4' );\n\n";
    echo "/** The Database Collate type. Don't change this if in doubt. */\n";
    echo "define( 'DB_COLLATE', '' );";
    echo '</pre>';
    echo '</div>';
    
    // Auto-fix option
    echo '<h2>3. Auto-Fix wp-config.php</h2>';
    
    if (isset($_POST['auto_fix'])) {
        $wp_config_path = dirname(__FILE__) . '/wp-config.php';
        
        if (file_exists($wp_config_path)) {
            $wp_config_content = file_get_contents($wp_config_path);
            
            // Replace database settings
            $patterns = array(
                "/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/",
                "/define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/",
                "/define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/",
                "/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/"
            );
            
            $replacements = array(
                "define( 'DB_NAME', '" . $working_config['database'] . "' );",
                "define( 'DB_USER', '" . $working_config['username'] . "' );",
                "define( 'DB_PASSWORD', '" . $working_config['password'] . "' );",
                "define( 'DB_HOST', '" . $working_config['host'] . "' );"
            );
            
            $new_content = preg_replace($patterns, $replacements, $wp_config_content);
            
            if (file_put_contents($wp_config_path, $new_content)) {
                echo '<p style="color: green;">✅ wp-config.php updated successfully!</p>';
                echo '<p><a href="setup-database.php" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Test Database Connection</a></p>';
            } else {
                echo '<p style="color: red;">❌ Failed to update wp-config.php. Please update manually.</p>';
            }
        } else {
            echo '<p style="color: red;">❌ wp-config.php file not found.</p>';
        }
    } else {
        echo '<form method="post">';
        echo '<button type="submit" name="auto_fix" value="1" style="background: #007cba; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">Auto-Fix wp-config.php</button>';
        echo '</form>';
        echo '<p style="color: orange;"><strong>Warning:</strong> This will modify your wp-config.php file. Make a backup first!</p>';
    }
    
} else {
    echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>❌ No Working Database Configuration Found</h3>';
    echo '<p>Please check the following:</p>';
    echo '<ul>';
    echo '<li>Is XAMPP MySQL service running?</li>';
    echo '<li>Is the database "wordpress" created in phpMyAdmin?</li>';
    echo '<li>Are the database credentials correct in wp-config.php?</li>';
    echo '</ul>';
    echo '</div>';
}

// XAMPP Status Check
echo '<h2>4. XAMPP Status Check</h2>';

// Check if XAMPP MySQL is running (Windows)
$mysql_running = false;
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    $output = shell_exec('tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL');
    if (strpos($output, 'mysqld.exe') !== false) {
        $mysql_running = true;
    }
}

if ($mysql_running) {
    echo '<p style="color: green;">✅ MySQL service is running</p>';
} else {
    echo '<p style="color: red;">❌ MySQL service not detected</p>';
    echo '<p><strong>To start MySQL in XAMPP:</strong></p>';
    echo '<ol>';
    echo '<li>Open XAMPP Control Panel</li>';
    echo '<li>Click "Start" next to MySQL</li>';
    echo '<li>Wait for it to turn green</li>';
    echo '</ol>';
}

// phpMyAdmin link
echo '<h2>5. Database Management</h2>';
echo '<p><a href="http://localhost/phpmyadmin" target="_blank" style="background: #17a2b8; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Open phpMyAdmin</a></p>';
echo '<p>Use phpMyAdmin to:</p>';
echo '<ul>';
echo '<li>Check if MySQL is running</li>';
echo '<li>Create "wordpress" database if it doesn\'t exist</li>';
echo '<li>Check user permissions</li>';
echo '</ul>';

// Next steps
echo '<h2>6. Next Steps</h2>';
echo '<ol>';
echo '<li>Fix the database connection using the settings above</li>';
echo '<li><a href="setup-database.php">Run Database Setup</a></li>';
echo '<li><a href="troubleshoot-bizmanage.php">Run Plugin Troubleshoot</a></li>';
echo '</ol>';
?>

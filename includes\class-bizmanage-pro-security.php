<?php
/**
 * BizManage Pro Security Class
 *
 * Handles security operations, validation, and sanitization
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Security Class
 */
class BizManage_Pro_Security {

    /**
     * Instance of this class
     * @var BizManage_Pro_Security
     */
    private static $instance = null;

    /**
     * Encryption key
     * @var string
     */
    private $encryption_key;

    /**
     * Get instance
     * @return BizManage_Pro_Security
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_encryption_key();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init_security_headers'));
    }

    /**
     * Initialize encryption key
     */
    private function init_encryption_key() {
        $this->encryption_key = get_option('bizmanage_pro_encryption_key');
        
        if (empty($this->encryption_key)) {
            $this->encryption_key = $this->generate_encryption_key();
            update_option('bizmanage_pro_encryption_key', $this->encryption_key);
        }
    }

    /**
     * Generate encryption key
     * @return string
     */
    private function generate_encryption_key() {
        return base64_encode(random_bytes(32));
    }

    /**
     * Initialize security headers
     */
    public function init_security_headers() {
        if (is_admin() && strpos($_SERVER['REQUEST_URI'], 'bizmanage') !== false) {
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
            header('X-XSS-Protection: 1; mode=block');
        }
    }

    /**
     * Verify nonce
     * @param string $nonce
     * @param string $action
     * @return bool
     */
    public function verify_nonce($nonce, $action) {
        return wp_verify_nonce($nonce, $action);
    }

    /**
     * Create nonce
     * @param string $action
     * @return string
     */
    public function create_nonce($action) {
        return wp_create_nonce($action);
    }

    /**
     * Check user capability
     * @param string $capability
     * @param int $user_id
     * @return bool
     */
    public function check_capability($capability, $user_id = null) {
        if ($user_id) {
            return user_can($user_id, $capability);
        }
        return current_user_can($capability);
    }

    /**
     * Sanitize input data
     * @param mixed $data
     * @param string $type
     * @return mixed
     */
    public function sanitize_input($data, $type = 'text') {
        switch ($type) {
            case 'email':
                return sanitize_email($data);
            case 'url':
                return esc_url_raw($data);
            case 'int':
                return intval($data);
            case 'float':
                return floatval($data);
            case 'textarea':
                return sanitize_textarea_field($data);
            case 'html':
                return wp_kses_post($data);
            case 'key':
                return sanitize_key($data);
            case 'slug':
                return sanitize_title($data);
            case 'filename':
                return sanitize_file_name($data);
            case 'text':
            default:
                return sanitize_text_field($data);
        }
    }

    /**
     * Validate input data
     * @param mixed $data
     * @param string $type
     * @param array $rules
     * @return array
     */
    public function validate_input($data, $type, $rules = array()) {
        $errors = array();

        switch ($type) {
            case 'email':
                if (!is_email($data)) {
                    $errors[] = __('Invalid email address.', 'bizmanage-pro');
                }
                break;

            case 'url':
                if (!filter_var($data, FILTER_VALIDATE_URL)) {
                    $errors[] = __('Invalid URL.', 'bizmanage-pro');
                }
                break;

            case 'phone':
                if (!preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $data)) {
                    $errors[] = __('Invalid phone number.', 'bizmanage-pro');
                }
                break;

            case 'date':
                if (!$this->validate_date($data)) {
                    $errors[] = __('Invalid date format.', 'bizmanage-pro');
                }
                break;

            case 'currency':
                if (!preg_match('/^\d+(\.\d{1,2})?$/', $data)) {
                    $errors[] = __('Invalid currency amount.', 'bizmanage-pro');
                }
                break;

            case 'required':
                if (empty($data)) {
                    $errors[] = __('This field is required.', 'bizmanage-pro');
                }
                break;
        }

        // Check additional rules
        if (!empty($rules)) {
            if (isset($rules['min_length']) && strlen($data) < $rules['min_length']) {
                $errors[] = sprintf(__('Minimum length is %d characters.', 'bizmanage-pro'), $rules['min_length']);
            }

            if (isset($rules['max_length']) && strlen($data) > $rules['max_length']) {
                $errors[] = sprintf(__('Maximum length is %d characters.', 'bizmanage-pro'), $rules['max_length']);
            }

            if (isset($rules['min_value']) && floatval($data) < $rules['min_value']) {
                $errors[] = sprintf(__('Minimum value is %s.', 'bizmanage-pro'), $rules['min_value']);
            }

            if (isset($rules['max_value']) && floatval($data) > $rules['max_value']) {
                $errors[] = sprintf(__('Maximum value is %s.', 'bizmanage-pro'), $rules['max_value']);
            }
        }

        return $errors;
    }

    /**
     * Validate date
     * @param string $date
     * @param string $format
     * @return bool
     */
    public function validate_date($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    /**
     * Encrypt data
     * @param string $data
     * @return string|false
     */
    public function encrypt_data($data) {
        if (empty($data)) {
            return false;
        }

        $key = base64_decode($this->encryption_key);
        $iv = random_bytes(16);
        
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        if ($encrypted === false) {
            return false;
        }

        return base64_encode($iv . $encrypted);
    }

    /**
     * Decrypt data
     * @param string $encrypted_data
     * @return string|false
     */
    public function decrypt_data($encrypted_data) {
        if (empty($encrypted_data)) {
            return false;
        }

        $data = base64_decode($encrypted_data);
        $key = base64_decode($this->encryption_key);
        
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }

    /**
     * Hash password
     * @param string $password
     * @return string
     */
    public function hash_password($password) {
        return wp_hash_password($password);
    }

    /**
     * Verify password
     * @param string $password
     * @param string $hash
     * @return bool
     */
    public function verify_password($password, $hash) {
        return wp_check_password($password, $hash);
    }

    /**
     * Generate secure token
     * @param int $length
     * @return string
     */
    public function generate_token($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Validate file upload
     * @param array $file
     * @return array
     */
    public function validate_file_upload($file) {
        $errors = array();

        if (empty($file) || $file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = __('File upload failed.', 'bizmanage-pro');
            return $errors;
        }

        // Check file size
        $max_size = get_option('bizmanage_pro_max_file_size', 10485760); // 10MB default
        if ($file['size'] > $max_size) {
            $errors[] = sprintf(__('File size exceeds maximum allowed size of %s.', 'bizmanage-pro'), size_format($max_size));
        }

        // Check file type
        $allowed_types = explode(',', get_option('bizmanage_pro_allowed_file_types', 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png'));
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($file_extension, $allowed_types)) {
            $errors[] = sprintf(__('File type %s is not allowed.', 'bizmanage-pro'), $file_extension);
        }

        // Check MIME type
        $allowed_mimes = array(
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
        );

        if (isset($allowed_mimes[$file_extension])) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mime_type = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);

            if ($mime_type !== $allowed_mimes[$file_extension]) {
                $errors[] = __('File type does not match file content.', 'bizmanage-pro');
            }
        }

        return $errors;
    }

    /**
     * Log security event
     * @param string $event
     * @param array $data
     */
    public function log_security_event($event, $data = array()) {
        $log_data = array(
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'event' => $event,
            'data' => $data,
        );

        error_log('BizManage Pro Security Event: ' . json_encode($log_data));
    }

    /**
     * Get client IP address
     * @return string
     */
    public function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

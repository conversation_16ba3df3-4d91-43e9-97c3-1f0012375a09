<?php
/**
 * BizManage Pro Settings Template
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get instances
$settings_manager = BizManage_Pro_Settings::instance();
$admin = BizManage_Pro_Admin::instance();
$security = BizManage_Pro_Security::instance();

// Get current section
$current_section = isset($_GET['section']) ? sanitize_text_field($_GET['section']) : 'general';

// Get available sections
$sections = $settings_manager->get_sections();

// Get current entity (for entity-specific settings)
$entities = $admin->get_user_entities_dropdown();
$selected_entity_id = isset($_GET['entity_id']) ? intval($_GET['entity_id']) : 0;

// Get settings for current section
$current_settings = $settings_manager->get_section($current_section, $selected_entity_id ?: null);
$field_config = $settings_manager->get_field_config($current_section);
?>

<div class="wrap bizmanage-admin-wrap">
    <div class="bizmanage-header">
        <h1><?php _e('BizManage Pro Settings', 'bizmanage-pro'); ?></h1>
        <p><?php _e('Configure your BizManage Pro installation and customize settings for your business needs.', 'bizmanage-pro'); ?></p>
    </div>

    <div class="bizmanage-settings-container">
        
        <!-- Settings Navigation -->
        <div class="bizmanage-settings-nav">
            <h3><?php _e('Settings Sections', 'bizmanage-pro'); ?></h3>
            <ul class="settings-nav-list">
                <?php foreach ($sections as $section_key => $section_label): ?>
                    <li class="<?php echo $current_section === $section_key ? 'active' : ''; ?>">
                        <a href="<?php echo admin_url('admin.php?page=bizmanage-settings&section=' . $section_key . ($selected_entity_id ? '&entity_id=' . $selected_entity_id : '')); ?>">
                            <?php echo esc_html($section_label); ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>

            <!-- Entity Selector for Entity-Specific Settings -->
            <?php if (!empty($entities) && in_array($current_section, array('general', 'financial', 'documents'))): ?>
                <div class="entity-settings-selector">
                    <h4><?php _e('Entity-Specific Settings', 'bizmanage-pro'); ?></h4>
                    <select id="entity-settings-select" class="form-select">
                        <option value="0"><?php _e('Global Settings', 'bizmanage-pro'); ?></option>
                        <?php foreach ($entities as $id => $name): ?>
                            <option value="<?php echo esc_attr($id); ?>" <?php selected($selected_entity_id, $id); ?>>
                                <?php echo esc_html($name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="description">
                        <?php _e('Choose an entity to configure entity-specific settings, or select "Global Settings" for system-wide configuration.', 'bizmanage-pro'); ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Settings Content -->
        <div class="bizmanage-settings-content">
            
            <!-- Section Header -->
            <div class="settings-section-header">
                <h2><?php echo esc_html($sections[$current_section]); ?></h2>
                <?php if ($selected_entity_id): ?>
                    <p class="entity-indicator">
                        <?php printf(__('Configuring settings for: %s', 'bizmanage-pro'), '<strong>' . esc_html($entities[$selected_entity_id]) . '</strong>'); ?>
                    </p>
                <?php endif; ?>
            </div>

            <!-- Settings Form -->
            <form id="settings-form" class="bizmanage-form">
                <?php wp_nonce_field('bizmanage_save_settings', 'settings_nonce'); ?>
                <input type="hidden" name="section" value="<?php echo esc_attr($current_section); ?>">
                <input type="hidden" name="entity_id" value="<?php echo esc_attr($selected_entity_id); ?>">

                <?php if (!empty($field_config)): ?>
                    
                    <?php foreach ($field_config as $field_key => $field): ?>
                        <div class="form-group">
                            <label for="<?php echo esc_attr($field_key); ?>">
                                <?php echo esc_html($field['label']); ?>
                                <?php if (isset($field['required']) && $field['required']): ?>
                                    <span class="required">*</span>
                                <?php endif; ?>
                            </label>

                            <?php
                            $field_name = 'settings[' . $field_key . ']';
                            $field_value = isset($current_settings[$field_key]) ? $current_settings[$field_key] : '';
                            $field_id = esc_attr($field_key);
                            ?>

                            <?php switch ($field['type']): 
                                case 'text': ?>
                                    <input type="text" 
                                           id="<?php echo $field_id; ?>" 
                                           name="<?php echo esc_attr($field_name); ?>" 
                                           value="<?php echo esc_attr($field_value); ?>"
                                           placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
                                           <?php echo isset($field['required']) && $field['required'] ? 'required' : ''; ?>>
                                    <?php break;

                                case 'number': ?>
                                    <input type="number" 
                                           id="<?php echo $field_id; ?>" 
                                           name="<?php echo esc_attr($field_name); ?>" 
                                           value="<?php echo esc_attr($field_value); ?>"
                                           step="<?php echo esc_attr($field['step'] ?? '1'); ?>"
                                           min="<?php echo esc_attr($field['min'] ?? ''); ?>"
                                           max="<?php echo esc_attr($field['max'] ?? ''); ?>"
                                           <?php echo isset($field['required']) && $field['required'] ? 'required' : ''; ?>>
                                    <?php break;

                                case 'email': ?>
                                    <input type="email" 
                                           id="<?php echo $field_id; ?>" 
                                           name="<?php echo esc_attr($field_name); ?>" 
                                           value="<?php echo esc_attr($field_value); ?>"
                                           <?php echo isset($field['required']) && $field['required'] ? 'required' : ''; ?>>
                                    <?php break;

                                case 'select': ?>
                                    <select id="<?php echo $field_id; ?>" 
                                            name="<?php echo esc_attr($field_name); ?>"
                                            <?php echo isset($field['required']) && $field['required'] ? 'required' : ''; ?>>
                                        <?php if (isset($field['options'])): ?>
                                            <?php foreach ($field['options'] as $option_value => $option_label): ?>
                                                <option value="<?php echo esc_attr($option_value); ?>" 
                                                        <?php selected($field_value, $option_value); ?>>
                                                    <?php 
                                                    if (is_array($option_label)) {
                                                        echo esc_html($option_label['name'] ?? $option_value);
                                                    } else {
                                                        echo esc_html($option_label);
                                                    }
                                                    ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                    <?php break;

                                case 'checkbox': ?>
                                    <label class="checkbox-label">
                                        <input type="checkbox" 
                                               id="<?php echo $field_id; ?>" 
                                               name="<?php echo esc_attr($field_name); ?>" 
                                               value="1"
                                               <?php checked($field_value, '1'); ?>>
                                        <span class="checkmark"></span>
                                        <?php echo esc_html($field['label']); ?>
                                    </label>
                                    <?php break;

                                case 'textarea': ?>
                                    <textarea id="<?php echo $field_id; ?>" 
                                              name="<?php echo esc_attr($field_name); ?>" 
                                              rows="<?php echo esc_attr($field['rows'] ?? '5'); ?>"
                                              placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
                                              <?php echo isset($field['required']) && $field['required'] ? 'required' : ''; ?>><?php echo esc_textarea($field_value); ?></textarea>
                                    <?php break;

                            endswitch; ?>

                            <?php if (isset($field['description'])): ?>
                                <p class="description"><?php echo esc_html($field['description']); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" id="save-settings" class="bizmanage-btn bizmanage-btn-primary">
                            <?php _e('Save Settings', 'bizmanage-pro'); ?>
                        </button>
                        
                        <button type="button" id="reset-settings" class="bizmanage-btn bizmanage-btn-secondary">
                            <?php _e('Reset to Defaults', 'bizmanage-pro'); ?>
                        </button>

                        <?php if (current_user_can('manage_options')): ?>
                        <button type="button" id="export-settings" class="bizmanage-btn bizmanage-btn-secondary">
                            <?php _e('Export Settings', 'bizmanage-pro'); ?>
                        </button>
                        
                        <button type="button" id="import-settings" class="bizmanage-btn bizmanage-btn-secondary">
                            <?php _e('Import Settings', 'bizmanage-pro'); ?>
                        </button>
                        <?php endif; ?>
                    </div>

                <?php else: ?>
                    <div class="no-settings-message">
                        <p><?php _e('No settings available for this section.', 'bizmanage-pro'); ?></p>
                    </div>
                <?php endif; ?>
            </form>

            <!-- Settings Information -->
            <div class="settings-info">
                <?php switch ($current_section): 
                    case 'general': ?>
                        <div class="info-box">
                            <h4><?php _e('General Settings Information', 'bizmanage-pro'); ?></h4>
                            <p><?php _e('These settings control the basic behavior and appearance of BizManage Pro. Changes here will affect how dates, currencies, and other data are displayed throughout the application.', 'bizmanage-pro'); ?></p>
                        </div>
                        <?php break;

                    case 'financial': ?>
                        <div class="info-box">
                            <h4><?php _e('Financial Settings Information', 'bizmanage-pro'); ?></h4>
                            <p><?php _e('Configure financial-specific settings including fiscal year, tax rates, and currency handling. These settings directly impact financial calculations and reporting.', 'bizmanage-pro'); ?></p>
                        </div>
                        <?php break;

                    case 'documents': ?>
                        <div class="info-box">
                            <h4><?php _e('Document Settings Information', 'bizmanage-pro'); ?></h4>
                            <p><?php _e('Control document upload limits, file types, and security settings. Enable encryption for sensitive documents and configure versioning options.', 'bizmanage-pro'); ?></p>
                        </div>
                        <?php break;

                    case 'security': ?>
                        <div class="info-box warning">
                            <h4><?php _e('Security Settings Information', 'bizmanage-pro'); ?></h4>
                            <p><?php _e('These settings control security features and access controls. Changes to security settings may affect user access and system behavior. Please review carefully before making changes.', 'bizmanage-pro'); ?></p>
                        </div>
                        <?php break;

                    case 'backup': ?>
                        <div class="info-box">
                            <h4><?php _e('Backup Settings Information', 'bizmanage-pro'); ?></h4>
                            <p><?php _e('Configure automatic backup settings to protect your business data. Regular backups are essential for data recovery and business continuity.', 'bizmanage-pro'); ?></p>
                        </div>
                        <?php break;

                    case 'advanced': ?>
                        <div class="info-box warning">
                            <h4><?php _e('Advanced Settings Information', 'bizmanage-pro'); ?></h4>
                            <p><?php _e('These are advanced settings that should only be modified by experienced users. Incorrect settings may affect system performance or functionality.', 'bizmanage-pro'); ?></p>
                        </div>
                        <?php break;

                endswitch; ?>
            </div>

        </div>
    </div>
</div>

<!-- Import Settings Modal -->
<div id="import-settings-modal" class="bizmanage-modal" style="display: none;">
    <div class="bizmanage-modal-content">
        <span class="bizmanage-modal-close">&times;</span>
        <h3><?php _e('Import Settings', 'bizmanage-pro'); ?></h3>
        <form id="import-settings-form" enctype="multipart/form-data">
            <div class="form-group">
                <label for="settings-file"><?php _e('Select Settings File', 'bizmanage-pro'); ?></label>
                <input type="file" id="settings-file" name="settings_file" accept=".json" required>
                <p class="description"><?php _e('Select a JSON file exported from BizManage Pro settings.', 'bizmanage-pro'); ?></p>
            </div>
            <div class="form-actions">
                <button type="submit" class="bizmanage-btn bizmanage-btn-primary">
                    <?php _e('Import Settings', 'bizmanage-pro'); ?>
                </button>
                <button type="button" class="bizmanage-btn bizmanage-btn-secondary" onclick="BizManagePro.closeModal()">
                    <?php _e('Cancel', 'bizmanage-pro'); ?>
                </button>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    
    // Entity selector change handler
    $('#entity-settings-select').on('change', function() {
        var entityId = $(this).val();
        var currentUrl = new URL(window.location);
        currentUrl.searchParams.set('entity_id', entityId);
        window.location.href = currentUrl.toString();
    });
    
    // Save settings
    $('#save-settings').on('click', function() {
        var formData = $('#settings-form').serialize();
        
        BizManagePro.ajaxRequest('bizmanage_save_settings', formData, function(response) {
            if (response.success) {
                BizManagePro.showAlert(response.message, 'success');
            } else {
                BizManagePro.showAlert(response.message, 'error');
            }
        });
    });
    
    // Reset settings
    $('#reset-settings').on('click', function() {
        if (!confirm('<?php _e("Are you sure you want to reset all settings in this section to their default values?", "bizmanage-pro"); ?>')) {
            return;
        }
        
        var section = $('input[name="section"]').val();
        var entityId = $('input[name="entity_id"]').val();
        
        BizManagePro.ajaxRequest('bizmanage_reset_settings', {
            section: section,
            entity_id: entityId
        }, function(response) {
            if (response.success) {
                BizManagePro.showAlert(response.message, 'success');
                location.reload();
            } else {
                BizManagePro.showAlert(response.message, 'error');
            }
        });
    });
    
    // Export settings
    $('#export-settings').on('click', function() {
        var section = $('input[name="section"]').val();
        var entityId = $('input[name="entity_id"]').val();
        
        BizManagePro.ajaxRequest('bizmanage_export_settings', {
            section: section,
            entity_id: entityId
        }, function(response) {
            if (response.success && response.data.download_url) {
                var link = document.createElement('a');
                link.href = response.data.download_url;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                BizManagePro.showAlert(response.message || '<?php _e("Error exporting settings.", "bizmanage-pro"); ?>', 'error');
            }
        });
    });
    
    // Import settings
    $('#import-settings').on('click', function() {
        $('#import-settings-modal').show();
    });
    
    // Import settings form submission
    $('#import-settings-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        formData.append('action', 'bizmanage_import_settings');
        formData.append('nonce', bizmanageAjax.nonce);
        
        $.ajax({
            url: bizmanageAjax.ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function() {
                BizManagePro.showLoading();
            },
            success: function(response) {
                BizManagePro.hideLoading();
                BizManagePro.closeModal();
                
                if (response.success) {
                    BizManagePro.showAlert(response.message, 'success');
                    location.reload();
                } else {
                    BizManagePro.showAlert(response.message, 'error');
                }
            },
            error: function() {
                BizManagePro.hideLoading();
                BizManagePro.closeModal();
                BizManagePro.showAlert('<?php _e("Error importing settings.", "bizmanage-pro"); ?>', 'error');
            }
        });
    });
});
</script>

<style>
.bizmanage-settings-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 30px;
    margin-top: 20px;
}

.bizmanage-settings-nav {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    height: fit-content;
}

.bizmanage-settings-nav h3 {
    margin: 0 0 15px 0;
    color: #23282d;
    font-size: 16px;
}

.settings-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.settings-nav-list li {
    margin: 0 0 5px 0;
}

.settings-nav-list li a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    color: #0073aa;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.settings-nav-list li a:hover {
    background-color: #f0f0f1;
}

.settings-nav-list li.active a {
    background-color: #007cba;
    color: #fff;
}

.entity-settings-selector {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.entity-settings-selector h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #23282d;
}

.bizmanage-settings-content {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.settings-section-header {
    border-bottom: 2px solid #007cba;
    padding-bottom: 15px;
    margin-bottom: 30px;
}

.settings-section-header h2 {
    margin: 0 0 5px 0;
    color: #23282d;
}

.entity-indicator {
    margin: 0;
    color: #666;
    font-style: italic;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 8px;
}

.info-box {
    background: #f0f6fc;
    border: 1px solid #c3d9ed;
    border-radius: 4px;
    padding: 15px;
    margin-top: 30px;
}

.info-box.warning {
    background: #fff8e1;
    border-color: #ffcc02;
}

.info-box h4 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.info-box p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.no-settings-message {
    text-align: center;
    padding: 40px;
    color: #666;
}

@media (max-width: 768px) {
    .bizmanage-settings-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}
</style>

<?php
/**
 * Create Debug Log File
 * 
 * This script will manually create the debug.log file and test logging
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Create Debug Log File</h1>';

// Define log file path
$log_file = WP_CONTENT_DIR . '/debug.log';
$wp_content_dir = WP_CONTENT_DIR;

echo '<p><strong>WP_CONTENT_DIR:</strong> ' . $wp_content_dir . '</p>';
echo '<p><strong>Log file path:</strong> ' . $log_file . '</p>';

// Check if wp-content directory is writable
if (!is_writable($wp_content_dir)) {
    echo '<p style="color: red;">❌ wp-content directory is not writable!</p>';
    echo '<p>Please set permissions for: ' . $wp_content_dir . '</p>';
    exit;
} else {
    echo '<p style="color: green;">✅ wp-content directory is writable</p>';
}

// Create debug.log file if it doesn't exist
if (!file_exists($log_file)) {
    $created = file_put_contents($log_file, '');
    if ($created !== false) {
        echo '<p style="color: green;">✅ Debug log file created successfully!</p>';
    } else {
        echo '<p style="color: red;">❌ Failed to create debug log file</p>';
        exit;
    }
} else {
    echo '<p style="color: blue;">ℹ️ Debug log file already exists</p>';
}

// Check if file is writable
if (!is_writable($log_file)) {
    echo '<p style="color: red;">❌ Debug log file is not writable!</p>';
    echo '<p>Please set permissions for: ' . $log_file . '</p>';
    exit;
} else {
    echo '<p style="color: green;">✅ Debug log file is writable</p>';
}

// Test logging
echo '<h2>Test Logging</h2>';

// Test 1: Direct file write
$test_message = '[' . date('d-M-Y H:i:s UTC') . '] BizManagePro: Debug log test - Direct file write' . PHP_EOL;
$result1 = file_put_contents($log_file, $test_message, FILE_APPEND | LOCK_EX);

if ($result1 !== false) {
    echo '<p style="color: green;">✅ Direct file write successful</p>';
} else {
    echo '<p style="color: red;">❌ Direct file write failed</p>';
}

// Test 2: error_log function
error_log('BizManagePro: Debug log test - error_log function');
echo '<p style="color: blue;">ℹ️ error_log function called</p>';

// Test 3: WordPress logging
if (function_exists('error_log')) {
    error_log('BizManagePro: Debug log test - WordPress error_log at ' . current_time('mysql'));
    echo '<p style="color: blue;">ℹ️ WordPress error_log called</p>';
}

// Check file contents
echo '<h2>Debug Log Contents</h2>';
if (file_exists($log_file) && filesize($log_file) > 0) {
    $contents = file_get_contents($log_file);
    $lines = explode("\n", $contents);
    $recent_lines = array_slice($lines, -10); // Last 10 lines
    
    echo '<div style="background: #f0f0f0; padding: 10px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">';
    foreach ($recent_lines as $line) {
        if (trim($line)) {
            echo htmlspecialchars($line) . '<br>';
        }
    }
    echo '</div>';
    
    echo '<p><strong>File size:</strong> ' . filesize($log_file) . ' bytes</p>';
} else {
    echo '<p style="color: orange;">⚠️ Debug log file is empty or doesn\'t exist</p>';
}

// Check PHP error logging settings
echo '<h2>PHP Error Logging Settings</h2>';
echo '<p><strong>log_errors:</strong> ' . (ini_get('log_errors') ? 'On' : 'Off') . '</p>';
echo '<p><strong>error_log:</strong> ' . (ini_get('error_log') ?: 'Not set') . '</p>';
echo '<p><strong>display_errors:</strong> ' . (ini_get('display_errors') ? 'On' : 'Off') . '</p>';

// Set PHP error logging to our debug file
ini_set('log_errors', 1);
ini_set('error_log', $log_file);
echo '<p style="color: green;">✅ PHP error logging configured to use debug.log</p>';

// Test PHP error
if (isset($_GET['test_php_error'])) {
    trigger_error('BizManagePro: Test PHP error generated at ' . date('Y-m-d H:i:s'), E_USER_WARNING);
    echo '<p style="color: blue;">ℹ️ Test PHP error triggered</p>';
}

// WordPress debug constants check
echo '<h2>WordPress Debug Constants</h2>';
echo '<p><strong>WP_DEBUG:</strong> ' . (defined('WP_DEBUG') && WP_DEBUG ? 'true' : 'false') . '</p>';
echo '<p><strong>WP_DEBUG_LOG:</strong> ' . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'true' : 'false') . '</p>';
echo '<p><strong>WP_DEBUG_DISPLAY:</strong> ' . (defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY ? 'true' : 'false') . '</p>';
echo '<p><strong>SCRIPT_DEBUG:</strong> ' . (defined('SCRIPT_DEBUG') && SCRIPT_DEBUG ? 'true' : 'false') . '</p>';

if (!defined('WP_DEBUG') || !WP_DEBUG) {
    echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    echo '<h3>⚠️ WordPress Debug Not Enabled</h3>';
    echo '<p>Please add these lines to your wp-config.php file before the "/* That\'s all, stop editing! */" line:</p>';
    echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px;">';
    echo "// Enable WordPress debugging\n";
    echo "define('WP_DEBUG', true);\n";
    echo "define('WP_DEBUG_LOG', true);\n";
    echo "define('WP_DEBUG_DISPLAY', false);\n";
    echo "define('SCRIPT_DEBUG', true);\n\n";
    echo "// Optional: Log to a custom file\n";
    echo "ini_set('log_errors', 1);\n";
    echo "ini_set('error_log', WP_CONTENT_DIR . '/debug.log');";
    echo '</pre>';
    echo '</div>';
}

// Actions
echo '<h2>Actions</h2>';
echo '<p><a href="?test_php_error=1" style="background: #dc3545; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Generate Test PHP Error</a></p>';
echo '<p><a href="check-debug-log.php" style="background: #0073aa; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Check Debug Log</a></p>';
echo '<p><a href="troubleshoot-bizmanage.php" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">Run Full Troubleshoot</a></p>';

// Auto refresh
if (isset($_GET['auto_refresh'])) {
    echo '<meta http-equiv="refresh" content="5">';
    echo '<p style="color: blue;">Page will auto-refresh every 5 seconds...</p>';
    echo '<p><a href="?">Stop Auto Refresh</a></p>';
} else {
    echo '<p><a href="?auto_refresh=1">Enable Auto Refresh</a></p>';
}
?>

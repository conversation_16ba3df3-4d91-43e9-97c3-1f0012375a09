<?php
/**
 * BizManage Pro Finances Class
 *
 * Handles financial transactions, income/expense tracking, and financial calculations
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Finances Class
 */
class BizManage_Pro_Finances {

    /**
     * Instance of this class
     * @var BizManage_Pro_Finances
     */
    private static $instance = null;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Security instance
     * @var BizManage_Pro_Security
     */
    private $security;

    /**
     * Utilities instance
     * @var BizManage_Pro_Utilities
     */
    private $utilities;

    /**
     * Get instance
     * @return BizManage_Pro_Finances
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->db = BizManage_Pro_Database::instance();
        $this->security = BizManage_Pro_Security::instance();
        $this->utilities = BizManage_Pro_Utilities::instance();
    }

    /**
     * Create financial transaction
     * @param array $data
     * @return int|false
     */
    public function create_transaction($data) {
        // Validate transaction data
        $validation_errors = $this->validate_transaction_data($data);
        if (!empty($validation_errors)) {
            error_log('BizManagePro: Transaction validation failed: ' . implode(', ', $validation_errors));
            return false;
        }

        // Sanitize data
        $sanitized_data = $this->sanitize_transaction_data($data);

        // Add default values
        $sanitized_data['user_id'] = get_current_user_id();
        $sanitized_data['status'] = 'completed'; // Set to completed by default
        $sanitized_data['created_at'] = current_time('mysql');

        // Handle currency conversion
        if ($sanitized_data['currency'] !== $this->get_base_currency($sanitized_data['entity_id'])) {
            $exchange_rate = $this->utilities->convert_currency(1, $sanitized_data['currency'], $this->get_base_currency($sanitized_data['entity_id']));
            $sanitized_data['exchange_rate'] = $exchange_rate;
            $sanitized_data['base_amount'] = $sanitized_data['amount'] * $exchange_rate;
        } else {
            $sanitized_data['exchange_rate'] = 1.0000;
            $sanitized_data['base_amount'] = $sanitized_data['amount'];
        }

        // Calculate tax if applicable
        if (!empty($sanitized_data['tax_rate']) && $sanitized_data['tax_rate'] > 0) {
            $sanitized_data['tax_amount'] = $this->utilities->calculate_tax($sanitized_data['amount'], $sanitized_data['tax_rate']);
        }

        // Insert transaction
        $transaction_id = $this->db->insert('transactions', $sanitized_data);

        if ($transaction_id) {
            error_log('BizManagePro: Transaction created successfully with ID: ' . $transaction_id . ' - Amount: ' . $sanitized_data['amount'] . ' - Type: ' . $sanitized_data['transaction_type']);
            do_action('bizmanage_transaction_created', $transaction_id, $sanitized_data);
        } else {
            error_log('BizManagePro: Transaction creation failed - database insert error. Data: ' . print_r($sanitized_data, true));
        }

        return $transaction_id;
    }

    /**
     * Update transaction
     * @param int $transaction_id
     * @param array $data
     * @return bool
     */
    public function update_transaction($transaction_id, $data) {
        // Check permissions
        if (!$this->user_can_edit_transaction($transaction_id)) {
            return false;
        }

        // Validate data
        $validation_errors = $this->validate_transaction_data($data, $transaction_id);
        if (!empty($validation_errors)) {
            return false;
        }

        // Sanitize data
        $sanitized_data = $this->sanitize_transaction_data($data);
        $sanitized_data['updated_at'] = current_time('mysql');

        // Recalculate currency conversion if needed
        $transaction = $this->get_transaction($transaction_id);
        if ($sanitized_data['currency'] !== $this->get_base_currency($transaction->entity_id)) {
            $exchange_rate = $this->utilities->convert_currency(1, $sanitized_data['currency'], $this->get_base_currency($transaction->entity_id));
            $sanitized_data['exchange_rate'] = $exchange_rate;
            $sanitized_data['base_amount'] = $sanitized_data['amount'] * $exchange_rate;
        } else {
            $sanitized_data['exchange_rate'] = 1.0000;
            $sanitized_data['base_amount'] = $sanitized_data['amount'];
        }

        // Recalculate tax
        if (!empty($sanitized_data['tax_rate']) && $sanitized_data['tax_rate'] > 0) {
            $sanitized_data['tax_amount'] = $this->utilities->calculate_tax($sanitized_data['amount'], $sanitized_data['tax_rate']);
        }

        $result = $this->db->update('transactions', $sanitized_data, array('id' => $transaction_id));

        if ($result !== false) {
            do_action('bizmanage_transaction_updated', $transaction_id, $sanitized_data);
        }

        return $result !== false;
    }

    /**
     * Delete transaction
     * @param int $transaction_id
     * @return bool
     */
    public function delete_transaction($transaction_id) {
        // Check permissions
        if (!$this->user_can_edit_transaction($transaction_id)) {
            return false;
        }

        // Soft delete - update status
        $result = $this->db->update('transactions', 
            array('status' => 'deleted', 'updated_at' => current_time('mysql')), 
            array('id' => $transaction_id)
        );

        if ($result !== false) {
            do_action('bizmanage_transaction_deleted', $transaction_id);
        }

        return $result !== false;
    }

    /**
     * Get transaction by ID
     * @param int $transaction_id
     * @return object|null
     */
    public function get_transaction($transaction_id) {
        return $this->db->get_row('transactions', array('id' => $transaction_id));
    }

    /**
     * Get transactions
     * @param array $args
     * @return array
     */
    public function get_transactions($args = array()) {
        $defaults = array(
            'entity_id' => 0,
            'transaction_type' => '',
            'category' => '',
            'status' => 'completed',
            'start_date' => '',
            'end_date' => '',
            'order_by' => 'transaction_date',
            'order' => 'DESC',
            'limit' => '',
            'offset' => '',
        );

        $args = wp_parse_args($args, $defaults);

        $where_conditions = array();

        if (!empty($args['status'])) {
            $where_conditions['status'] = $args['status'];
        }

        if ($args['entity_id'] > 0) {
            $where_conditions['entity_id'] = $args['entity_id'];
        }

        if (!empty($args['transaction_type'])) {
            $where_conditions['transaction_type'] = $args['transaction_type'];
        }

        if (!empty($args['category'])) {
            $where_conditions['category'] = $args['category'];
        }

        $query_args = array(
            'where' => $where_conditions,
            'order_by' => $args['order_by'],
            'order' => $args['order']
        );

        if (!empty($args['limit'])) {
            $query_args['limit'] = $args['limit'];
        }

        if (!empty($args['offset'])) {
            $query_args['offset'] = $args['offset'];
        }

        $transactions = $this->db->get_results('transactions', $query_args);

        // Filter by date range if provided
        if (!empty($args['start_date']) || !empty($args['end_date'])) {
            $transactions = $this->filter_by_date_range($transactions, $args['start_date'], $args['end_date']);
        }

        return $transactions;
    }

    /**
     * Get financial summary
     * @param int $entity_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function get_financial_summary($entity_id, $start_date = '', $end_date = '') {
        $where_sql = "entity_id = %d AND status = 'completed'";
        $where_params = array($entity_id);

        if (!empty($start_date) && !empty($end_date)) {
            $where_sql .= " AND transaction_date BETWEEN %s AND %s";
            $where_params[] = $start_date;
            $where_params[] = $end_date;
        }

        // Get income total
        $income_sql = "SELECT SUM(base_amount) FROM {$this->db->get_table('transactions')} WHERE transaction_type = 'income' AND $where_sql";
        $income_total = $this->db->get_var($this->db->prepare($income_sql, ...$where_params));

        // Get expense total
        $expense_sql = "SELECT SUM(base_amount) FROM {$this->db->get_table('transactions')} WHERE transaction_type = 'expense' AND $where_sql";
        $expense_total = $this->db->get_var($this->db->prepare($expense_sql, ...$where_params));

        // Get transaction count
        $count_sql = "SELECT COUNT(*) FROM {$this->db->get_table('transactions')} WHERE $where_sql";
        $transaction_count = $this->db->get_var($this->db->prepare($count_sql, ...$where_params));

        return array(
            'income_total' => floatval($income_total),
            'expense_total' => floatval($expense_total),
            'net_profit' => floatval($income_total) - floatval($expense_total),
            'transaction_count' => intval($transaction_count),
            'start_date' => $start_date,
            'end_date' => $end_date,
        );
    }

    /**
     * Get category breakdown
     * @param int $entity_id
     * @param string $transaction_type
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function get_category_breakdown($entity_id, $transaction_type, $start_date = '', $end_date = '') {
        $where_sql = "entity_id = %d AND transaction_type = %s AND status = 'completed'";
        $where_params = array($entity_id, $transaction_type);

        if (!empty($start_date) && !empty($end_date)) {
            $where_sql .= " AND transaction_date BETWEEN %s AND %s";
            $where_params[] = $start_date;
            $where_params[] = $end_date;
        }

        $sql = "SELECT category, SUM(base_amount) as total, COUNT(*) as count 
                FROM {$this->db->get_table('transactions')} 
                WHERE $where_sql 
                GROUP BY category 
                ORDER BY total DESC";

        return $this->db->get_query_results($this->db->prepare($sql, ...$where_params));
    }

    /**
     * Create recurring transaction
     * @param array $data
     * @return int|false
     */
    public function create_recurring_transaction($data) {
        // Validate recurring transaction data
        $validation_errors = $this->validate_recurring_data($data);
        if (!empty($validation_errors)) {
            return false;
        }

        // Sanitize data
        $sanitized_data = $this->sanitize_recurring_data($data);

        // Add default values
        $sanitized_data['user_id'] = get_current_user_id();
        $sanitized_data['status'] = 'active';
        $sanitized_data['generated_count'] = 0;
        $sanitized_data['created_at'] = current_time('mysql');

        // Calculate next date
        $sanitized_data['next_date'] = $this->calculate_next_date($sanitized_data['start_date'], $sanitized_data['frequency'], $sanitized_data['interval_value']);

        // Insert recurring transaction
        $recurring_id = $this->db->insert('recurring_transactions', $sanitized_data);

        if ($recurring_id) {
            do_action('bizmanage_recurring_transaction_created', $recurring_id, $sanitized_data);
        }

        return $recurring_id;
    }

    /**
     * Process recurring transactions
     */
    public function process_recurring_transactions() {
        $today = current_time('Y-m-d');
        
        // Get due recurring transactions
        $recurring_transactions = $this->db->get_results('recurring_transactions', array(
            'where' => array(
                'status' => 'active',
                'next_date' => $today
            )
        ));

        foreach ($recurring_transactions as $recurring) {
            // Create transaction from recurring template
            $transaction_data = array(
                'entity_id' => $recurring->entity_id,
                'transaction_type' => $recurring->transaction_type,
                'category' => $recurring->category,
                'subcategory' => $recurring->subcategory,
                'amount' => $recurring->amount,
                'currency' => $recurring->currency,
                'description' => $recurring->description,
                'transaction_date' => $today,
                'recurring_id' => $recurring->id,
                'status' => 'completed',
            );

            $transaction_id = $this->create_transaction($transaction_data);

            if ($transaction_id) {
                // Update recurring transaction
                $next_date = $this->calculate_next_date($recurring->next_date, $recurring->frequency, $recurring->interval_value);
                $generated_count = $recurring->generated_count + 1;

                $update_data = array(
                    'next_date' => $next_date,
                    'last_generated' => current_time('mysql'),
                    'generated_count' => $generated_count,
                );

                // Check if we've reached the total occurrences limit
                if ($recurring->total_occurrences && $generated_count >= $recurring->total_occurrences) {
                    $update_data['status'] = 'completed';
                }

                // Check if we've reached the end date
                if ($recurring->end_date && $next_date > $recurring->end_date) {
                    $update_data['status'] = 'completed';
                }

                $this->db->update('recurring_transactions', $update_data, array('id' => $recurring->id));
            }
        }
    }

    /**
     * Validate transaction data
     * @param array $data
     * @param int $transaction_id
     * @return array
     */
    private function validate_transaction_data($data, $transaction_id = 0) {
        $errors = array();

        // Required fields
        if (empty($data['entity_id']) || !is_numeric($data['entity_id'])) {
            $errors[] = __('Valid entity ID is required.', 'bizmanage-pro');
        }

        if (empty($data['transaction_type'])) {
            $errors[] = __('Transaction type is required.', 'bizmanage-pro');
        }

        if (!in_array($data['transaction_type'], array('income', 'expense'))) {
            $errors[] = __('Invalid transaction type.', 'bizmanage-pro');
        }

        if (empty($data['amount']) || !is_numeric($data['amount']) || floatval($data['amount']) <= 0) {
            $errors[] = __('Valid amount is required.', 'bizmanage-pro');
        }

        if (empty($data['category'])) {
            $errors[] = __('Category is required.', 'bizmanage-pro');
        } else {
            // Validate category exists for this entity and transaction type
            $categories = BizManage_Pro_Categories::instance();
            $entity_categories = $categories->get_categories($data['entity_id'], $data['transaction_type']);

            $category_names = array_column($entity_categories, 'name');
            if (!in_array($data['category'], $category_names)) {
                $errors[] = __('Invalid category for this entity and transaction type.', 'bizmanage-pro');
            }
        }

        if (empty($data['transaction_date'])) {
            $errors[] = __('Transaction date is required.', 'bizmanage-pro');
        } elseif (!$this->security->validate_date($data['transaction_date'])) {
            $errors[] = __('Invalid transaction date format.', 'bizmanage-pro');
        }

        return $errors;
    }

    /**
     * Sanitize transaction data
     * @param array $data
     * @return array
     */
    private function sanitize_transaction_data($data) {
        $sanitized = array();

        $fields = array(
            'entity_id' => 'int',
            'transaction_type' => 'text',
            'category' => 'text',
            'subcategory' => 'text',
            'amount' => 'float',
            'currency' => 'text',
            'description' => 'textarea',
            'reference_number' => 'text',
            'transaction_date' => 'text',
            'due_date' => 'text',
            'payment_method' => 'text',
            'bank_account' => 'text',
            'tax_rate' => 'float',
        );

        foreach ($fields as $field => $type) {
            if (isset($data[$field])) {
                $sanitized[$field] = $this->security->sanitize_input($data[$field], $type);
            }
        }

        return $sanitized;
    }

    /**
     * Validate recurring transaction data
     * @param array $data
     * @return array
     */
    private function validate_recurring_data($data) {
        $errors = array();

        // Required fields
        if (empty($data['template_name'])) {
            $errors[] = __('Template name is required.', 'bizmanage-pro');
        }

        if (empty($data['frequency'])) {
            $errors[] = __('Frequency is required.', 'bizmanage-pro');
        }

        if (empty($data['start_date'])) {
            $errors[] = __('Start date is required.', 'bizmanage-pro');
        }

        return $errors;
    }

    /**
     * Sanitize recurring transaction data
     * @param array $data
     * @return array
     */
    private function sanitize_recurring_data($data) {
        $sanitized = array();

        $fields = array(
            'entity_id' => 'int',
            'template_name' => 'text',
            'transaction_type' => 'text',
            'category' => 'text',
            'subcategory' => 'text',
            'amount' => 'float',
            'currency' => 'text',
            'description' => 'textarea',
            'frequency' => 'text',
            'interval_value' => 'int',
            'start_date' => 'text',
            'end_date' => 'text',
            'total_occurrences' => 'int',
        );

        foreach ($fields as $field => $type) {
            if (isset($data[$field])) {
                $sanitized[$field] = $this->security->sanitize_input($data[$field], $type);
            }
        }

        return $sanitized;
    }

    /**
     * Calculate next date for recurring transaction
     * @param string $current_date
     * @param string $frequency
     * @param int $interval
     * @return string
     */
    private function calculate_next_date($current_date, $frequency, $interval = 1) {
        $date = new DateTime($current_date);

        switch ($frequency) {
            case 'daily':
                $date->add(new DateInterval('P' . $interval . 'D'));
                break;
            case 'weekly':
                $date->add(new DateInterval('P' . ($interval * 7) . 'D'));
                break;
            case 'monthly':
                $date->add(new DateInterval('P' . $interval . 'M'));
                break;
            case 'quarterly':
                $date->add(new DateInterval('P' . ($interval * 3) . 'M'));
                break;
            case 'yearly':
                $date->add(new DateInterval('P' . $interval . 'Y'));
                break;
        }

        return $date->format('Y-m-d');
    }

    /**
     * Filter transactions by date range
     * @param array $transactions
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    private function filter_by_date_range($transactions, $start_date, $end_date) {
        if (empty($start_date) && empty($end_date)) {
            return $transactions;
        }

        return array_filter($transactions, function($transaction) use ($start_date, $end_date) {
            $transaction_date = $transaction->transaction_date;
            
            if (!empty($start_date) && $transaction_date < $start_date) {
                return false;
            }
            
            if (!empty($end_date) && $transaction_date > $end_date) {
                return false;
            }
            
            return true;
        });
    }

    /**
     * Get base currency for entity
     * @param int $entity_id
     * @return string
     */
    private function get_base_currency($entity_id) {
        $entity = BizManage_Pro_Entities::instance()->get_entity($entity_id);
        return $entity ? $entity->currency : get_option('bizmanage_pro_default_currency', 'USD');
    }

    /**
     * Check if user can edit transaction
     * @param int $transaction_id
     * @return bool
     */
    private function user_can_edit_transaction($transaction_id) {
        if (!current_user_can('bizmanage_manage_finances')) {
            return false;
        }

        $transaction = $this->get_transaction($transaction_id);
        if (!$transaction) {
            return false;
        }

        // Check entity access
        $roles = BizManage_Pro_Roles::instance();
        return $roles->user_can_access_entity(get_current_user_id(), $transaction->entity_id);
    }
}

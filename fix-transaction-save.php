<?php
/**
 * Fix Transaction Save Issues
 * 
 * This script will diagnose and fix transaction saving problems
 */

// Find WordPress root directory
$wp_root = dirname(__FILE__);
$max_depth = 5;
$depth = 0;

while ($depth < $max_depth) {
    if (file_exists($wp_root . '/wp-config.php') && file_exists($wp_root . '/wp-load.php')) {
        break;
    }
    $wp_root = dirname($wp_root);
    $depth++;
}

if (!file_exists($wp_root . '/wp-load.php')) {
    die('WordPress installation not found.');
}

// WordPress environment
define('WP_USE_THEMES', false);
require_once($wp_root . '/wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Fix Transaction Save Issues</h1>';

// Check prerequisites
echo '<h2>1. Prerequisites Check</h2>';
global $wpdb;

$entities_table = $wpdb->prefix . 'bizmanage_business_entities';
$categories_table = $wpdb->prefix . 'bizmanage_categories';
$transactions_table = $wpdb->prefix . 'bizmanage_transactions';

// Check if tables exist
$tables_check = array(
    'entities' => $wpdb->get_var("SHOW TABLES LIKE '$entities_table'") === $entities_table,
    'categories' => $wpdb->get_var("SHOW TABLES LIKE '$categories_table'") === $categories_table,
    'transactions' => $wpdb->get_var("SHOW TABLES LIKE '$transactions_table'") === $transactions_table
);

foreach ($tables_check as $table => $exists) {
    if ($exists) {
        echo '<p style="color: green;">✅ ' . ucfirst($table) . ' table exists</p>';
    } else {
        echo '<p style="color: red;">❌ ' . ucfirst($table) . ' table missing</p>';
    }
}

// Get test data
$entity = $wpdb->get_row("SELECT * FROM $entities_table WHERE status = 'active' LIMIT 1");
if (!$entity) {
    echo '<p style="color: red;">❌ No active business entity found</p>';
    exit;
}

$category = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM $categories_table WHERE entity_id = %d AND status = 'active' LIMIT 1",
    $entity->id
));
if (!$category) {
    echo '<p style="color: red;">❌ No active category found for entity</p>';
    exit;
}

echo '<p style="color: green;">✅ Test entity: ' . $entity->business_name . ' (ID: ' . $entity->id . ')</p>';
echo '<p style="color: green;">✅ Test category: ' . $category->name . ' (ID: ' . $category->id . ', Type: ' . $category->type . ')</p>';

// Test transaction save with correct data structure
echo '<h2>2. Test Transaction Save with Correct Data</h2>';

if (isset($_POST['test_transaction_save'])) {
    // Prepare transaction data exactly as expected by the API
    $transaction_data = array(
        'action' => 'bizmanage_save_transaction',
        'nonce' => wp_create_nonce('bizmanage_save_transaction'),
        'transaction_id' => 0,
        'entity_id' => $entity->id,
        'transaction_type' => $category->type,
        'amount' => 150.00,
        'category_id' => $category->id,
        'description' => 'Test transaction ' . time(),
        'transaction_date' => date('Y-m-d'),
        'reference_number' => 'REF-' . time(),
        'notes' => 'Test transaction notes'
    );
    
    echo '<h3>Testing Transaction Save</h3>';
    echo '<p><strong>Transaction Data:</strong></p>';
    echo '<pre>' . json_encode($transaction_data, JSON_PRETTY_PRINT) . '</pre>';
    
    // Simulate AJAX request
    $_POST = $transaction_data;
    
    // Test AJAX handler
    if (class_exists('BizManage_Pro_Ajax')) {
        $ajax = BizManage_Pro_Ajax::instance();
        
        if (method_exists($ajax, 'save_transaction')) {
            // Capture output
            ob_start();
            try {
                $ajax->save_transaction();
                $output = ob_get_clean();
                
                echo '<p style="color: green;">✅ AJAX save_transaction method executed</p>';
                echo '<p><strong>Response:</strong></p>';
                echo '<pre>' . htmlspecialchars($output) . '</pre>';
                
                // Check if transaction was created
                $latest_transaction = $wpdb->get_row("SELECT * FROM $transactions_table ORDER BY id DESC LIMIT 1");
                if ($latest_transaction) {
                    echo '<p style="color: green;">✅ Transaction found in database!</p>';
                    echo '<p><strong>Transaction Details:</strong></p>';
                    echo '<pre>' . print_r($latest_transaction, true) . '</pre>';
                } else {
                    echo '<p style="color: red;">❌ No transaction found in database</p>';
                }
                
            } catch (Exception $e) {
                ob_end_clean();
                echo '<p style="color: red;">❌ Error: ' . $e->getMessage() . '</p>';
            }
        } else {
            echo '<p style="color: red;">❌ save_transaction method not found in BizManage_Pro_Ajax</p>';
        }
    } else {
        echo '<p style="color: red;">❌ BizManage_Pro_Ajax class not found</p>';
    }
}

echo '<form method="post">';
echo '<button type="submit" name="test_transaction_save" value="1">Test Transaction Save</button>';
echo '</form>';

// Test direct finances class
echo '<h2>3. Test Direct Finances Class</h2>';

if (isset($_POST['test_direct_finances'])) {
    $transaction_data = array(
        'entity_id' => $entity->id,
        'transaction_type' => $category->type,
        'amount' => 200.00,
        'category_id' => $category->id,
        'description' => 'Direct test transaction ' . time(),
        'transaction_date' => date('Y-m-d'),
        'reference_number' => 'DIRECT-' . time()
    );
    
    echo '<h3>Testing Direct Finances Class</h3>';
    echo '<p><strong>Data:</strong></p>';
    echo '<pre>' . json_encode($transaction_data, JSON_PRETTY_PRINT) . '</pre>';
    
    if (class_exists('BizManage_Pro_Finances')) {
        $finances = BizManage_Pro_Finances::instance();
        
        if (method_exists($finances, 'create_transaction')) {
            try {
                $result = $finances->create_transaction($transaction_data);
                
                if ($result) {
                    echo '<p style="color: green;">✅ Direct transaction creation successful! ID: ' . $result . '</p>';
                    
                    // Get the created transaction
                    $created_transaction = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $transactions_table WHERE id = %d",
                        $result
                    ));
                    
                    if ($created_transaction) {
                        echo '<p><strong>Created Transaction:</strong></p>';
                        echo '<pre>' . print_r($created_transaction, true) . '</pre>';
                    }
                } else {
                    echo '<p style="color: red;">❌ Direct transaction creation failed</p>';
                }
                
            } catch (Exception $e) {
                echo '<p style="color: red;">❌ Error: ' . $e->getMessage() . '</p>';
            }
        } else {
            echo '<p style="color: red;">❌ create_transaction method not found</p>';
        }
    } else {
        echo '<p style="color: red;">❌ BizManage_Pro_Finances class not found</p>';
    }
}

echo '<form method="post">';
echo '<button type="submit" name="test_direct_finances" value="1">Test Direct Finances Class</button>';
echo '</form>';

// JavaScript test
echo '<h2>4. JavaScript Test</h2>';
echo '<div id="js-result"></div>';
echo '<button onclick="testTransactionSaveJS()">Test Transaction Save via JavaScript</button>';

echo '<script>
function testTransactionSaveJS() {
    var data = {
        action: "bizmanage_save_transaction",
        nonce: "' . wp_create_nonce('bizmanage_save_transaction') . '",
        transaction_id: 0,
        entity_id: ' . $entity->id . ',
        transaction_type: "' . $category->type . '",
        amount: 250.00,
        category_id: ' . $category->id . ',
        description: "JS Test Transaction " + Date.now(),
        transaction_date: "' . date('Y-m-d') . '",
        reference_number: "JS-" + Date.now()
    };
    
    console.log("Testing transaction save with data:", data);
    
    jQuery.post(ajaxurl, data, function(response) {
        console.log("Transaction save response:", response);
        document.getElementById("js-result").innerHTML = 
            "<h4>JavaScript Test Result:</h4><pre>" + JSON.stringify(response, null, 2) + "</pre>";
    }).fail(function(xhr, status, error) {
        console.log("Transaction save error:", error);
        console.log("XHR response:", xhr.responseText);
        document.getElementById("js-result").innerHTML = 
            "<h4 style=\"color: red;\">JavaScript Test Failed:</h4><pre>" + xhr.responseText + "</pre>";
    });
}
</script>';

// Check AJAX handlers
echo '<h2>5. AJAX Handlers Check</h2>';

$ajax_actions = array(
    'bizmanage_save_transaction',
    'bizmanage_get_transactions',
    'bizmanage_delete_transaction'
);

foreach ($ajax_actions as $action) {
    $has_action = has_action('wp_ajax_' . $action);
    echo '<p><strong>' . $action . ':</strong> ';
    if ($has_action) {
        echo '<span style="color: green;">✅ Registered</span>';
    } else {
        echo '<span style="color: red;">❌ Not registered</span>';
    }
    echo '</p>';
}

// Recent transactions
echo '<h2>6. Recent Transactions</h2>';
$recent_transactions = $wpdb->get_results("SELECT * FROM $transactions_table ORDER BY id DESC LIMIT 5");

if (!empty($recent_transactions)) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Entity</th><th>Type</th><th>Amount</th><th>Category</th><th>Description</th><th>Date</th><th>Status</th></tr>';
    foreach ($recent_transactions as $trans) {
        echo '<tr>';
        echo '<td>' . $trans->id . '</td>';
        echo '<td>' . $trans->entity_id . '</td>';
        echo '<td>' . $trans->transaction_type . '</td>';
        echo '<td>$' . number_format($trans->amount, 2) . '</td>';
        echo '<td>' . $trans->category_id . '</td>';
        echo '<td>' . $trans->description . '</td>';
        echo '<td>' . $trans->transaction_date . '</td>';
        echo '<td>' . $trans->status . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p>No transactions found.</p>';
}

// Instructions
echo '<h2>7. Next Steps</h2>';
echo '<ol>';
echo '<li><strong>Test transaction save</strong> using the buttons above</li>';
echo '<li><strong>Check JavaScript test</strong> for AJAX functionality</li>';
echo '<li><strong>Go to BizManage Pro</strong> and try saving a transaction</li>';
echo '<li><strong>Check browser console</strong> for detailed logs</li>';
echo '</ol>';

echo '<h2>8. Fixes Applied</h2>';
echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;">';
echo '<h4>✅ Transaction Save Fixes:</h4>';
echo '<ul>';
echo '<li>Fixed form field mapping in saveTransaction() function</li>';
echo '<li>Added proper data validation and error handling</li>';
echo '<li>Enhanced AJAX response handling</li>';
echo '<li>Added detailed console logging for debugging</li>';
echo '<li>Fixed nonce handling for transaction save</li>';
echo '</ul>';
echo '</div>';

echo '<h2>9. Quick Links</h2>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances&action=add-income&entity_id=' . $entity->id) . '">Add Income Transaction</a></p>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances&action=add-expense&entity_id=' . $entity->id) . '">Add Expense Transaction</a></p>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances&entity_id=' . $entity->id) . '">View Transactions</a></p>';
?>

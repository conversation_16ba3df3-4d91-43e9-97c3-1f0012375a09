<?php
/**
 * Simple script to generate .mo files from .po files
 * This is a basic implementation for development purposes
 */

// Function to convert PO to MO (simplified)
function po_to_mo($po_file, $mo_file) {
    if (!file_exists($po_file)) {
        echo "PO file not found: $po_file\n";
        return false;
    }
    
    $po_content = file_get_contents($po_file);
    $lines = explode("\n", $po_content);
    
    $translations = array();
    $current_msgid = '';
    $current_msgstr = '';
    $in_msgid = false;
    $in_msgstr = false;
    
    foreach ($lines as $line) {
        $line = trim($line);
        
        if (strpos($line, 'msgid ') === 0) {
            // Save previous translation if exists
            if (!empty($current_msgid) && !empty($current_msgstr)) {
                $translations[$current_msgid] = $current_msgstr;
            }
            
            $current_msgid = substr($line, 7, -1); // Remove 'msgid "' and '"'
            $current_msgstr = '';
            $in_msgid = true;
            $in_msgstr = false;
        } elseif (strpos($line, 'msgstr ') === 0) {
            $current_msgstr = substr($line, 8, -1); // Remove 'msgstr "' and '"'
            $in_msgid = false;
            $in_msgstr = true;
        } elseif (strpos($line, '"') === 0 && $in_msgid) {
            $current_msgid .= substr($line, 1, -1); // Remove quotes
        } elseif (strpos($line, '"') === 0 && $in_msgstr) {
            $current_msgstr .= substr($line, 1, -1); // Remove quotes
        }
    }
    
    // Save last translation
    if (!empty($current_msgid) && !empty($current_msgstr)) {
        $translations[$current_msgid] = $current_msgstr;
    }
    
    // Create a simple serialized format (not actual MO format, but functional for WordPress)
    $mo_data = serialize($translations);
    
    if (file_put_contents($mo_file, $mo_data)) {
        echo "Generated MO file: $mo_file\n";
        return true;
    } else {
        echo "Failed to write MO file: $mo_file\n";
        return false;
    }
}

// Generate Bengali MO file
$po_file = __DIR__ . '/bizmanage-pro-bn_BD.po';
$mo_file = __DIR__ . '/bizmanage-pro-bn_BD.mo';

po_to_mo($po_file, $mo_file);

echo "Translation file generation complete.\n";
?>

<?php
/**
 * BizManage Pro Shortcodes Class
 *
 * Handles frontend shortcodes for financial reports
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Shortcodes Class
 */
class BizManage_Pro_Shortcodes {

    /**
     * Instance of this class
     * @var BizManage_Pro_Shortcodes
     */
    private static $instance = null;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Reports instance
     * @var BizManage_Pro_Reports
     */
    private $reports;

    /**
     * Get instance
     * @return BizManage_Pro_Shortcodes
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->db = BizManage_Pro_Database::instance();
        $this->reports = BizManage_Pro_Reports::instance();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'register_shortcodes'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_styles'));
    }

    /**
     * Register shortcodes
     */
    public function register_shortcodes() {
        add_shortcode('bizmanage_income_statement', array($this, 'income_statement_shortcode'));
        add_shortcode('bizmanage_balance_sheet', array($this, 'balance_sheet_shortcode'));
        add_shortcode('bizmanage_cashflow', array($this, 'cashflow_shortcode'));
        add_shortcode('bizmanage_financial_summary', array($this, 'financial_summary_shortcode'));
    }

    /**
     * Enqueue frontend styles
     */
    public function enqueue_frontend_styles() {
        if ($this->has_bizmanage_shortcode()) {
            wp_enqueue_style(
                'bizmanage-pro-frontend',
                BIZMANAGE_PRO_PLUGIN_URL . 'assets/css/frontend.css',
                array(),
                BIZMANAGE_PRO_VERSION
            );
        }
    }

    /**
     * Check if page has BizManage shortcodes
     * @return bool
     */
    private function has_bizmanage_shortcode() {
        global $post;
        
        if (!$post) {
            return false;
        }
        
        $shortcodes = array(
            'bizmanage_income_statement',
            'bizmanage_balance_sheet',
            'bizmanage_cashflow',
            'bizmanage_financial_summary'
        );
        
        foreach ($shortcodes as $shortcode) {
            if (has_shortcode($post->post_content, $shortcode)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Income Statement shortcode
     * @param array $atts
     * @return string
     */
    public function income_statement_shortcode($atts) {
        $atts = shortcode_atts(array(
            'entity_id' => 0,
            'period' => 'last_month',
            'style' => 'table',
            'show_title' => 'true',
            'currency' => 'USD'
        ), $atts);

        $entity_id = intval($atts['entity_id']);
        $dates = $this->parse_period($atts['period']);
        
        if (!$entity_id || !$dates) {
            return '<div class="bizmanage-error">' . __('Invalid parameters provided.', 'bizmanage-pro') . '</div>';
        }

        // Check if entity is public or user has access
        if (!$this->can_view_entity_reports($entity_id)) {
            return '<div class="bizmanage-error">' . __('Access denied.', 'bizmanage-pro') . '</div>';
        }

        $data = $this->get_income_statement_data($entity_id, $dates['start'], $dates['end']);
        
        return $this->render_income_statement($data, $atts);
    }

    /**
     * Balance Sheet shortcode
     * @param array $atts
     * @return string
     */
    public function balance_sheet_shortcode($atts) {
        $atts = shortcode_atts(array(
            'entity_id' => 0,
            'period' => 'last_month',
            'style' => 'table',
            'show_title' => 'true',
            'currency' => 'USD'
        ), $atts);

        $entity_id = intval($atts['entity_id']);
        $dates = $this->parse_period($atts['period']);
        
        if (!$entity_id || !$dates) {
            return '<div class="bizmanage-error">' . __('Invalid parameters provided.', 'bizmanage-pro') . '</div>';
        }

        if (!$this->can_view_entity_reports($entity_id)) {
            return '<div class="bizmanage-error">' . __('Access denied.', 'bizmanage-pro') . '</div>';
        }

        $data = $this->get_balance_sheet_data($entity_id, $dates['end']);
        
        return $this->render_balance_sheet($data, $atts);
    }

    /**
     * Cash Flow shortcode
     * @param array $atts
     * @return string
     */
    public function cashflow_shortcode($atts) {
        $atts = shortcode_atts(array(
            'entity_id' => 0,
            'period' => 'last_month',
            'style' => 'table',
            'show_title' => 'true',
            'currency' => 'USD'
        ), $atts);

        $entity_id = intval($atts['entity_id']);
        $dates = $this->parse_period($atts['period']);
        
        if (!$entity_id || !$dates) {
            return '<div class="bizmanage-error">' . __('Invalid parameters provided.', 'bizmanage-pro') . '</div>';
        }

        if (!$this->can_view_entity_reports($entity_id)) {
            return '<div class="bizmanage-error">' . __('Access denied.', 'bizmanage-pro') . '</div>';
        }

        $data = $this->get_cashflow_data($entity_id, $dates['start'], $dates['end']);
        
        return $this->render_cashflow($data, $atts);
    }

    /**
     * Financial Summary shortcode
     * @param array $atts
     * @return string
     */
    public function financial_summary_shortcode($atts) {
        $atts = shortcode_atts(array(
            'entity_id' => 0,
            'period' => 'last_month',
            'style' => 'cards',
            'show_title' => 'true',
            'currency' => 'USD',
            'show_chart' => 'false'
        ), $atts);

        $entity_id = intval($atts['entity_id']);
        $dates = $this->parse_period($atts['period']);
        
        if (!$entity_id || !$dates) {
            return '<div class="bizmanage-error">' . __('Invalid parameters provided.', 'bizmanage-pro') . '</div>';
        }

        if (!$this->can_view_entity_reports($entity_id)) {
            return '<div class="bizmanage-error">' . __('Access denied.', 'bizmanage-pro') . '</div>';
        }

        $data = $this->get_financial_summary_data($entity_id, $dates['start'], $dates['end']);
        
        return $this->render_financial_summary($data, $atts);
    }

    /**
     * Parse period parameter
     * @param string $period
     * @return array|false
     */
    private function parse_period($period) {
        $end_date = current_time('Y-m-d');
        $start_date = '';

        switch ($period) {
            case 'last_week':
                $start_date = date('Y-m-d', strtotime('-7 days'));
                break;
            case 'last_month':
                $start_date = date('Y-m-d', strtotime('-30 days'));
                break;
            case 'last_quarter':
                $start_date = date('Y-m-d', strtotime('-90 days'));
                break;
            case 'last_year':
                $start_date = date('Y-m-d', strtotime('-365 days'));
                break;
            case 'this_month':
                $start_date = date('Y-m-01');
                $end_date = date('Y-m-t');
                break;
            case 'this_year':
                $start_date = date('Y-01-01');
                $end_date = date('Y-12-31');
                break;
            default:
                // Check if it's a custom date range (format: start_date,end_date)
                if (strpos($period, ',') !== false) {
                    $dates = explode(',', $period);
                    if (count($dates) === 2) {
                        $start_date = sanitize_text_field(trim($dates[0]));
                        $end_date = sanitize_text_field(trim($dates[1]));
                        
                        // Validate dates
                        if (!$this->is_valid_date($start_date) || !$this->is_valid_date($end_date)) {
                            return false;
                        }
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
                break;
        }

        return array(
            'start' => $start_date,
            'end' => $end_date
        );
    }

    /**
     * Validate date format
     * @param string $date
     * @return bool
     */
    private function is_valid_date($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * Check if user can view entity reports
     * @param int $entity_id
     * @return bool
     */
    private function can_view_entity_reports($entity_id) {
        // Check if entity exists
        $entity = $this->db->get_row('business_entities', array(
            'where' => array('id' => $entity_id, 'status' => 'active')
        ));

        if (!$entity) {
            return false;
        }

        // Check if reports are public
        $public_reports = get_option('bizmanage_pro_public_reports_' . $entity_id, false);
        if ($public_reports) {
            return true;
        }

        // Check if user is logged in and has access
        if (is_user_logged_in()) {
            $roles = BizManage_Pro_Roles::instance();
            return $roles->user_can_access_entity(get_current_user_id(), $entity_id);
        }

        return false;
    }

    /**
     * Get income statement data
     * @param int $entity_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    private function get_income_statement_data($entity_id, $start_date, $end_date) {
        // Get income transactions
        $income_query = "SELECT category, SUM(amount) as total
                        FROM {$this->db->get_table('transactions')}
                        WHERE entity_id = %d AND transaction_type = 'income'
                        AND status = 'completed' AND transaction_date BETWEEN %s AND %s
                        GROUP BY category";

        $income_results = $this->db->get_results($income_query, $entity_id, $start_date, $end_date);

        $expense_query = "SELECT category, SUM(amount) as total
                         FROM {$this->db->get_table('transactions')}
                         WHERE entity_id = %d AND transaction_type = 'expense'
                         AND status = 'completed' AND transaction_date BETWEEN %s AND %s
                         GROUP BY category";

        $expense_results = $this->db->get_results($expense_query, $entity_id, $start_date, $end_date);

        $income_by_category = array();
        $total_income = 0;
        foreach ($income_results as $row) {
            $income_by_category[$row->category] = floatval($row->total);
            $total_income += floatval($row->total);
        }

        $expenses_by_category = array();
        $total_expenses = 0;
        foreach ($expense_results as $row) {
            $expenses_by_category[$row->category] = floatval($row->total);
            $total_expenses += floatval($row->total);
        }

        return array(
            'income' => $income_by_category,
            'expenses' => $expenses_by_category,
            'total_income' => $total_income,
            'total_expenses' => $total_expenses,
            'net_profit' => $total_income - $total_expenses,
            'start_date' => $start_date,
            'end_date' => $end_date
        );
    }

    /**
     * Get financial summary data
     * @param int $entity_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    private function get_financial_summary_data($entity_id, $start_date, $end_date) {
        $income_statement = $this->get_income_statement_data($entity_id, $start_date, $end_date);

        $transaction_count = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->db->get_table('transactions')}
             WHERE entity_id = %d AND status = 'completed'
             AND transaction_date BETWEEN %s AND %s",
            $entity_id, $start_date, $end_date
        );

        return array(
            'total_income' => $income_statement['total_income'],
            'total_expenses' => $income_statement['total_expenses'],
            'net_profit' => $income_statement['net_profit'],
            'transaction_count' => intval($transaction_count),
            'profit_margin' => $income_statement['total_income'] > 0 ?
                ($income_statement['net_profit'] / $income_statement['total_income']) * 100 : 0,
            'start_date' => $start_date,
            'end_date' => $end_date
        );
    }

    /**
     * Get balance sheet data (simplified)
     * @param int $entity_id
     * @param string $end_date
     * @return array
     */
    private function get_balance_sheet_data($entity_id, $end_date) {
        $total_income = $this->db->get_var(
            "SELECT SUM(amount) FROM {$this->db->get_table('transactions')}
             WHERE entity_id = %d AND transaction_type = 'income'
             AND status = 'completed' AND transaction_date <= %s",
            $entity_id, $end_date
        );

        $total_expenses = $this->db->get_var(
            "SELECT SUM(amount) FROM {$this->db->get_table('transactions')}
             WHERE entity_id = %d AND transaction_type = 'expense'
             AND status = 'completed' AND transaction_date <= %s",
            $entity_id, $end_date
        );

        $retained_earnings = floatval($total_income) - floatval($total_expenses);

        return array(
            'assets' => array(
                'cash' => $retained_earnings > 0 ? $retained_earnings : 0,
                'total_assets' => $retained_earnings > 0 ? $retained_earnings : 0
            ),
            'liabilities' => array(
                'total_liabilities' => 0
            ),
            'equity' => array(
                'retained_earnings' => $retained_earnings,
                'total_equity' => $retained_earnings
            ),
            'as_of_date' => $end_date
        );
    }

    /**
     * Get cash flow data (simplified)
     * @param int $entity_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    private function get_cashflow_data($entity_id, $start_date, $end_date) {
        $income_statement = $this->get_income_statement_data($entity_id, $start_date, $end_date);

        return array(
            'operating_activities' => array(
                'net_income' => $income_statement['net_profit'],
                'total_operating' => $income_statement['net_profit']
            ),
            'investing_activities' => array(
                'total_investing' => 0
            ),
            'financing_activities' => array(
                'total_financing' => 0
            ),
            'net_cash_flow' => $income_statement['net_profit'],
            'start_date' => $start_date,
            'end_date' => $end_date
        );
    }

    /**
     * Format currency
     * @param float $amount
     * @param string $currency
     * @return string
     */
    private function format_currency($amount, $currency = 'USD') {
        $symbols = array(
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'BDT' => '৳'
        );

        $symbol = isset($symbols[$currency]) ? $symbols[$currency] : $currency . ' ';
        return $symbol . number_format(abs($amount), 2);
    }

    /**
     * Render income statement
     * @param array $data
     * @param array $atts
     * @return string
     */
    private function render_income_statement($data, $atts) {
        $output = '<div class="bizmanage-frontend-report bizmanage-income-statement">';

        if ($atts['show_title'] === 'true') {
            $output .= '<h3 class="bizmanage-report-title">' . __('Income Statement', 'bizmanage-pro') . '</h3>';
            $output .= '<p class="bizmanage-report-period">' .
                sprintf(__('Period: %s to %s', 'bizmanage-pro'),
                    date('M j, Y', strtotime($data['start_date'])),
                    date('M j, Y', strtotime($data['end_date']))
                ) . '</p>';
        }

        if ($atts['style'] === 'table') {
            $output .= $this->render_income_statement_table($data, $atts['currency']);
        } else {
            $output .= $this->render_income_statement_summary($data, $atts['currency']);
        }

        $output .= '</div>';
        return $output;
    }

    /**
     * Render income statement table
     * @param array $data
     * @param string $currency
     * @return string
     */
    private function render_income_statement_table($data, $currency) {
        $output = '<table class="bizmanage-report-table">';

        // Income section
        $output .= '<thead><tr><th colspan="2" class="section-header">' . __('Revenue', 'bizmanage-pro') . '</th></tr></thead>';
        $output .= '<tbody>';

        foreach ($data['income'] as $category => $amount) {
            $output .= '<tr>';
            $output .= '<td>' . esc_html($category) . '</td>';
            $output .= '<td class="amount">' . $this->format_currency($amount, $currency) . '</td>';
            $output .= '</tr>';
        }

        $output .= '<tr class="total-row">';
        $output .= '<td><strong>' . __('Total Revenue', 'bizmanage-pro') . '</strong></td>';
        $output .= '<td class="amount"><strong>' . $this->format_currency($data['total_income'], $currency) . '</strong></td>';
        $output .= '</tr>';

        // Expenses section
        $output .= '<tr><th colspan="2" class="section-header">' . __('Expenses', 'bizmanage-pro') . '</th></tr>';

        foreach ($data['expenses'] as $category => $amount) {
            $output .= '<tr>';
            $output .= '<td>' . esc_html($category) . '</td>';
            $output .= '<td class="amount">(' . $this->format_currency($amount, $currency) . ')</td>';
            $output .= '</tr>';
        }

        $output .= '<tr class="total-row">';
        $output .= '<td><strong>' . __('Total Expenses', 'bizmanage-pro') . '</strong></td>';
        $output .= '<td class="amount"><strong>(' . $this->format_currency($data['total_expenses'], $currency) . ')</strong></td>';
        $output .= '</tr>';

        // Net profit
        $profit_class = $data['net_profit'] >= 0 ? 'profit' : 'loss';
        $output .= '<tr class="net-profit-row ' . $profit_class . '">';
        $output .= '<td><strong>' . __('Net Profit', 'bizmanage-pro') . '</strong></td>';
        $output .= '<td class="amount"><strong>' . $this->format_currency($data['net_profit'], $currency) . '</strong></td>';
        $output .= '</tr>';

        $output .= '</tbody></table>';

        return $output;
    }

    /**
     * Render income statement summary
     * @param array $data
     * @param string $currency
     * @return string
     */
    private function render_income_statement_summary($data, $currency) {
        $profit_class = $data['net_profit'] >= 0 ? 'profit' : 'loss';

        $output = '<div class="bizmanage-summary-cards">';

        $output .= '<div class="bizmanage-summary-card revenue">';
        $output .= '<h4>' . __('Total Revenue', 'bizmanage-pro') . '</h4>';
        $output .= '<div class="amount">' . $this->format_currency($data['total_income'], $currency) . '</div>';
        $output .= '</div>';

        $output .= '<div class="bizmanage-summary-card expenses">';
        $output .= '<h4>' . __('Total Expenses', 'bizmanage-pro') . '</h4>';
        $output .= '<div class="amount">' . $this->format_currency($data['total_expenses'], $currency) . '</div>';
        $output .= '</div>';

        $output .= '<div class="bizmanage-summary-card net-profit ' . $profit_class . '">';
        $output .= '<h4>' . __('Net Profit', 'bizmanage-pro') . '</h4>';
        $output .= '<div class="amount">' . $this->format_currency($data['net_profit'], $currency) . '</div>';
        $output .= '</div>';

        $output .= '</div>';

        return $output;
    }

    /**
     * Render balance sheet
     * @param array $data
     * @param array $atts
     * @return string
     */
    private function render_balance_sheet($data, $atts) {
        $output = '<div class="bizmanage-frontend-report bizmanage-balance-sheet">';

        if ($atts['show_title'] === 'true') {
            $output .= '<h3 class="bizmanage-report-title">' . __('Balance Sheet', 'bizmanage-pro') . '</h3>';
            $output .= '<p class="bizmanage-report-period">' .
                sprintf(__('As of %s', 'bizmanage-pro'),
                    date('M j, Y', strtotime($data['as_of_date']))
                ) . '</p>';
        }

        $output .= '<table class="bizmanage-report-table">';

        // Assets
        $output .= '<thead><tr><th colspan="2" class="section-header">' . __('Assets', 'bizmanage-pro') . '</th></tr></thead>';
        $output .= '<tbody>';
        $output .= '<tr><td>' . __('Cash', 'bizmanage-pro') . '</td><td class="amount">' . $this->format_currency($data['assets']['cash'], $atts['currency']) . '</td></tr>';
        $output .= '<tr class="total-row"><td><strong>' . __('Total Assets', 'bizmanage-pro') . '</strong></td><td class="amount"><strong>' . $this->format_currency($data['assets']['total_assets'], $atts['currency']) . '</strong></td></tr>';

        // Liabilities
        $output .= '<tr><th colspan="2" class="section-header">' . __('Liabilities', 'bizmanage-pro') . '</th></tr>';
        $output .= '<tr class="total-row"><td><strong>' . __('Total Liabilities', 'bizmanage-pro') . '</strong></td><td class="amount"><strong>' . $this->format_currency($data['liabilities']['total_liabilities'], $atts['currency']) . '</strong></td></tr>';

        // Equity
        $output .= '<tr><th colspan="2" class="section-header">' . __('Equity', 'bizmanage-pro') . '</th></tr>';
        $output .= '<tr><td>' . __('Retained Earnings', 'bizmanage-pro') . '</td><td class="amount">' . $this->format_currency($data['equity']['retained_earnings'], $atts['currency']) . '</td></tr>';
        $output .= '<tr class="total-row"><td><strong>' . __('Total Equity', 'bizmanage-pro') . '</strong></td><td class="amount"><strong>' . $this->format_currency($data['equity']['total_equity'], $atts['currency']) . '</strong></td></tr>';

        $output .= '</tbody></table>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render cash flow
     * @param array $data
     * @param array $atts
     * @return string
     */
    private function render_cashflow($data, $atts) {
        $output = '<div class="bizmanage-frontend-report bizmanage-cashflow">';

        if ($atts['show_title'] === 'true') {
            $output .= '<h3 class="bizmanage-report-title">' . __('Cash Flow Statement', 'bizmanage-pro') . '</h3>';
            $output .= '<p class="bizmanage-report-period">' .
                sprintf(__('Period: %s to %s', 'bizmanage-pro'),
                    date('M j, Y', strtotime($data['start_date'])),
                    date('M j, Y', strtotime($data['end_date']))
                ) . '</p>';
        }

        $output .= '<table class="bizmanage-report-table">';

        // Operating Activities
        $output .= '<thead><tr><th colspan="2" class="section-header">' . __('Operating Activities', 'bizmanage-pro') . '</th></tr></thead>';
        $output .= '<tbody>';
        $output .= '<tr><td>' . __('Net Income', 'bizmanage-pro') . '</td><td class="amount">' . $this->format_currency($data['operating_activities']['net_income'], $atts['currency']) . '</td></tr>';
        $output .= '<tr class="total-row"><td><strong>' . __('Net Cash from Operating Activities', 'bizmanage-pro') . '</strong></td><td class="amount"><strong>' . $this->format_currency($data['operating_activities']['total_operating'], $atts['currency']) . '</strong></td></tr>';

        // Net Cash Flow
        $cash_flow_class = $data['net_cash_flow'] >= 0 ? 'profit' : 'loss';
        $output .= '<tr class="net-profit-row ' . $cash_flow_class . '"><td><strong>' . __('Net Cash Flow', 'bizmanage-pro') . '</strong></td><td class="amount"><strong>' . $this->format_currency($data['net_cash_flow'], $atts['currency']) . '</strong></td></tr>';

        $output .= '</tbody></table>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render financial summary
     * @param array $data
     * @param array $atts
     * @return string
     */
    private function render_financial_summary($data, $atts) {
        $output = '<div class="bizmanage-frontend-report bizmanage-financial-summary">';

        if ($atts['show_title'] === 'true') {
            $output .= '<h3 class="bizmanage-report-title">' . __('Financial Summary', 'bizmanage-pro') . '</h3>';
            $output .= '<p class="bizmanage-report-period">' .
                sprintf(__('Period: %s to %s', 'bizmanage-pro'),
                    date('M j, Y', strtotime($data['start_date'])),
                    date('M j, Y', strtotime($data['end_date']))
                ) . '</p>';
        }

        $profit_class = $data['net_profit'] >= 0 ? 'profit' : 'loss';

        $output .= '<div class="bizmanage-summary-cards">';

        $output .= '<div class="bizmanage-summary-card revenue">';
        $output .= '<h4>' . __('Total Income', 'bizmanage-pro') . '</h4>';
        $output .= '<div class="amount">' . $this->format_currency($data['total_income'], $atts['currency']) . '</div>';
        $output .= '</div>';

        $output .= '<div class="bizmanage-summary-card expenses">';
        $output .= '<h4>' . __('Total Expenses', 'bizmanage-pro') . '</h4>';
        $output .= '<div class="amount">' . $this->format_currency($data['total_expenses'], $atts['currency']) . '</div>';
        $output .= '</div>';

        $output .= '<div class="bizmanage-summary-card net-profit ' . $profit_class . '">';
        $output .= '<h4>' . __('Net Profit', 'bizmanage-pro') . '</h4>';
        $output .= '<div class="amount">' . $this->format_currency($data['net_profit'], $atts['currency']) . '</div>';
        $output .= '</div>';

        $output .= '<div class="bizmanage-summary-card transactions">';
        $output .= '<h4>' . __('Transactions', 'bizmanage-pro') . '</h4>';
        $output .= '<div class="amount">' . number_format($data['transaction_count']) . '</div>';
        $output .= '</div>';

        if ($data['profit_margin'] !== 0) {
            $output .= '<div class="bizmanage-summary-card profit-margin ' . $profit_class . '">';
            $output .= '<h4>' . __('Profit Margin', 'bizmanage-pro') . '</h4>';
            $output .= '<div class="amount">' . number_format($data['profit_margin'], 1) . '%</div>';
            $output .= '</div>';
        }

        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }
}

<?php
/**
 * BizManage Pro Business Entities Class
 *
 * Handles business entity management and operations
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Business Entities Class
 */
class BizManage_Pro_Entities {

    /**
     * Instance of this class
     * @var BizManage_Pro_Entities
     */
    private static $instance = null;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Security instance
     * @var BizManage_Pro_Security
     */
    private $security;

    /**
     * Utilities instance
     * @var BizManage_Pro_Utilities
     */
    private $utilities;

    /**
     * Get instance
     * @return BizManage_Pro_Entities
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->db = BizManage_Pro_Database::instance();
        $this->security = BizManage_Pro_Security::instance();
        $this->utilities = BizManage_Pro_Utilities::instance();
    }

    /**
     * Create new business entity
     * @param array $data
     * @return int|false
     */
    public function create_entity($data) {
        // Validate required fields
        $validation_errors = $this->validate_entity_data($data);
        if (!empty($validation_errors)) {
            return false;
        }

        // Sanitize data
        $sanitized_data = $this->sanitize_entity_data($data);

        // Add default values
        $sanitized_data['user_id'] = get_current_user_id();
        $sanitized_data['status'] = 'active';
        $sanitized_data['created_at'] = current_time('mysql');

        // Insert entity
        $entity_id = $this->db->insert('business_entities', $sanitized_data);

        if ($entity_id) {
            // Create default categories for this entity
            $this->create_default_entity_categories($entity_id);
            
            // Create default settings
            $this->create_default_entity_settings($entity_id);

            do_action('bizmanage_entity_created', $entity_id, $sanitized_data);
        }

        return $entity_id;
    }

    /**
     * Update business entity
     * @param int $entity_id
     * @param array $data
     * @return bool
     */
    public function update_entity($entity_id, $data) {
        // Check if entity exists and user has permission
        if (!$this->user_can_edit_entity($entity_id)) {
            return false;
        }

        // Validate data
        $validation_errors = $this->validate_entity_data($data, $entity_id);
        if (!empty($validation_errors)) {
            return false;
        }

        // Sanitize data
        $sanitized_data = $this->sanitize_entity_data($data);
        $sanitized_data['updated_at'] = current_time('mysql');

        // Update entity
        $result = $this->db->update('business_entities', $sanitized_data, array('id' => $entity_id));

        if ($result !== false) {
            do_action('bizmanage_entity_updated', $entity_id, $sanitized_data);
        }

        return $result !== false;
    }

    /**
     * Delete business entity
     * @param int $entity_id
     * @return bool
     */
    public function delete_entity($entity_id) {
        // Check if entity exists and user has permission
        if (!$this->user_can_edit_entity($entity_id)) {
            return false;
        }

        // Soft delete - update status instead of removing
        $result = $this->db->update('business_entities', 
            array('status' => 'deleted', 'updated_at' => current_time('mysql')), 
            array('id' => $entity_id)
        );

        if ($result !== false) {
            do_action('bizmanage_entity_deleted', $entity_id);
        }

        return $result !== false;
    }

    /**
     * Get business entity by ID
     * @param int $entity_id
     * @return object|null
     */
    public function get_entity($entity_id) {
        return $this->db->get_row('business_entities', array('id' => $entity_id));
    }

    /**
     * Get entities for current user
     * @param array $args
     * @return array
     */
    public function get_user_entities($args = array()) {
        $defaults = array(
            'user_id' => get_current_user_id(),
            'status' => 'active',
            'order_by' => 'business_name',
            'order' => 'ASC',
        );

        $args = wp_parse_args($args, $defaults);

        return $this->db->get_results('business_entities', array(
            'where' => array(
                'user_id' => $args['user_id'],
                'status' => $args['status']
            ),
            'order_by' => $args['order_by'],
            'order' => $args['order']
        ));
    }

    /**
     * Get all entities (admin only)
     * @param array $args
     * @return array
     */
    public function get_all_entities($args = array()) {
        if (!current_user_can('bizmanage_manage_entities')) {
            return array();
        }

        $defaults = array(
            'status' => 'active',
            'order_by' => 'business_name',
            'order' => 'ASC',
            'limit' => '',
        );

        $args = wp_parse_args($args, $defaults);

        $query_args = array(
            'where' => array('status' => $args['status']),
            'order_by' => $args['order_by'],
            'order' => $args['order']
        );

        if (!empty($args['limit'])) {
            $query_args['limit'] = $args['limit'];
        }

        return $this->db->get_results('business_entities', $query_args);
    }

    /**
     * Validate entity data
     * @param array $data
     * @param int $entity_id
     * @return array
     */
    private function validate_entity_data($data, $entity_id = 0) {
        $errors = array();

        // Required fields
        if (empty($data['business_name'])) {
            $errors[] = __('Business name is required.', 'bizmanage-pro');
        }

        if (empty($data['entity_type'])) {
            $errors[] = __('Entity type is required.', 'bizmanage-pro');
        }

        // Validate entity type
        $valid_types = array_keys($this->utilities->get_business_entity_types());
        if (!empty($data['entity_type']) && !in_array($data['entity_type'], $valid_types)) {
            $errors[] = __('Invalid entity type.', 'bizmanage-pro');
        }

        // Validate email
        if (!empty($data['email'])) {
            $email_errors = $this->security->validate_input($data['email'], 'email');
            $errors = array_merge($errors, $email_errors);
        }

        // Validate website URL
        if (!empty($data['website'])) {
            $url_errors = $this->security->validate_input($data['website'], 'url');
            $errors = array_merge($errors, $url_errors);
        }

        // Validate phone number
        if (!empty($data['phone'])) {
            $phone_errors = $this->security->validate_input($data['phone'], 'phone');
            $errors = array_merge($errors, $phone_errors);
        }

        // Check for duplicate business name (excluding current entity)
        $existing_entity = $this->db->get_row('business_entities', array(
            'business_name' => $data['business_name'],
            'user_id' => get_current_user_id(),
            'status' => 'active'
        ));

        if ($existing_entity && $existing_entity->id != $entity_id) {
            $errors[] = __('A business entity with this name already exists.', 'bizmanage-pro');
        }

        return $errors;
    }

    /**
     * Sanitize entity data
     * @param array $data
     * @return array
     */
    private function sanitize_entity_data($data) {
        $sanitized = array();

        $fields = array(
            'entity_type' => 'text',
            'business_name' => 'text',
            'registration_number' => 'text',
            'tax_id' => 'text',
            'address' => 'textarea',
            'city' => 'text',
            'state' => 'text',
            'postal_code' => 'text',
            'country' => 'text',
            'phone' => 'text',
            'email' => 'email',
            'website' => 'url',
            'currency' => 'text',
            'timezone' => 'text',
        );

        foreach ($fields as $field => $type) {
            if (isset($data[$field])) {
                $sanitized[$field] = $this->security->sanitize_input($data[$field], $type);
            }
        }

        // Handle fiscal year start date
        if (isset($data['fiscal_year_start'])) {
            $sanitized['fiscal_year_start'] = $this->security->sanitize_input($data['fiscal_year_start'], 'text');
            if (!$this->security->validate_date($sanitized['fiscal_year_start'])) {
                unset($sanitized['fiscal_year_start']);
            }
        }

        return $sanitized;
    }

    /**
     * Check if user can edit entity
     * @param int $entity_id
     * @return bool
     */
    private function user_can_edit_entity($entity_id) {
        // Administrators can edit any entity
        if (current_user_can('bizmanage_manage_entities')) {
            return true;
        }

        // Check if user owns the entity
        $entity = $this->get_entity($entity_id);
        if (!$entity) {
            return false;
        }

        return $entity->user_id == get_current_user_id();
    }

    /**
     * Create default categories for entity
     * @param int $entity_id
     */
    private function create_default_entity_categories($entity_id) {
        $default_categories = array(
            // Income categories
            array('name' => 'Product Sales', 'type' => 'income', 'color' => '#28a745'),
            array('name' => 'Service Revenue', 'type' => 'income', 'color' => '#17a2b8'),
            array('name' => 'Consulting Fees', 'type' => 'income', 'color' => '#ffc107'),
            
            // Expense categories
            array('name' => 'Office Rent', 'type' => 'expense', 'color' => '#dc3545'),
            array('name' => 'Utilities', 'type' => 'expense', 'color' => '#fd7e14'),
            array('name' => 'Office Supplies', 'type' => 'expense', 'color' => '#e83e8c'),
            array('name' => 'Travel Expenses', 'type' => 'expense', 'color' => '#6f42c1'),
            array('name' => 'Marketing', 'type' => 'expense', 'color' => '#20c997'),
        );

        foreach ($default_categories as $category) {
            $category['entity_id'] = $entity_id;
            $category['status'] = 'active';
            $this->db->insert('categories', $category);
        }
    }

    /**
     * Create default settings for entity
     * @param int $entity_id
     */
    private function create_default_entity_settings($entity_id) {
        $default_settings = array(
            'default_currency' => get_option('bizmanage_pro_default_currency', 'USD'),
            'date_format' => get_option('bizmanage_pro_date_format', 'Y-m-d'),
            'time_format' => get_option('bizmanage_pro_time_format', 'H:i:s'),
            'timezone' => get_option('bizmanage_pro_timezone', 'UTC'),
            'fiscal_year_start' => get_option('bizmanage_pro_fiscal_year_start', '01-01'),
            'tax_rate' => get_option('bizmanage_pro_tax_rate', '0.00'),
        );

        foreach ($default_settings as $key => $value) {
            $this->db->insert('settings', array(
                'entity_id' => $entity_id,
                'setting_key' => $key,
                'setting_value' => $value,
            ));
        }
    }

    /**
     * Get entity statistics
     * @param int $entity_id
     * @return array
     */
    public function get_entity_statistics($entity_id) {
        if (!$this->user_can_edit_entity($entity_id)) {
            return array();
        }

        $stats = array();

        // Total transactions
        $stats['total_transactions'] = $this->db->get_count('transactions', array(
            'entity_id' => $entity_id,
            'status' => 'completed'
        ));

        // Total documents
        $stats['total_documents'] = $this->db->get_count('documents', array(
            'entity_id' => $entity_id,
            'status' => 'active'
        ));

        // Current month income
        $current_month_start = date('Y-m-01');
        $current_month_end = date('Y-m-t');
        
        $stats['current_month_income'] = $this->db->get_var(
            $this->db->prepare(
                "SELECT SUM(amount) FROM {$this->db->get_table('transactions')} 
                WHERE entity_id = %d 
                AND transaction_type = 'income' 
                AND transaction_date BETWEEN %s AND %s 
                AND status = 'completed'",
                $entity_id, $current_month_start, $current_month_end
            )
        );

        // Current month expenses
        $stats['current_month_expenses'] = $this->db->get_var(
            $this->db->prepare(
                "SELECT SUM(amount) FROM {$this->db->get_table('transactions')} 
                WHERE entity_id = %d 
                AND transaction_type = 'expense' 
                AND transaction_date BETWEEN %s AND %s 
                AND status = 'completed'",
                $entity_id, $current_month_start, $current_month_end
            )
        );

        // Calculate profit
        $stats['current_month_profit'] = floatval($stats['current_month_income']) - floatval($stats['current_month_expenses']);

        return $stats;
    }

    /**
     * Get entity types with specific field requirements
     * @return array
     */
    public function get_entity_type_fields() {
        return array(
            'sole_proprietorship' => array(
                'required_fields' => array('business_name', 'tax_id'),
                'optional_fields' => array('registration_number'),
                'description' => __('A business owned and operated by a single individual.', 'bizmanage-pro')
            ),
            'partnership' => array(
                'required_fields' => array('business_name', 'registration_number', 'tax_id'),
                'optional_fields' => array(),
                'description' => __('A business owned by two or more individuals.', 'bizmanage-pro')
            ),
            'limited_company' => array(
                'required_fields' => array('business_name', 'registration_number', 'tax_id'),
                'optional_fields' => array(),
                'description' => __('A company with limited liability for its shareholders.', 'bizmanage-pro')
            ),
            'corporation' => array(
                'required_fields' => array('business_name', 'registration_number', 'tax_id'),
                'optional_fields' => array(),
                'description' => __('A legal entity separate from its owners.', 'bizmanage-pro')
            ),
            'llc' => array(
                'required_fields' => array('business_name', 'registration_number'),
                'optional_fields' => array('tax_id'),
                'description' => __('A business structure that combines elements of corporations and partnerships.', 'bizmanage-pro')
            ),
        );
    }
}

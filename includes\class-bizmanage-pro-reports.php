<?php
/**
 * BizManage Pro Reports Class
 *
 * Handles financial report generation and export
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Reports Class
 */
class BizManage_Pro_Reports {

    /**
     * Instance of this class
     * @var BizManage_Pro_Reports
     */
    private static $instance = null;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Security instance
     * @var BizManage_Pro_Security
     */
    private $security;

    /**
     * Utilities instance
     * @var BizManage_Pro_Utilities
     */
    private $utilities;

    /**
     * Finances instance
     * @var BizManage_Pro_Finances
     */
    private $finances;

    /**
     * Get instance
     * @return BizManage_Pro_Reports
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->db = BizManage_Pro_Database::instance();
        $this->security = BizManage_Pro_Security::instance();
        $this->utilities = BizManage_Pro_Utilities::instance();
        $this->finances = BizManage_Pro_Finances::instance();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_ajax_bizmanage_generate_report', array($this, 'ajax_generate_report'));
        add_action('wp_ajax_bizmanage_export_report', array($this, 'ajax_export_report'));
    }

    /**
     * Generate Income Statement (Profit & Loss)
     * @param int $entity_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function generate_income_statement($entity_id, $start_date, $end_date) {
        $report_data = array(
            'report_type' => 'income_statement',
            'entity_id' => $entity_id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'generated_at' => current_time('mysql'),
        );

        // Get income categories breakdown
        $income_categories = $this->finances->get_category_breakdown($entity_id, 'income', $start_date, $end_date);
        $total_income = 0;
        foreach ($income_categories as $category) {
            $total_income += $category->total;
        }

        // Get expense categories breakdown
        $expense_categories = $this->finances->get_category_breakdown($entity_id, 'expense', $start_date, $end_date);
        $total_expenses = 0;
        foreach ($expense_categories as $category) {
            $total_expenses += $category->total;
        }

        // Calculate totals
        $gross_profit = $total_income;
        $net_profit = $total_income - $total_expenses;

        $report_data['income_categories'] = $income_categories;
        $report_data['expense_categories'] = $expense_categories;
        $report_data['total_income'] = $total_income;
        $report_data['total_expenses'] = $total_expenses;
        $report_data['gross_profit'] = $gross_profit;
        $report_data['net_profit'] = $net_profit;

        return $report_data;
    }

    /**
     * Generate Balance Sheet
     * @param int $entity_id
     * @param string $as_of_date
     * @return array
     */
    public function generate_balance_sheet($entity_id, $as_of_date) {
        $report_data = array(
            'report_type' => 'balance_sheet',
            'entity_id' => $entity_id,
            'as_of_date' => $as_of_date,
            'generated_at' => current_time('mysql'),
        );

        // Get all transactions up to the date
        $transactions = $this->finances->get_transactions(array(
            'entity_id' => $entity_id,
            'end_date' => $as_of_date,
            'status' => 'completed'
        ));

        // Calculate assets (simplified - cash and receivables)
        $cash_balance = 0;
        foreach ($transactions as $transaction) {
            if ($transaction->transaction_type === 'income') {
                $cash_balance += $transaction->base_amount;
            } else {
                $cash_balance -= $transaction->base_amount;
            }
        }

        // Assets
        $current_assets = array(
            'cash' => $cash_balance,
            'accounts_receivable' => 0, // Placeholder for future implementation
            'inventory' => 0, // Placeholder for future implementation
        );
        $total_current_assets = array_sum($current_assets);

        $fixed_assets = array(
            'equipment' => 0, // Placeholder for future implementation
            'furniture' => 0, // Placeholder for future implementation
        );
        $total_fixed_assets = array_sum($fixed_assets);

        $total_assets = $total_current_assets + $total_fixed_assets;

        // Liabilities (simplified)
        $current_liabilities = array(
            'accounts_payable' => 0, // Placeholder for future implementation
            'short_term_loans' => 0, // Placeholder for future implementation
        );
        $total_current_liabilities = array_sum($current_liabilities);

        $long_term_liabilities = array(
            'long_term_loans' => 0, // Placeholder for future implementation
        );
        $total_long_term_liabilities = array_sum($long_term_liabilities);

        $total_liabilities = $total_current_liabilities + $total_long_term_liabilities;

        // Equity
        $equity = array(
            'retained_earnings' => $total_assets - $total_liabilities,
        );
        $total_equity = array_sum($equity);

        $report_data['current_assets'] = $current_assets;
        $report_data['fixed_assets'] = $fixed_assets;
        $report_data['total_current_assets'] = $total_current_assets;
        $report_data['total_fixed_assets'] = $total_fixed_assets;
        $report_data['total_assets'] = $total_assets;
        $report_data['current_liabilities'] = $current_liabilities;
        $report_data['long_term_liabilities'] = $long_term_liabilities;
        $report_data['total_current_liabilities'] = $total_current_liabilities;
        $report_data['total_long_term_liabilities'] = $total_long_term_liabilities;
        $report_data['total_liabilities'] = $total_liabilities;
        $report_data['equity'] = $equity;
        $report_data['total_equity'] = $total_equity;

        return $report_data;
    }

    /**
     * Generate Cash Flow Statement
     * @param int $entity_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function generate_cash_flow($entity_id, $start_date, $end_date) {
        $report_data = array(
            'report_type' => 'cash_flow',
            'entity_id' => $entity_id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'generated_at' => current_time('mysql'),
        );

        // Get transactions for the period
        $transactions = $this->finances->get_transactions(array(
            'entity_id' => $entity_id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'status' => 'completed'
        ));

        // Operating Activities
        $operating_income = 0;
        $operating_expenses = 0;

        foreach ($transactions as $transaction) {
            if ($transaction->transaction_type === 'income') {
                $operating_income += $transaction->base_amount;
            } else {
                $operating_expenses += $transaction->base_amount;
            }
        }

        $net_cash_from_operations = $operating_income - $operating_expenses;

        // Investing Activities (placeholder for future implementation)
        $investing_activities = array(
            'equipment_purchases' => 0,
            'asset_sales' => 0,
        );
        $net_cash_from_investing = array_sum($investing_activities);

        // Financing Activities (placeholder for future implementation)
        $financing_activities = array(
            'loan_proceeds' => 0,
            'loan_payments' => 0,
            'owner_contributions' => 0,
            'owner_withdrawals' => 0,
        );
        $net_cash_from_financing = array_sum($financing_activities);

        // Net change in cash
        $net_change_in_cash = $net_cash_from_operations + $net_cash_from_investing + $net_cash_from_financing;

        $report_data['operating_income'] = $operating_income;
        $report_data['operating_expenses'] = $operating_expenses;
        $report_data['net_cash_from_operations'] = $net_cash_from_operations;
        $report_data['investing_activities'] = $investing_activities;
        $report_data['net_cash_from_investing'] = $net_cash_from_investing;
        $report_data['financing_activities'] = $financing_activities;
        $report_data['net_cash_from_financing'] = $net_cash_from_financing;
        $report_data['net_change_in_cash'] = $net_change_in_cash;

        return $report_data;
    }

    /**
     * Generate Tax Summary Report
     * @param int $entity_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function generate_tax_summary($entity_id, $start_date, $end_date) {
        $report_data = array(
            'report_type' => 'tax_summary',
            'entity_id' => $entity_id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'generated_at' => current_time('mysql'),
        );

        // Get all transactions with tax information
        $transactions = $this->finances->get_transactions(array(
            'entity_id' => $entity_id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'status' => 'completed'
        ));

        $total_income = 0;
        $total_expenses = 0;
        $total_tax_collected = 0;
        $total_tax_paid = 0;
        $deductible_expenses = 0;

        foreach ($transactions as $transaction) {
            if ($transaction->transaction_type === 'income') {
                $total_income += $transaction->base_amount;
                if ($transaction->tax_amount > 0) {
                    $total_tax_collected += $transaction->tax_amount;
                }
            } else {
                $total_expenses += $transaction->base_amount;
                if ($transaction->tax_amount > 0) {
                    $total_tax_paid += $transaction->tax_amount;
                }
                // Most business expenses are deductible
                $deductible_expenses += $transaction->base_amount;
            }
        }

        $taxable_income = $total_income - $deductible_expenses;
        $net_tax_liability = $total_tax_collected - $total_tax_paid;

        $report_data['total_income'] = $total_income;
        $report_data['total_expenses'] = $total_expenses;
        $report_data['deductible_expenses'] = $deductible_expenses;
        $report_data['taxable_income'] = $taxable_income;
        $report_data['total_tax_collected'] = $total_tax_collected;
        $report_data['total_tax_paid'] = $total_tax_paid;
        $report_data['net_tax_liability'] = $net_tax_liability;

        return $report_data;
    }

    /**
     * Export report to PDF
     * @param array $report_data
     * @return string|false
     */
    public function export_to_pdf($report_data) {
        // This is a simplified implementation
        // In a production environment, you would use a library like TCPDF or DOMPDF
        
        $html = $this->generate_report_html($report_data);
        
        // For now, we'll create a simple HTML file that can be printed to PDF
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/bizmanage-pro/exports';
        
        if (!file_exists($export_dir)) {
            wp_mkdir_p($export_dir);
        }
        
        $filename = 'report_' . $report_data['report_type'] . '_' . date('Y-m-d_H-i-s') . '.html';
        $file_path = $export_dir . '/' . $filename;
        
        if (file_put_contents($file_path, $html)) {
            return $file_path;
        }
        
        return false;
    }

    /**
     * Export report to Excel (CSV format)
     * @param array $report_data
     * @return string|false
     */
    public function export_to_excel($report_data) {
        $csv_data = $this->generate_report_csv($report_data);
        
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/bizmanage-pro/exports';
        
        if (!file_exists($export_dir)) {
            wp_mkdir_p($export_dir);
        }
        
        $filename = 'report_' . $report_data['report_type'] . '_' . date('Y-m-d_H-i-s') . '.csv';
        $file_path = $export_dir . '/' . $filename;
        
        if (file_put_contents($file_path, $csv_data)) {
            return $file_path;
        }
        
        return false;
    }

    /**
     * Generate HTML for report
     * @param array $report_data
     * @return string
     */
    private function generate_report_html($report_data) {
        $html = '<!DOCTYPE html><html><head><meta charset="UTF-8">';
        $html .= '<title>' . ucwords(str_replace('_', ' ', $report_data['report_type'])) . '</title>';
        $html .= '<style>body{font-family:Arial,sans-serif;margin:20px;}table{width:100%;border-collapse:collapse;}th,td{border:1px solid #ddd;padding:8px;text-align:left;}th{background-color:#f2f2f2;}.text-right{text-align:right;}.total{font-weight:bold;background-color:#f9f9f9;}</style>';
        $html .= '</head><body>';
        
        $html .= '<h1>' . ucwords(str_replace('_', ' ', $report_data['report_type'])) . '</h1>';
        $html .= '<p>Generated on: ' . date('Y-m-d H:i:s') . '</p>';
        
        switch ($report_data['report_type']) {
            case 'income_statement':
                $html .= $this->generate_income_statement_html($report_data);
                break;
            case 'balance_sheet':
                $html .= $this->generate_balance_sheet_html($report_data);
                break;
            case 'cash_flow':
                $html .= $this->generate_cash_flow_html($report_data);
                break;
            case 'tax_summary':
                $html .= $this->generate_tax_summary_html($report_data);
                break;
        }
        
        $html .= '</body></html>';
        return $html;
    }

    /**
     * Generate CSV for report
     * @param array $report_data
     * @return string
     */
    private function generate_report_csv($report_data) {
        $csv = '';
        
        switch ($report_data['report_type']) {
            case 'income_statement':
                $csv = $this->generate_income_statement_csv($report_data);
                break;
            case 'balance_sheet':
                $csv = $this->generate_balance_sheet_csv($report_data);
                break;
            case 'cash_flow':
                $csv = $this->generate_cash_flow_csv($report_data);
                break;
            case 'tax_summary':
                $csv = $this->generate_tax_summary_csv($report_data);
                break;
        }
        
        return $csv;
    }

    /**
     * Generate Income Statement HTML
     * @param array $data
     * @return string
     */
    private function generate_income_statement_html($data) {
        $html = '<h2>Income Statement</h2>';
        $html .= '<p>Period: ' . $data['start_date'] . ' to ' . $data['end_date'] . '</p>';
        
        $html .= '<table>';
        $html .= '<tr><th>Category</th><th class="text-right">Amount</th></tr>';
        
        // Income section
        $html .= '<tr class="total"><td colspan="2"><strong>INCOME</strong></td></tr>';
        foreach ($data['income_categories'] as $category) {
            $html .= '<tr><td>' . esc_html($category->category) . '</td><td class="text-right">' . $this->utilities->format_currency($category->total) . '</td></tr>';
        }
        $html .= '<tr class="total"><td><strong>Total Income</strong></td><td class="text-right"><strong>' . $this->utilities->format_currency($data['total_income']) . '</strong></td></tr>';
        
        // Expenses section
        $html .= '<tr class="total"><td colspan="2"><strong>EXPENSES</strong></td></tr>';
        foreach ($data['expense_categories'] as $category) {
            $html .= '<tr><td>' . esc_html($category->category) . '</td><td class="text-right">' . $this->utilities->format_currency($category->total) . '</td></tr>';
        }
        $html .= '<tr class="total"><td><strong>Total Expenses</strong></td><td class="text-right"><strong>' . $this->utilities->format_currency($data['total_expenses']) . '</strong></td></tr>';
        
        // Net profit
        $html .= '<tr class="total"><td><strong>Net Profit</strong></td><td class="text-right"><strong>' . $this->utilities->format_currency($data['net_profit']) . '</strong></td></tr>';
        
        $html .= '</table>';
        return $html;
    }

    /**
     * Generate Income Statement CSV
     * @param array $data
     * @return string
     */
    private function generate_income_statement_csv($data) {
        $csv = "Income Statement\n";
        $csv .= "Period: " . $data['start_date'] . " to " . $data['end_date'] . "\n\n";
        $csv .= "Category,Amount\n";
        
        $csv .= "INCOME,\n";
        foreach ($data['income_categories'] as $category) {
            $csv .= '"' . $category->category . '",' . $category->total . "\n";
        }
        $csv .= "Total Income," . $data['total_income'] . "\n\n";
        
        $csv .= "EXPENSES,\n";
        foreach ($data['expense_categories'] as $category) {
            $csv .= '"' . $category->category . '",' . $category->total . "\n";
        }
        $csv .= "Total Expenses," . $data['total_expenses'] . "\n\n";
        
        $csv .= "Net Profit," . $data['net_profit'] . "\n";
        
        return $csv;
    }

    /**
     * AJAX handler for generating reports
     */
    public function ajax_generate_report() {
        // Verify nonce and permissions
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'bizmanage_ajax')) {
            wp_send_json_error(__('Security check failed.', 'bizmanage-pro'));
        }

        if (!current_user_can('bizmanage_view_reports')) {
            wp_send_json_error(__('You do not have permission to generate reports.', 'bizmanage-pro'));
        }

        $report_type = sanitize_text_field($_POST['report_type'] ?? '');
        $entity_id = intval($_POST['entity_id'] ?? 0);
        $start_date = sanitize_text_field($_POST['start_date'] ?? '');
        $end_date = sanitize_text_field($_POST['end_date'] ?? '');

        if (empty($report_type) || $entity_id <= 0) {
            wp_send_json_error(__('Invalid report parameters.', 'bizmanage-pro'));
        }

        try {
            switch ($report_type) {
                case 'income_statement':
                    $report_data = $this->generate_income_statement($entity_id, $start_date, $end_date);
                    break;
                case 'balance_sheet':
                    $report_data = $this->generate_balance_sheet($entity_id, $end_date);
                    break;
                case 'cash_flow':
                    $report_data = $this->generate_cash_flow($entity_id, $start_date, $end_date);
                    break;
                case 'tax_summary':
                    $report_data = $this->generate_tax_summary($entity_id, $start_date, $end_date);
                    break;
                default:
                    wp_send_json_error(__('Invalid report type.', 'bizmanage-pro'));
            }

            wp_send_json_success($report_data);
        } catch (Exception $e) {
            wp_send_json_error(__('Error generating report: ', 'bizmanage-pro') . $e->getMessage());
        }
    }

    /**
     * AJAX handler for exporting reports
     */
    public function ajax_export_report() {
        // Verify nonce and permissions
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'bizmanage_ajax')) {
            wp_send_json_error(__('Security check failed.', 'bizmanage-pro'));
        }

        if (!current_user_can('bizmanage_export_reports')) {
            wp_send_json_error(__('You do not have permission to export reports.', 'bizmanage-pro'));
        }

        $report_data = json_decode(stripslashes($_POST['report_data'] ?? ''), true);
        $export_format = sanitize_text_field($_POST['export_format'] ?? 'pdf');

        if (empty($report_data)) {
            wp_send_json_error(__('No report data provided.', 'bizmanage-pro'));
        }

        try {
            if ($export_format === 'excel') {
                $file_path = $this->export_to_excel($report_data);
            } else {
                $file_path = $this->export_to_pdf($report_data);
            }

            if ($file_path) {
                $upload_dir = wp_upload_dir();
                $file_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $file_path);
                wp_send_json_success(array('download_url' => $file_url));
            } else {
                wp_send_json_error(__('Failed to export report.', 'bizmanage-pro'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error exporting report: ', 'bizmanage-pro') . $e->getMessage());
        }
    }

    /**
     * Generate Balance Sheet HTML (simplified implementation)
     */
    private function generate_balance_sheet_html($data) {
        $html = '<h2>Balance Sheet</h2>';
        $html .= '<p>As of: ' . $data['as_of_date'] . '</p>';
        $html .= '<p><em>Note: This is a simplified balance sheet. For complete financial reporting, consult with an accountant.</em></p>';
        return $html;
    }

    /**
     * Generate Cash Flow HTML (simplified implementation)
     */
    private function generate_cash_flow_html($data) {
        $html = '<h2>Cash Flow Statement</h2>';
        $html .= '<p>Period: ' . $data['start_date'] . ' to ' . $data['end_date'] . '</p>';
        $html .= '<p><em>Note: This is a simplified cash flow statement. For complete financial reporting, consult with an accountant.</em></p>';
        return $html;
    }

    /**
     * Generate Tax Summary HTML (simplified implementation)
     */
    private function generate_tax_summary_html($data) {
        $html = '<h2>Tax Summary</h2>';
        $html .= '<p>Period: ' . $data['start_date'] . ' to ' . $data['end_date'] . '</p>';
        $html .= '<p><em>Note: This is a simplified tax summary. Consult with a tax professional for complete tax reporting.</em></p>';
        return $html;
    }

    /**
     * Generate Balance Sheet CSV (simplified implementation)
     */
    private function generate_balance_sheet_csv($data) {
        return "Balance Sheet\nAs of: " . $data['as_of_date'] . "\n";
    }

    /**
     * Generate Cash Flow CSV (simplified implementation)
     */
    private function generate_cash_flow_csv($data) {
        return "Cash Flow Statement\nPeriod: " . $data['start_date'] . " to " . $data['end_date'] . "\n";
    }

    /**
     * Generate Tax Summary CSV (simplified implementation)
     */
    private function generate_tax_summary_csv($data) {
        return "Tax Summary\nPeriod: " . $data['start_date'] . " to " . $data['end_date'] . "\n";
    }
}

<?php
/**
 * BizManage Pro Public Class
 *
 * Handles frontend functionality
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Public Class
 */
class BizManage_Pro_Public {

    /**
     * Instance of this class
     * @var BizManage_Pro_Public
     */
    private static $instance = null;

    /**
     * Security instance
     * @var BizManage_Pro_Security
     */
    private $security;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Utilities instance
     * @var BizManage_Pro_Utilities
     */
    private $utilities;

    /**
     * Get instance
     * @return BizManage_Pro_Public
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->security = BizManage_Pro_Security::instance();
        $this->db = BizManage_Pro_Database::instance();
        $this->utilities = BizManage_Pro_Utilities::instance();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_public_scripts'));
        add_action('init', array($this, 'init_shortcodes'));
        add_action('wp_ajax_nopriv_bizmanage_public_action', array($this, 'handle_public_ajax'));
    }

    /**
     * Enqueue public scripts and styles
     */
    public function enqueue_public_scripts() {
        // Only enqueue on pages that use BizManage Pro shortcodes or widgets
        if (!$this->should_enqueue_scripts()) {
            return;
        }

        // Enqueue styles
        wp_enqueue_style(
            'bizmanage-pro-public',
            BIZMANAGE_PRO_PLUGIN_URL . 'assets/css/public.css',
            array(),
            BIZMANAGE_PRO_VERSION
        );

        // Enqueue Bootstrap CSS
        wp_enqueue_style(
            'bizmanage-pro-bootstrap-public',
            'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
            array(),
            '5.1.3'
        );

        // Enqueue Chart.js for public reports
        wp_enqueue_script(
            'bizmanage-pro-chartjs-public',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            array(),
            '3.9.1',
            true
        );

        // Enqueue public scripts
        wp_enqueue_script(
            'bizmanage-pro-public',
            BIZMANAGE_PRO_PLUGIN_URL . 'assets/js/public.js',
            array('jquery', 'bizmanage-pro-chartjs-public'),
            BIZMANAGE_PRO_VERSION,
            true
        );

        // Localize script
        wp_localize_script('bizmanage-pro-public', 'bizmanagePublic', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => $this->security->create_nonce('bizmanage_public'),
            'strings' => array(
                'loading' => __('Loading...', 'bizmanage-pro'),
                'error' => __('An error occurred. Please try again.', 'bizmanage-pro'),
                'no_data' => __('No data available.', 'bizmanage-pro'),
            )
        ));
    }

    /**
     * Check if scripts should be enqueued
     * @return bool
     */
    private function should_enqueue_scripts() {
        global $post;

        if (is_admin()) {
            return false;
        }

        // Check if current page has BizManage shortcodes
        if ($post && has_shortcode($post->post_content, 'bizmanage_dashboard')) {
            return true;
        }

        if ($post && has_shortcode($post->post_content, 'bizmanage_reports')) {
            return true;
        }

        if ($post && has_shortcode($post->post_content, 'bizmanage_expense_form')) {
            return true;
        }

        return false;
    }

    /**
     * Initialize shortcodes
     */
    public function init_shortcodes() {
        add_shortcode('bizmanage_dashboard', array($this, 'dashboard_shortcode'));
        add_shortcode('bizmanage_reports', array($this, 'reports_shortcode'));
        add_shortcode('bizmanage_expense_form', array($this, 'expense_form_shortcode'));
        add_shortcode('bizmanage_client_portal', array($this, 'client_portal_shortcode'));
    }

    /**
     * Dashboard shortcode
     * @param array $atts
     * @return string
     */
    public function dashboard_shortcode($atts) {
        $atts = shortcode_atts(array(
            'entity_id' => 0,
            'period' => 'month',
            'show_charts' => 'true',
        ), $atts);

        if (!current_user_can('bizmanage_view_finances')) {
            return '<p>' . __('You do not have permission to view this content.', 'bizmanage-pro') . '</p>';
        }

        ob_start();
        $this->render_dashboard($atts);
        return ob_get_clean();
    }

    /**
     * Reports shortcode
     * @param array $atts
     * @return string
     */
    public function reports_shortcode($atts) {
        $atts = shortcode_atts(array(
            'entity_id' => 0,
            'report_type' => 'income_statement',
            'period' => 'month',
        ), $atts);

        if (!current_user_can('bizmanage_view_reports')) {
            return '<p>' . __('You do not have permission to view this content.', 'bizmanage-pro') . '</p>';
        }

        ob_start();
        $this->render_reports($atts);
        return ob_get_clean();
    }

    /**
     * Expense form shortcode
     * @param array $atts
     * @return string
     */
    public function expense_form_shortcode($atts) {
        $atts = shortcode_atts(array(
            'entity_id' => 0,
            'redirect_url' => '',
        ), $atts);

        if (!current_user_can('bizmanage_submit_expenses')) {
            return '<p>' . __('You do not have permission to submit expenses.', 'bizmanage-pro') . '</p>';
        }

        ob_start();
        $this->render_expense_form($atts);
        return ob_get_clean();
    }

    /**
     * Client portal shortcode
     * @param array $atts
     * @return string
     */
    public function client_portal_shortcode($atts) {
        $atts = shortcode_atts(array(
            'entity_id' => 0,
        ), $atts);

        if (!current_user_can('bizmanage_view_client_reports')) {
            return '<p>' . __('You do not have permission to view this content.', 'bizmanage-pro') . '</p>';
        }

        ob_start();
        $this->render_client_portal($atts);
        return ob_get_clean();
    }

    /**
     * Render dashboard
     * @param array $atts
     */
    private function render_dashboard($atts) {
        $entity_id = intval($atts['entity_id']);
        $period = sanitize_text_field($atts['period']);
        $show_charts = $atts['show_charts'] === 'true';

        // Get dashboard data
        $dashboard_data = $this->get_dashboard_data($entity_id, $period);

        echo '<div class="bizmanage-dashboard">';
        echo '<div class="row">';

        // Summary cards
        echo '<div class="col-md-4">';
        echo '<div class="card text-white bg-success">';
        echo '<div class="card-body">';
        echo '<h5 class="card-title">' . __('Total Income', 'bizmanage-pro') . '</h5>';
        echo '<h3>' . $this->utilities->format_currency($dashboard_data['income_total']) . '</h3>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '<div class="col-md-4">';
        echo '<div class="card text-white bg-danger">';
        echo '<div class="card-body">';
        echo '<h5 class="card-title">' . __('Total Expenses', 'bizmanage-pro') . '</h5>';
        echo '<h3>' . $this->utilities->format_currency($dashboard_data['expense_total']) . '</h3>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '<div class="col-md-4">';
        $profit_class = $dashboard_data['profit'] >= 0 ? 'bg-primary' : 'bg-warning';
        echo '<div class="card text-white ' . $profit_class . '">';
        echo '<div class="card-body">';
        echo '<h5 class="card-title">' . __('Net Profit', 'bizmanage-pro') . '</h5>';
        echo '<h3>' . $this->utilities->format_currency($dashboard_data['profit']) . '</h3>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '</div>'; // End row

        if ($show_charts) {
            echo '<div class="row mt-4">';
            echo '<div class="col-md-12">';
            echo '<canvas id="bizmanage-chart" width="400" height="200"></canvas>';
            echo '</div>';
            echo '</div>';
        }

        echo '</div>'; // End dashboard
    }

    /**
     * Render reports
     * @param array $atts
     */
    private function render_reports($atts) {
        $entity_id = intval($atts['entity_id']);
        $report_type = sanitize_text_field($atts['report_type']);
        $period = sanitize_text_field($atts['period']);

        echo '<div class="bizmanage-reports">';
        echo '<h3>' . __('Financial Reports', 'bizmanage-pro') . '</h3>';

        // Report selector
        echo '<div class="mb-3">';
        echo '<select id="report-type-selector" class="form-select">';
        echo '<option value="income_statement"' . selected($report_type, 'income_statement', false) . '>' . __('Income Statement', 'bizmanage-pro') . '</option>';
        echo '<option value="balance_sheet"' . selected($report_type, 'balance_sheet', false) . '>' . __('Balance Sheet', 'bizmanage-pro') . '</option>';
        echo '<option value="cash_flow"' . selected($report_type, 'cash_flow', false) . '>' . __('Cash Flow', 'bizmanage-pro') . '</option>';
        echo '</select>';
        echo '</div>';

        echo '<div id="report-content">';
        // Report content will be loaded via AJAX
        echo '</div>';

        echo '</div>';
    }

    /**
     * Render expense form
     * @param array $atts
     */
    private function render_expense_form($atts) {
        $entity_id = intval($atts['entity_id']);

        echo '<div class="bizmanage-expense-form">';
        echo '<h3>' . __('Submit Expense', 'bizmanage-pro') . '</h3>';

        echo '<form id="expense-form" method="post">';
        echo wp_nonce_field('bizmanage_submit_expense', 'expense_nonce', true, false);
        echo '<input type="hidden" name="entity_id" value="' . esc_attr($entity_id) . '">';

        echo '<div class="mb-3">';
        echo '<label for="expense-amount" class="form-label">' . __('Amount', 'bizmanage-pro') . '</label>';
        echo '<input type="number" step="0.01" class="form-control" id="expense-amount" name="amount" required>';
        echo '</div>';

        echo '<div class="mb-3">';
        echo '<label for="expense-description" class="form-label">' . __('Description', 'bizmanage-pro') . '</label>';
        echo '<textarea class="form-control" id="expense-description" name="description" rows="3" required></textarea>';
        echo '</div>';

        echo '<div class="mb-3">';
        echo '<label for="expense-category" class="form-label">' . __('Category', 'bizmanage-pro') . '</label>';
        echo '<select class="form-select" id="expense-category" name="category" required>';
        
        $categories = $this->get_expense_categories();
        foreach ($categories as $category) {
            echo '<option value="' . esc_attr($category->name) . '">' . esc_html($category->name) . '</option>';
        }
        
        echo '</select>';
        echo '</div>';

        echo '<div class="mb-3">';
        echo '<label for="expense-date" class="form-label">' . __('Date', 'bizmanage-pro') . '</label>';
        echo '<input type="date" class="form-control" id="expense-date" name="transaction_date" value="' . current_time('Y-m-d') . '" required>';
        echo '</div>';

        echo '<button type="submit" class="btn btn-primary">' . __('Submit Expense', 'bizmanage-pro') . '</button>';
        echo '</form>';

        echo '</div>';
    }

    /**
     * Render client portal
     * @param array $atts
     */
    private function render_client_portal($atts) {
        $entity_id = intval($atts['entity_id']);

        echo '<div class="bizmanage-client-portal">';
        echo '<h3>' . __('Client Portal', 'bizmanage-pro') . '</h3>';

        // Client-specific reports and data
        echo '<div class="row">';
        echo '<div class="col-md-6">';
        echo '<div class="card">';
        echo '<div class="card-body">';
        echo '<h5 class="card-title">' . __('Recent Transactions', 'bizmanage-pro') . '</h5>';
        echo '<div id="client-transactions"></div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '<div class="col-md-6">';
        echo '<div class="card">';
        echo '<div class="card-body">';
        echo '<h5 class="card-title">' . __('Account Summary', 'bizmanage-pro') . '</h5>';
        echo '<div id="client-summary"></div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '</div>'; // End row
        echo '</div>'; // End client portal
    }

    /**
     * Get dashboard data
     * @param int $entity_id
     * @param string $period
     * @return array
     */
    private function get_dashboard_data($entity_id, $period) {
        // Calculate date range
        $end_date = current_time('Y-m-d');
        switch ($period) {
            case 'week':
                $start_date = date('Y-m-d', strtotime('-7 days'));
                break;
            case 'month':
                $start_date = date('Y-m-d', strtotime('-30 days'));
                break;
            case 'quarter':
                $start_date = date('Y-m-d', strtotime('-90 days'));
                break;
            case 'year':
                $start_date = date('Y-m-d', strtotime('-365 days'));
                break;
            default:
                $start_date = date('Y-m-d', strtotime('-30 days'));
        }

        $where_sql = "transaction_date BETWEEN %s AND %s AND status = 'completed'";
        $where_params = array($start_date, $end_date);

        if ($entity_id > 0) {
            $where_sql .= " AND entity_id = %d";
            $where_params[] = $entity_id;
        }

        // Get income total
        $income_sql = "SELECT SUM(amount) FROM {$this->db->get_table('transactions')} WHERE transaction_type = 'income' AND $where_sql";
        $income_total = $this->db->get_var($this->db->prepare($income_sql, ...$where_params));

        // Get expense total
        $expense_sql = "SELECT SUM(amount) FROM {$this->db->get_table('transactions')} WHERE transaction_type = 'expense' AND $where_sql";
        $expense_total = $this->db->get_var($this->db->prepare($expense_sql, ...$where_params));

        return array(
            'income_total' => floatval($income_total),
            'expense_total' => floatval($expense_total),
            'profit' => floatval($income_total) - floatval($expense_total),
            'period' => $period,
            'start_date' => $start_date,
            'end_date' => $end_date,
        );
    }

    /**
     * Get expense categories
     * @return array
     */
    private function get_expense_categories() {
        return $this->db->get_results('categories', array(
            'where' => array(
                'type' => 'expense',
                'status' => 'active'
            ),
            'order_by' => 'name',
            'order' => 'ASC'
        ));
    }

    /**
     * Handle public AJAX requests
     */
    public function handle_public_ajax() {
        // Handle public AJAX requests here
        wp_die();
    }
}

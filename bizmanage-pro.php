<?php
/**
 * Plugin Name: BizManage Pro - Smart Accounting & Document Management
 * Plugin URI: https://bizmanagepro.com
 * Description: Comprehensive accounting and document management solution for small businesses with multi-entity support, financial reporting, and secure document storage.
 * Version: 1.0.0
 * Author: BizManage Pro Team
 * Author URI: https://bizmanagepro.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: bizmanage-pro
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package BizManagePro
 * @version 1.0.0
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('BIZMANAGE_PRO_VERSION', '1.0.0');
define('BIZMANAGE_PRO_PLUGIN_FILE', __FILE__);
define('BIZMANAGE_PRO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BIZMANAGE_PRO_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BIZMANAGE_PRO_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('BIZMANAGE_PRO_TEXT_DOMAIN', 'bizmanage-pro');

// Minimum WordPress version required
define('BIZMANAGE_PRO_MIN_WP_VERSION', '5.0');

// Database version for schema updates
define('BIZMANAGE_PRO_DB_VERSION', '1.0.0');

/**
 * Check if WordPress version is compatible
 */
function bizmanage_pro_check_wp_version() {
    global $wp_version;
    
    if (version_compare($wp_version, BIZMANAGE_PRO_MIN_WP_VERSION, '<')) {
        deactivate_plugins(BIZMANAGE_PRO_PLUGIN_BASENAME);
        wp_die(
            sprintf(
                __('BizManage Pro requires WordPress version %s or higher. You are running version %s. Please upgrade WordPress.', 'bizmanage-pro'),
                BIZMANAGE_PRO_MIN_WP_VERSION,
                $wp_version
            )
        );
    }
}

/**
 * Check if PHP version is compatible
 */
function bizmanage_pro_check_php_version() {
    if (version_compare(PHP_VERSION, '7.4', '<')) {
        deactivate_plugins(BIZMANAGE_PRO_PLUGIN_BASENAME);
        wp_die(
            sprintf(
                __('BizManage Pro requires PHP version 7.4 or higher. You are running version %s. Please upgrade PHP.', 'bizmanage-pro'),
                PHP_VERSION
            )
        );
    }
}

// Check compatibility on activation
register_activation_hook(__FILE__, 'bizmanage_pro_check_wp_version');
register_activation_hook(__FILE__, 'bizmanage_pro_check_php_version');

/**
 * Main BizManage Pro Class
 */
final class BizManage_Pro {
    
    /**
     * Plugin instance
     * @var BizManage_Pro
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     * @return BizManage_Pro
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->includes();
        $this->init();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'load_textdomain'));
        add_action('plugins_loaded', array($this, 'plugins_loaded'));

        // Debug capability fix
        add_action('init', array($this, 'debug_fix_capabilities'));

        // Activation and deactivation hooks
        register_activation_hook(BIZMANAGE_PRO_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(BIZMANAGE_PRO_PLUGIN_FILE, array($this, 'deactivate'));
        register_uninstall_hook(BIZMANAGE_PRO_PLUGIN_FILE, array('BizManage_Pro', 'uninstall'));
    }
    
    /**
     * Include required files
     */
    private function includes() {
        // Core classes
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-installer.php';
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-database.php';
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-security.php';
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-utilities.php';
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-roles.php';

        // Business logic classes
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-entities.php';
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-documents.php';
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-finances.php';
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-reports.php';
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-settings.php';
        
        // Admin classes
        if (is_admin()) {
            require_once BIZMANAGE_PRO_PLUGIN_DIR . 'admin/class-bizmanage-pro-admin.php';
        }
        
        // Frontend classes
        if (!is_admin()) {
            require_once BIZMANAGE_PRO_PLUGIN_DIR . 'public/class-bizmanage-pro-public.php';
        }
        
        // AJAX handlers
        require_once BIZMANAGE_PRO_PLUGIN_DIR . 'includes/class-bizmanage-pro-ajax.php';
    }
    
    /**
     * Initialize plugin
     */
    private function init() {
        // Initialize core components
        BizManage_Pro_Database::instance();
        BizManage_Pro_Security::instance();
        BizManage_Pro_Utilities::instance();
        BizManage_Pro_Roles::instance();
        BizManage_Pro_Ajax::instance();

        // Initialize business logic components
        BizManage_Pro_Entities::instance();
        BizManage_Pro_Documents::instance();
        BizManage_Pro_Finances::instance();
        BizManage_Pro_Reports::instance();
        BizManage_Pro_Settings::instance();

        // Ensure administrators have all capabilities
        $this->ensure_admin_capabilities();
        
        // Initialize admin or frontend
        if (is_admin()) {
            BizManage_Pro_Admin::instance();
        } else {
            BizManage_Pro_Public::instance();
        }
    }

    /**
     * Ensure administrators have all BizManage capabilities
     */
    private function ensure_admin_capabilities() {
        $admin_role = get_role('administrator');
        if (!$admin_role) {
            return;
        }

        $capabilities = array(
            'bizmanage_manage_entities',
            'bizmanage_view_entities',
            'bizmanage_manage_documents',
            'bizmanage_view_documents',
            'bizmanage_upload_documents',
            'bizmanage_delete_documents',
            'bizmanage_manage_finances',
            'bizmanage_view_finances',
            'bizmanage_limited_finances',
            'bizmanage_submit_expenses',
            'bizmanage_view_reports',
            'bizmanage_view_client_reports',
            'bizmanage_export_reports',
            'bizmanage_manage_users',
            'bizmanage_view_users',
            'bizmanage_manage_settings',
            'bizmanage_view_settings',
            'bizmanage_export_data',
            'bizmanage_import_data',
            'bizmanage_backup_data',
            'bizmanage_view_own_data',
        );

        foreach ($capabilities as $cap) {
            if (!$admin_role->has_cap($cap)) {
                $admin_role->add_cap($cap);
            }
        }
    }

    /**
     * Debug method to fix capabilities
     */
    public function debug_fix_capabilities() {
        // Only run if debug parameter is present and user is admin
        if (isset($_GET['bizmanage_fix_caps']) && current_user_can('manage_options')) {
            $this->ensure_admin_capabilities();
            wp_die('BizManage Pro capabilities have been added to administrators. <a href="' . admin_url('admin.php?page=bizmanage-dashboard') . '">Go to Dashboard</a>');
        }
    }

    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'bizmanage-pro',
            false,
            dirname(BIZMANAGE_PRO_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Plugins loaded hook
     */
    public function plugins_loaded() {
        // Check for required plugins or dependencies
        do_action('bizmanage_pro_loaded');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check system requirements
        bizmanage_pro_check_wp_version();
        bizmanage_pro_check_php_version();
        
        // Create database tables
        BizManage_Pro_Installer::create_tables();
        
        // Create user roles
        BizManage_Pro_Installer::create_roles();
        
        // Create upload directories
        BizManage_Pro_Installer::create_directories();
        
        // Set default options
        BizManage_Pro_Installer::set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set activation flag
        update_option('bizmanage_pro_activated', true);
        
        do_action('bizmanage_pro_activated');
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Clear scheduled events
        wp_clear_scheduled_hook('bizmanage_pro_daily_backup');
        wp_clear_scheduled_hook('bizmanage_pro_weekly_cleanup');
        
        do_action('bizmanage_pro_deactivated');
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Only proceed if user has proper permissions
        if (!current_user_can('activate_plugins')) {
            return;
        }
        
        // Check if we should remove data on uninstall
        $remove_data = get_option('bizmanage_pro_remove_data_on_uninstall', false);
        
        if ($remove_data) {
            // Remove database tables
            BizManage_Pro_Installer::remove_tables();
            
            // Remove user roles
            BizManage_Pro_Installer::remove_roles();
            
            // Remove options
            BizManage_Pro_Installer::remove_options();
            
            // Remove upload directories
            BizManage_Pro_Installer::remove_directories();
        }
        
        do_action('bizmanage_pro_uninstalled');
    }
}

/**
 * Initialize the plugin
 */
function bizmanage_pro() {
    return BizManage_Pro::instance();
}

// Start the plugin
bizmanage_pro();

# BizManage Pro - Deployment Guide

This guide covers deploying BizManage Pro to production environments and ensuring optimal performance and security.

## 🚀 Pre-Deployment Checklist

### Server Requirements
- [ ] WordPress 5.0 or higher
- [ ] PHP 7.4 or higher (PHP 8.0+ recommended)
- [ ] MySQL 5.6 or higher (MySQL 8.0+ recommended)
- [ ] Memory limit: 256MB minimum (512MB recommended)
- [ ] SSL certificate installed
- [ ] Backup system in place

### Security Checklist
- [ ] WordPress core updated to latest version
- [ ] All plugins and themes updated
- [ ] Strong admin passwords
- [ ] Two-factor authentication enabled
- [ ] File permissions properly set
- [ ] Database credentials secured
- [ ] SSL/HTTPS enforced

## 📦 Deployment Methods

### Method 1: Manual Upload (Recommended for Production)

1. **Prepare the Plugin**
   ```bash
   # Create deployment package
   zip -r bizmanage-pro-v1.0.0.zip bizmanage-pro/ -x "*.git*" "*.DS_Store*" "node_modules/*"
   ```

2. **Upload to Server**
   ```bash
   # Via FTP/SFTP
   scp bizmanage-pro-v1.0.0.zip user@server:/path/to/wordpress/wp-content/plugins/
   
   # Extract on server
   cd /path/to/wordpress/wp-content/plugins/
   unzip bizmanage-pro-v1.0.0.zip
   ```

3. **Set Permissions**
   ```bash
   # Set proper file permissions
   find bizmanage-pro/ -type f -exec chmod 644 {} \;
   find bizmanage-pro/ -type d -exec chmod 755 {} \;
   
   # Ensure upload directories are writable
   chmod 755 wp-content/uploads/
   mkdir -p wp-content/uploads/bizmanage-pro/
   chmod 755 wp-content/uploads/bizmanage-pro/
   ```

### Method 2: WordPress Admin Upload

1. **Create Clean Package**
   - Remove development files (.git, node_modules, etc.)
   - Ensure all files are properly formatted
   - Test locally before packaging

2. **Upload via Admin**
   - Go to `Plugins > Add New > Upload Plugin`
   - Select the ZIP file
   - Click "Install Now"
   - Activate the plugin

### Method 3: Git Deployment (Advanced)

1. **Set Up Git Hook**
   ```bash
   # On server
   git clone https://github.com/your-repo/bizmanage-pro.git
   cd bizmanage-pro
   git checkout production
   ```

2. **Automated Deployment Script**
   ```bash
   #!/bin/bash
   # deploy.sh
   cd /path/to/wordpress/wp-content/plugins/bizmanage-pro
   git pull origin production
   composer install --no-dev --optimize-autoloader
   wp plugin activate bizmanage-pro
   ```

## 🔧 Post-Deployment Configuration

### 1. Database Setup

The plugin automatically creates necessary tables on activation. Verify:

```sql
-- Check if tables exist
SHOW TABLES LIKE 'wp_bizmanage_%';

-- Verify table structure
DESCRIBE wp_bizmanage_business_entities;
DESCRIBE wp_bizmanage_transactions;
DESCRIBE wp_bizmanage_documents;
```

### 2. File System Setup

```bash
# Create required directories
mkdir -p wp-content/uploads/bizmanage-pro/{documents,exports,backups}

# Set permissions
chmod 755 wp-content/uploads/bizmanage-pro/
chmod 755 wp-content/uploads/bizmanage-pro/documents/
chmod 755 wp-content/uploads/bizmanage-pro/exports/
chmod 755 wp-content/uploads/bizmanage-pro/backups/

# Secure directories
echo "deny from all" > wp-content/uploads/bizmanage-pro/documents/.htaccess
echo "<?php // Silence is golden" > wp-content/uploads/bizmanage-pro/documents/index.php
```

### 3. WordPress Configuration

Add to `wp-config.php`:

```php
// BizManage Pro Configuration
define('BIZMANAGE_PRO_ENCRYPTION_KEY', 'your-unique-32-character-key-here');
define('BIZMANAGE_PRO_DEBUG', false);
define('BIZMANAGE_PRO_MAX_FILE_SIZE', 10485760); // 10MB

// Security enhancements
define('DISALLOW_FILE_EDIT', true);
define('FORCE_SSL_ADMIN', true);
```

### 4. Server Configuration

#### Apache (.htaccess)
```apache
# Add to .htaccess in wp-content/uploads/bizmanage-pro/documents/
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Allow only authenticated access
<FilesMatch "\.(pdf|doc|docx|xls|xlsx|jpg|jpeg|png)$">
    Order Deny,Allow
    Deny from all
    # Add your server IP for admin access
    Allow from 127.0.0.1
</FilesMatch>
```

#### Nginx
```nginx
# Add to nginx.conf
location ~* /wp-content/uploads/bizmanage-pro/documents/ {
    deny all;
    return 403;
}

# For authenticated document access
location ~* /wp-content/uploads/bizmanage-pro/documents/.*\.(pdf|doc|docx|xls|xlsx|jpg|jpeg|png)$ {
    # Handle through WordPress
    try_files $uri /index.php?$args;
}
```

## 🔒 Security Hardening

### 1. File Security

```bash
# Remove write permissions from plugin files
find bizmanage-pro/ -name "*.php" -exec chmod 644 {} \;

# Protect sensitive files
chmod 600 wp-config.php
chmod 644 .htaccess

# Hide version information
echo "<?php // BizManage Pro" > bizmanage-pro/readme.txt
```

### 2. Database Security

```sql
-- Create dedicated database user
CREATE USER 'bizmanage_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT SELECT, INSERT, UPDATE, DELETE ON wordpress.wp_bizmanage_* TO 'bizmanage_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Encryption Setup

```php
// Generate encryption key
$encryption_key = bin2hex(random_bytes(16));
echo "BIZMANAGE_PRO_ENCRYPTION_KEY: " . $encryption_key;
```

## 📊 Performance Optimization

### 1. Caching Configuration

```php
// wp-config.php
define('WP_CACHE', true);
define('BIZMANAGE_PRO_CACHE_ENABLED', true);
define('BIZMANAGE_PRO_CACHE_DURATION', 3600); // 1 hour
```

### 2. Database Optimization

```sql
-- Add indexes for better performance
ALTER TABLE wp_bizmanage_transactions ADD INDEX idx_entity_date (entity_id, transaction_date);
ALTER TABLE wp_bizmanage_documents ADD INDEX idx_entity_category (entity_id, category);
ALTER TABLE wp_bizmanage_business_entities ADD INDEX idx_user_status (user_id, status);
```

### 3. File Optimization

```bash
# Optimize images
find bizmanage-pro/assets/images/ -name "*.png" -exec optipng {} \;
find bizmanage-pro/assets/images/ -name "*.jpg" -exec jpegoptim {} \;

# Minify CSS and JS (if not already done)
# Use build tools or online minifiers
```

## 🔄 Backup Strategy

### 1. Database Backup

```bash
#!/bin/bash
# backup-db.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u username -p database_name wp_bizmanage_* > bizmanage_backup_$DATE.sql
gzip bizmanage_backup_$DATE.sql
```

### 2. File Backup

```bash
#!/bin/bash
# backup-files.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf bizmanage_files_$DATE.tar.gz wp-content/uploads/bizmanage-pro/
```

### 3. Automated Backup

```bash
# Add to crontab
0 2 * * * /path/to/backup-db.sh
0 3 * * * /path/to/backup-files.sh

# Cleanup old backups (keep 30 days)
0 4 * * * find /backup/path/ -name "bizmanage_*" -mtime +30 -delete
```

## 📈 Monitoring & Maintenance

### 1. Health Checks

```php
// Add to functions.php or custom plugin
function bizmanage_health_check() {
    $checks = array();
    
    // Check database tables
    global $wpdb;
    $tables = $wpdb->get_results("SHOW TABLES LIKE 'wp_bizmanage_%'");
    $checks['database'] = count($tables) >= 7;
    
    // Check file permissions
    $upload_dir = wp_upload_dir();
    $bizmanage_dir = $upload_dir['basedir'] . '/bizmanage-pro';
    $checks['files'] = is_writable($bizmanage_dir);
    
    // Check encryption
    $checks['encryption'] = defined('BIZMANAGE_PRO_ENCRYPTION_KEY');
    
    return $checks;
}
```

### 2. Error Logging

```php
// wp-config.php
define('WP_DEBUG', false);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('BIZMANAGE_PRO_LOG_ERRORS', true);
```

### 3. Performance Monitoring

```bash
# Monitor disk usage
du -sh wp-content/uploads/bizmanage-pro/

# Monitor database size
mysql -e "SELECT table_name, ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)' FROM information_schema.tables WHERE table_schema = 'database_name' AND table_name LIKE 'wp_bizmanage_%';"
```

## 🚨 Troubleshooting

### Common Issues

1. **Plugin Won't Activate**
   ```bash
   # Check PHP error log
   tail -f /var/log/php_errors.log
   
   # Check WordPress debug log
   tail -f wp-content/debug.log
   ```

2. **File Upload Issues**
   ```php
   // Check PHP settings
   echo "Max file size: " . ini_get('upload_max_filesize') . "\n";
   echo "Max post size: " . ini_get('post_max_size') . "\n";
   echo "Memory limit: " . ini_get('memory_limit') . "\n";
   ```

3. **Database Connection Issues**
   ```php
   // Test database connection
   $connection = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
   if ($connection->connect_error) {
       die("Connection failed: " . $connection->connect_error);
   }
   echo "Database connection successful";
   ```

### Recovery Procedures

1. **Plugin Recovery**
   ```bash
   # Deactivate via database
   mysql -e "UPDATE wp_options SET option_value = '' WHERE option_name = 'active_plugins';"
   
   # Or via wp-cli
   wp plugin deactivate bizmanage-pro
   ```

2. **Data Recovery**
   ```bash
   # Restore from backup
   mysql database_name < bizmanage_backup_YYYYMMDD_HHMMSS.sql
   tar -xzf bizmanage_files_YYYYMMDD_HHMMSS.tar.gz -C wp-content/uploads/
   ```

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] Code tested in staging environment
- [ ] Database backup created
- [ ] File backup created
- [ ] SSL certificate verified
- [ ] Server requirements met
- [ ] Security measures in place

### During Deployment
- [ ] Plugin files uploaded
- [ ] Permissions set correctly
- [ ] Plugin activated successfully
- [ ] Database tables created
- [ ] Upload directories created
- [ ] Configuration applied

### Post-Deployment
- [ ] Basic functionality tested
- [ ] User roles working correctly
- [ ] File uploads working
- [ ] Reports generating correctly
- [ ] Email notifications working
- [ ] Backup system tested
- [ ] Monitoring enabled
- [ ] Documentation updated

## 🎯 Go-Live Checklist

- [ ] All tests passed
- [ ] Performance optimized
- [ ] Security hardened
- [ ] Backups configured
- [ ] Monitoring enabled
- [ ] Team trained
- [ ] Documentation complete
- [ ] Support contacts ready

---

**Congratulations!** BizManage Pro is now ready for production use. Monitor the system closely for the first few days and be prepared to address any issues quickly.

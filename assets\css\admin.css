/**
 * BizManage Pro Admin Styles
 *
 * @package BizManagePro
 * @since 1.0.0
 */

/* General <PERSON><PERSON> */
.bizmanage-admin-wrap {
    margin: 20px 0;
}

.bizmanage-header {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.bizmanage-header h1 {
    margin: 0 0 10px 0;
    color: #23282d;
    font-size: 24px;
    font-weight: 400;
}

.bizmanage-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Entity Selector */
.bizmanage-entity-selector {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.bizmanage-entity-selector label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.bizmanage-entity-selector select {
    width: 100%;
    max-width: 300px;
}

/* Dashboard Cards */
.bizmanage-dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.bizmanage-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    transition: box-shadow 0.3s ease;
}

.bizmanage-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,.1);
}

.bizmanage-card h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #23282d;
}

.bizmanage-card .card-value {
    font-size: 28px;
    font-weight: 600;
    margin: 10px 0;
}

.bizmanage-card .card-value.positive {
    color: #28a745;
}

.bizmanage-card .card-value.negative {
    color: #dc3545;
}

.bizmanage-card .card-description {
    color: #666;
    font-size: 14px;
}

/* Tables */
.bizmanage-table-wrapper {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.bizmanage-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.bizmanage-table th,
.bizmanage-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.bizmanage-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.bizmanage-table tbody tr:hover {
    background: #f8f9fa;
}

.bizmanage-table .actions {
    white-space: nowrap;
}

.bizmanage-table .actions a {
    margin-right: 8px;
    text-decoration: none;
}

/* Forms */
.bizmanage-form {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.bizmanage-form .form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.bizmanage-form .form-group {
    margin-bottom: 20px;
}

.bizmanage-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #495057;
}

.bizmanage-form input[type="text"],
.bizmanage-form input[type="email"],
.bizmanage-form input[type="url"],
.bizmanage-form input[type="tel"],
.bizmanage-form input[type="number"],
.bizmanage-form input[type="date"],
.bizmanage-form select,
.bizmanage-form textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.bizmanage-form input:focus,
.bizmanage-form select:focus,
.bizmanage-form textarea:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 0.2rem rgba(0, 124, 186, 0.25);
}

.bizmanage-form .required {
    color: #dc3545;
}

/* Buttons */
.bizmanage-btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 4px 2px;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bizmanage-btn-primary {
    background-color: #007cba;
    color: #fff;
}

.bizmanage-btn-primary:hover {
    background-color: #005a87;
    color: #fff;
}

.bizmanage-btn-secondary {
    background-color: #6c757d;
    color: #fff;
}

.bizmanage-btn-secondary:hover {
    background-color: #545b62;
    color: #fff;
}

.bizmanage-btn-success {
    background-color: #28a745;
    color: #fff;
}

.bizmanage-btn-success:hover {
    background-color: #1e7e34;
    color: #fff;
}

.bizmanage-btn-danger {
    background-color: #dc3545;
    color: #fff;
}

.bizmanage-btn-danger:hover {
    background-color: #c82333;
    color: #fff;
}

.bizmanage-btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.bizmanage-btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

/* Alerts */
.bizmanage-alert {
    padding: 12px 16px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.bizmanage-alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.bizmanage-alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.bizmanage-alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.bizmanage-alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Loading States */
.bizmanage-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.bizmanage-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: bizmanage-spin 1s linear infinite;
}

@keyframes bizmanage-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.bizmanage-modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.bizmanage-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border-radius: 4px;
    width: 80%;
    max-width: 600px;
    position: relative;
}

.bizmanage-modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.bizmanage-modal-close:hover {
    color: #000;
}

/* Charts */
.bizmanage-chart-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.bizmanage-chart-container h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #23282d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bizmanage-dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .bizmanage-form .form-row {
        grid-template-columns: 1fr;
    }
    
    .bizmanage-table-wrapper {
        overflow-x: auto;
    }
    
    .bizmanage-modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

/* Print Styles */
@media print {
    .bizmanage-btn,
    .bizmanage-entity-selector,
    .bizmanage-modal {
        display: none !important;
    }
    
    .bizmanage-card,
    .bizmanage-table-wrapper,
    .bizmanage-form {
        box-shadow: none;
        border: 1px solid #000;
    }
}

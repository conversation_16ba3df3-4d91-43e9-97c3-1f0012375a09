<?php
/**
 * Fix Entity Selection Issue
 * 
 * This script will fix the entity selection problem in category creation
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Fix Entity Selection Issue</h1>';

// Check entities
echo '<h2>1. Business Entities Check</h2>';
global $wpdb;
$entities_table = $wpdb->prefix . 'bizmanage_business_entities';
$entities = $wpdb->get_results("SELECT * FROM $entities_table WHERE status = 'active'");

if (empty($entities)) {
    echo '<p style="color: red;">❌ No active business entities found!</p>';
    
    // Create default entity
    if (isset($_POST['create_default_entity'])) {
        $result = $wpdb->insert(
            $entities_table,
            array(
                'business_name' => 'My Business',
                'entity_type' => 'Sole Proprietorship',
                'currency' => 'USD',
                'status' => 'active',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            )
        );
        
        if ($result) {
            echo '<p style="color: green;">✅ Default business entity created with ID: ' . $wpdb->insert_id . '</p>';
            $entities = $wpdb->get_results("SELECT * FROM $entities_table WHERE status = 'active'");
        } else {
            echo '<p style="color: red;">❌ Failed to create entity: ' . $wpdb->last_error . '</p>';
        }
    } else {
        echo '<form method="post">';
        echo '<button type="submit" name="create_default_entity" value="1">Create Default Business Entity</button>';
        echo '</form>';
    }
} else {
    echo '<p style="color: green;">✅ Active business entities found:</p>';
    echo '<table border="1" style="border-collapse: collapse;">';
    echo '<tr><th>ID</th><th>Business Name</th><th>Entity Type</th><th>Status</th></tr>';
    foreach ($entities as $entity) {
        echo '<tr>';
        echo '<td>' . $entity->id . '</td>';
        echo '<td>' . $entity->business_name . '</td>';
        echo '<td>' . $entity->entity_type . '</td>';
        echo '<td>' . $entity->status . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}

// Test category creation with proper entity ID
echo '<h2>2. Test Category Creation with Entity ID</h2>';

if (isset($_POST['test_category_with_entity']) && !empty($entities)) {
    $entity_id = intval($_POST['test_entity_id']);
    $category_name = sanitize_text_field($_POST['test_category_name']);
    $category_type = sanitize_text_field($_POST['test_category_type']);
    
    // Simulate AJAX call with proper entity ID
    $_POST = array(
        'action' => 'bizmanage_save_category',
        'nonce' => wp_create_nonce('bizmanage_save_category'),
        'entity_id' => $entity_id,
        'name' => $category_name,
        'type' => $category_type,
        'description' => 'Test category with proper entity ID',
        'color' => '#007cba'
    );
    
    echo '<h3>Testing with data:</h3>';
    echo '<pre>' . json_encode($_POST, JSON_PRETTY_PRINT) . '</pre>';
    
    if (class_exists('BizManage_Pro_Categories')) {
        $categories = BizManage_Pro_Categories::instance();
        
        // Capture output
        ob_start();
        $categories->ajax_save_category();
        $output = ob_get_clean();
        
        echo '<h3>AJAX Handler Output:</h3>';
        echo '<pre>' . htmlspecialchars($output) . '</pre>';
        
        // Check if category was created
        $categories_table = $wpdb->prefix . 'bizmanage_categories';
        $created_category = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $categories_table WHERE entity_id = %d AND name = %s AND type = %s ORDER BY id DESC LIMIT 1",
            $entity_id, $category_name, $category_type
        ));
        
        if ($created_category) {
            echo '<p style="color: green;">✅ Category created successfully!</p>';
            echo '<pre>' . print_r($created_category, true) . '</pre>';
        } else {
            echo '<p style="color: red;">❌ Category not found in database</p>';
        }
    }
}

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<table>';
    echo '<tr><td>Entity:</td><td><select name="test_entity_id" required>';
    foreach ($entities as $entity) {
        echo '<option value="' . $entity->id . '">' . $entity->business_name . ' (ID: ' . $entity->id . ')</option>';
    }
    echo '</select></td></tr>';
    echo '<tr><td>Category Name:</td><td><input type="text" name="test_category_name" value="Test Category ' . time() . '" required></td></tr>';
    echo '<tr><td>Type:</td><td><select name="test_category_type" required>';
    echo '<option value="income">Income</option>';
    echo '<option value="expense">Expense</option>';
    echo '</select></td></tr>';
    echo '<tr><td colspan="2"><button type="submit" name="test_category_with_entity" value="1">Test Category Creation with Entity ID</button></td></tr>';
    echo '</table>';
    echo '</form>';
}

// JavaScript test with proper entity ID
echo '<h2>3. JavaScript Test with Entity ID</h2>';
echo '<div id="js-test-result"></div>';

if (!empty($entities)) {
    $entity_id = $entities[0]->id;
    echo '<button onclick="testCategoryWithEntity(' . $entity_id . ')">Test Category AJAX with Entity ID ' . $entity_id . '</button>';
    
    echo '<script>
    function testCategoryWithEntity(entityId) {
        var data = {
            action: "bizmanage_save_category",
            nonce: "' . wp_create_nonce('bizmanage_save_category') . '",
            entity_id: entityId,
            name: "JS Test Entity " + entityId + " - " + Date.now(),
            type: "income",
            description: "Test from JavaScript with entity ID " + entityId,
            color: "#28a745"
        };
        
        console.log("Sending AJAX data with entity ID:", data);
        
        jQuery.post(ajaxurl, data, function(response) {
            console.log("AJAX Response:", response);
            document.getElementById("js-test-result").innerHTML = 
                "<h4>Success Response:</h4><pre>" + JSON.stringify(response, null, 2) + "</pre>";
        }).fail(function(xhr, status, error) {
            console.log("AJAX Error:", error);
            console.log("Response Text:", xhr.responseText);
            document.getElementById("js-test-result").innerHTML = 
                "<h4 style=\"color: red;\">Error Response:</h4><pre>" + xhr.responseText + "</pre>";
        });
    }
    </script>';
}

// Check current categories
echo '<h2>4. Current Categories by Entity</h2>';
$categories_table = $wpdb->prefix . 'bizmanage_categories';

foreach ($entities as $entity) {
    $entity_categories = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $categories_table WHERE entity_id = %d ORDER BY type, name",
        $entity->id
    ));
    
    echo '<h3>Entity: ' . $entity->business_name . ' (ID: ' . $entity->id . ')</h3>';
    
    if (!empty($entity_categories)) {
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>ID</th><th>Name</th><th>Type</th><th>Status</th><th>Created</th></tr>';
        foreach ($entity_categories as $cat) {
            echo '<tr>';
            echo '<td>' . $cat->id . '</td>';
            echo '<td>' . $cat->name . '</td>';
            echo '<td>' . $cat->type . '</td>';
            echo '<td>' . $cat->status . '</td>';
            echo '<td>' . $cat->created_at . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<p>No categories found for this entity.</p>';
    }
}

// Instructions
echo '<h2>5. Instructions to Fix</h2>';
echo '<ol>';
echo '<li><strong>Ensure you have active business entities</strong> (check section 1 above)</li>';
echo '<li><strong>Test category creation</strong> using the form in section 2</li>';
echo '<li><strong>Test JavaScript AJAX</strong> using the button in section 3</li>';
echo '<li><strong>Go to BizManage Pro Dashboard</strong> and try creating a category</li>';
echo '<li><strong>Check browser console</strong> for JavaScript errors (F12 → Console)</li>';
echo '</ol>';

echo '<h2>6. Quick Links</h2>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">BizManage Pro Dashboard</a></p>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances') . '">Finances Page</a></p>';
echo '<p><a href="check-debug-log.php?auto_refresh=1">Monitor Debug Log</a></p>';

// Debug info
echo '<h2>7. Debug Information</h2>';
echo '<p><strong>Current User ID:</strong> ' . get_current_user_id() . '</p>';
echo '<p><strong>Total Active Entities:</strong> ' . count($entities) . '</p>';

if (!empty($entities)) {
    echo '<p><strong>Default Entity ID:</strong> ' . $entities[0]->id . '</p>';
    echo '<p><strong>Entity Selection JavaScript:</strong></p>';
    echo '<pre>$("#bizmanage-entity-select").val() // Should return: ' . $entities[0]->id . '</pre>';
}
?>

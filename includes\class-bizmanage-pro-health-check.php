<?php
/**
 * BizManage Pro Health Check Class
 *
 * Performs system health checks and diagnostics
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Health Check Class
 */
class BizManage_Pro_Health_Check {

    /**
     * Instance of this class
     * @var BizManage_Pro_Health_Check
     */
    private static $instance = null;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Get instance
     * @return BizManage_Pro_Health_Check
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->db = BizManage_Pro_Database::instance();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_ajax_bizmanage_health_check', array($this, 'ajax_health_check'));
        add_action('admin_init', array($this, 'maybe_run_health_check'));
    }

    /**
     * Maybe run health check on admin init
     */
    public function maybe_run_health_check() {
        if (isset($_GET['bizmanage_health_check']) && current_user_can('manage_options')) {
            $this->display_health_check();
        }
    }

    /**
     * Run comprehensive health check
     * @return array
     */
    public function run_health_check() {
        $checks = array();

        // Database checks
        $checks['database'] = $this->check_database();
        
        // File system checks
        $checks['filesystem'] = $this->check_filesystem();
        
        // Plugin checks
        $checks['plugin'] = $this->check_plugin_integrity();
        
        // Security checks
        $checks['security'] = $this->check_security();
        
        // Performance checks
        $checks['performance'] = $this->check_performance();

        return $checks;
    }

    /**
     * Check database integrity
     * @return array
     */
    private function check_database() {
        $checks = array();
        
        // Check if all tables exist
        $required_tables = array(
            'business_entities',
            'documents',
            'transactions',
            'recurring_transactions',
            'bank_accounts',
            'categories',
            'settings'
        );

        foreach ($required_tables as $table) {
            $table_name = $this->db->get_table($table);
            $exists = $this->db->wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
            
            $checks['tables'][$table] = array(
                'status' => $exists ? 'pass' : 'fail',
                'message' => $exists ? 'Table exists' : 'Table missing',
                'table_name' => $table_name
            );
        }

        // Check table structure
        foreach ($required_tables as $table) {
            if ($checks['tables'][$table]['status'] === 'pass') {
                $table_name = $this->db->get_table($table);
                $columns = $this->db->wpdb->get_results("DESCRIBE $table_name");
                $checks['structure'][$table] = array(
                    'status' => count($columns) > 0 ? 'pass' : 'fail',
                    'columns' => count($columns),
                    'message' => count($columns) > 0 ? 'Table structure OK' : 'Table structure invalid'
                );
            }
        }

        // Check database connection
        $checks['connection'] = array(
            'status' => $this->db->wpdb->last_error ? 'fail' : 'pass',
            'message' => $this->db->wpdb->last_error ? $this->db->wpdb->last_error : 'Database connection OK'
        );

        return $checks;
    }

    /**
     * Check filesystem
     * @return array
     */
    private function check_filesystem() {
        $checks = array();
        
        // Check upload directory
        $upload_dir = wp_upload_dir();
        $bizmanage_dir = $upload_dir['basedir'] . '/bizmanage-pro';
        
        $checks['upload_dir'] = array(
            'status' => is_dir($bizmanage_dir) && is_writable($bizmanage_dir) ? 'pass' : 'fail',
            'message' => is_dir($bizmanage_dir) && is_writable($bizmanage_dir) ? 'Upload directory OK' : 'Upload directory not writable',
            'path' => $bizmanage_dir
        );

        // Check document directory
        $doc_dir = $bizmanage_dir . '/documents';
        $checks['document_dir'] = array(
            'status' => is_dir($doc_dir) && is_writable($doc_dir) ? 'pass' : 'fail',
            'message' => is_dir($doc_dir) && is_writable($doc_dir) ? 'Document directory OK' : 'Document directory not writable',
            'path' => $doc_dir
        );

        // Check security files
        $htaccess_file = $doc_dir . '/.htaccess';
        $checks['security_files'] = array(
            'status' => file_exists($htaccess_file) ? 'pass' : 'warning',
            'message' => file_exists($htaccess_file) ? 'Security files present' : 'Security files missing',
            'path' => $htaccess_file
        );

        return $checks;
    }

    /**
     * Check plugin integrity
     * @return array
     */
    private function check_plugin_integrity() {
        $checks = array();
        
        // Check required files
        $required_files = array(
            'bizmanage-pro.php',
            'includes/class-bizmanage-pro-database.php',
            'includes/class-bizmanage-pro-security.php',
            'includes/class-bizmanage-pro-ajax.php',
            'includes/class-bizmanage-pro-categories.php',
            'includes/class-bizmanage-pro-shortcodes.php',
            'admin/class-bizmanage-pro-admin.php',
            'assets/css/admin.css',
            'assets/css/frontend.css',
            'assets/js/admin.js'
        );

        foreach ($required_files as $file) {
            $file_path = BIZMANAGE_PRO_PLUGIN_DIR . $file;
            $exists = file_exists($file_path);
            
            $checks['files'][$file] = array(
                'status' => $exists ? 'pass' : 'fail',
                'message' => $exists ? 'File exists' : 'File missing',
                'path' => $file_path
            );
        }

        // Check class loading
        $required_classes = array(
            'BizManage_Pro_Database',
            'BizManage_Pro_Security',
            'BizManage_Pro_Ajax',
            'BizManage_Pro_Categories',
            'BizManage_Pro_Shortcodes',
            'BizManage_Pro_Admin'
        );

        foreach ($required_classes as $class) {
            $checks['classes'][$class] = array(
                'status' => class_exists($class) ? 'pass' : 'fail',
                'message' => class_exists($class) ? 'Class loaded' : 'Class not loaded'
            );
        }

        return $checks;
    }

    /**
     * Check security
     * @return array
     */
    private function check_security() {
        $checks = array();
        
        // Check encryption key
        $checks['encryption'] = array(
            'status' => defined('BIZMANAGE_PRO_ENCRYPTION_KEY') ? 'pass' : 'warning',
            'message' => defined('BIZMANAGE_PRO_ENCRYPTION_KEY') ? 'Encryption key defined' : 'Encryption key not defined'
        );

        // Check user capabilities
        $admin_role = get_role('administrator');
        $has_capabilities = $admin_role && $admin_role->has_cap('bizmanage_manage_entities');
        
        $checks['capabilities'] = array(
            'status' => $has_capabilities ? 'pass' : 'fail',
            'message' => $has_capabilities ? 'User capabilities OK' : 'User capabilities missing'
        );

        // Check nonce system
        $security = BizManage_Pro_Security::instance();
        $test_nonce = $security->create_nonce('test');
        
        $checks['nonce_system'] = array(
            'status' => !empty($test_nonce) ? 'pass' : 'fail',
            'message' => !empty($test_nonce) ? 'Nonce system working' : 'Nonce system failed'
        );

        return $checks;
    }

    /**
     * Check performance
     * @return array
     */
    private function check_performance() {
        $checks = array();
        
        // Check PHP version
        $php_version = PHP_VERSION;
        $checks['php_version'] = array(
            'status' => version_compare($php_version, '7.4', '>=') ? 'pass' : 'warning',
            'message' => 'PHP ' . $php_version,
            'recommendation' => version_compare($php_version, '7.4', '<') ? 'Upgrade to PHP 7.4 or higher' : null
        );

        // Check memory limit
        $memory_limit = ini_get('memory_limit');
        $memory_bytes = $this->convert_to_bytes($memory_limit);
        
        $checks['memory_limit'] = array(
            'status' => $memory_bytes >= 268435456 ? 'pass' : 'warning', // 256MB
            'message' => 'Memory limit: ' . $memory_limit,
            'recommendation' => $memory_bytes < 268435456 ? 'Increase memory limit to 256MB or higher' : null
        );

        // Check max execution time
        $max_execution_time = ini_get('max_execution_time');
        $checks['execution_time'] = array(
            'status' => $max_execution_time >= 30 || $max_execution_time == 0 ? 'pass' : 'warning',
            'message' => 'Max execution time: ' . ($max_execution_time == 0 ? 'unlimited' : $max_execution_time . 's'),
            'recommendation' => $max_execution_time < 30 && $max_execution_time != 0 ? 'Increase max execution time to 30s or higher' : null
        );

        return $checks;
    }

    /**
     * Convert memory limit to bytes
     * @param string $val
     * @return int
     */
    private function convert_to_bytes($val) {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int) $val;
        
        switch($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }
        
        return $val;
    }

    /**
     * Display health check results
     */
    public function display_health_check() {
        $results = $this->run_health_check();
        
        echo '<div class="wrap">';
        echo '<h1>BizManage Pro Health Check</h1>';
        
        foreach ($results as $category => $checks) {
            echo '<h2>' . ucfirst($category) . '</h2>';
            echo '<table class="widefat">';
            
            $this->display_check_results($checks);
            
            echo '</table>';
        }
        
        echo '</div>';
        exit;
    }

    /**
     * Display check results recursively
     * @param array $checks
     */
    private function display_check_results($checks) {
        foreach ($checks as $key => $check) {
            if (is_array($check) && isset($check['status'])) {
                $status_color = $check['status'] === 'pass' ? 'green' : ($check['status'] === 'warning' ? 'orange' : 'red');
                echo '<tr>';
                echo '<td><strong>' . ucfirst(str_replace('_', ' ', $key)) . '</strong></td>';
                echo '<td style="color: ' . $status_color . ';">' . strtoupper($check['status']) . '</td>';
                echo '<td>' . $check['message'] . '</td>';
                echo '</tr>';
            } elseif (is_array($check)) {
                echo '<tr><td colspan="3"><strong>' . ucfirst(str_replace('_', ' ', $key)) . '</strong></td></tr>';
                $this->display_check_results($check);
            }
        }
    }

    /**
     * AJAX health check
     */
    public function ajax_health_check() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        $results = $this->run_health_check();
        wp_send_json_success($results);
    }
}

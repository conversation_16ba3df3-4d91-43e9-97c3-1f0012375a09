<?php
/**
 * BizManage Pro Finances Template
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get instances
$finances_manager = BizManage_Pro_Finances::instance();
$admin = BizManage_Pro_Admin::instance();
$utilities = BizManage_Pro_Utilities::instance();

// Handle actions
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
$transaction_id = isset($_GET['transaction_id']) ? intval($_GET['transaction_id']) : 0;

// Get current entity
$entities = $admin->get_user_entities_dropdown();
$selected_entity_id = isset($_GET['entity_id']) ? intval($_GET['entity_id']) : 0;

if (!$selected_entity_id && !empty($entities)) {
    $selected_entity_id = array_keys($entities)[0];
}

// Get transaction types and categories
$transaction_types = $utilities->get_transaction_types();
$payment_methods = $utilities->get_payment_methods();
?>

<div class="wrap bizmanage-admin-wrap">
    <div class="bizmanage-header">
        <h1>
            <?php 
            switch ($action) {
                case 'add-income':
                    _e('Add Income', 'bizmanage-pro');
                    break;
                case 'add-expense':
                    _e('Add Expense', 'bizmanage-pro');
                    break;
                case 'edit':
                    _e('Edit Transaction', 'bizmanage-pro');
                    break;
                default:
                    _e('Financial Management', 'bizmanage-pro');
            }
            ?>
        </h1>
        <p>
            <?php 
            switch ($action) {
                case 'add-income':
                    _e('Record income transactions for your business.', 'bizmanage-pro');
                    break;
                case 'add-expense':
                    _e('Record expense transactions for your business.', 'bizmanage-pro');
                    break;
                case 'edit':
                    _e('Update transaction details and information.', 'bizmanage-pro');
                    break;
                default:
                    _e('Track and manage your business income and expenses.', 'bizmanage-pro');
            }
            ?>
        </p>
    </div>

    <?php if (empty($entities)): ?>
        <div class="notice notice-warning">
            <p>
                <?php _e('No business entities found. Please create a business entity first.', 'bizmanage-pro'); ?>
                <a href="<?php echo admin_url('admin.php?page=bizmanage-entities'); ?>" class="button button-primary">
                    <?php _e('Create Business Entity', 'bizmanage-pro'); ?>
                </a>
            </p>
        </div>
    <?php else: ?>

        <?php if ($action === 'list'): ?>
            
            <!-- List View -->
            <div class="bizmanage-entity-selector">
                <label for="bizmanage-entity-select"><?php _e('Select Business Entity:', 'bizmanage-pro'); ?></label>
                <select id="bizmanage-entity-select" name="entity_id" class="form-select">
                    <?php foreach ($entities as $id => $name): ?>
                        <option value="<?php echo esc_attr($id); ?>" <?php selected($selected_entity_id, $id); ?>>
                            <?php echo esc_html($name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Financial Summary -->
            <div class="bizmanage-dashboard-cards">
                <div class="bizmanage-card">
                    <h3><?php _e('Total Income', 'bizmanage-pro'); ?></h3>
                    <div class="card-value positive" id="total-income">$0.00</div>
                    <div class="card-description"><?php _e('Current month income', 'bizmanage-pro'); ?></div>
                </div>
                
                <div class="bizmanage-card">
                    <h3><?php _e('Total Expenses', 'bizmanage-pro'); ?></h3>
                    <div class="card-value negative" id="total-expenses">$0.00</div>
                    <div class="card-description"><?php _e('Current month expenses', 'bizmanage-pro'); ?></div>
                </div>
                
                <div class="bizmanage-card">
                    <h3><?php _e('Net Profit', 'bizmanage-pro'); ?></h3>
                    <div class="card-value" id="net-profit">$0.00</div>
                    <div class="card-description"><?php _e('Income minus expenses', 'bizmanage-pro'); ?></div>
                </div>
                
                <div class="bizmanage-card">
                    <h3><?php _e('Transactions', 'bizmanage-pro'); ?></h3>
                    <div class="card-value" id="transaction-count">0</div>
                    <div class="card-description"><?php _e('Total transactions', 'bizmanage-pro'); ?></div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div style="margin-bottom: 20px;">
                <?php if (current_user_can('bizmanage_manage_finances')): ?>
                <a href="<?php echo admin_url('admin.php?page=bizmanage-finances&action=add-income&entity_id=' . $selected_entity_id); ?>" 
                   class="bizmanage-btn bizmanage-btn-success">
                    <?php _e('Add Income', 'bizmanage-pro'); ?>
                </a>
                
                <a href="<?php echo admin_url('admin.php?page=bizmanage-finances&action=add-expense&entity_id=' . $selected_entity_id); ?>" 
                   class="bizmanage-btn bizmanage-btn-danger">
                    <?php _e('Add Expense', 'bizmanage-pro'); ?>
                </a>
                <?php endif; ?>
            </div>

            <!-- Filters -->
            <div class="bizmanage-transactions-filters" style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
                <div style="display: grid; grid-template-columns: auto auto auto auto auto; gap: 15px; align-items: end;">
                    <div>
                        <label for="transaction-type-filter"><?php _e('Type:', 'bizmanage-pro'); ?></label>
                        <select id="transaction-type-filter" class="form-select">
                            <option value=""><?php _e('All Types', 'bizmanage-pro'); ?></option>
                            <option value="income"><?php _e('Income', 'bizmanage-pro'); ?></option>
                            <option value="expense"><?php _e('Expense', 'bizmanage-pro'); ?></option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="start-date-filter"><?php _e('Start Date:', 'bizmanage-pro'); ?></label>
                        <input type="date" id="start-date-filter" class="form-control">
                    </div>
                    
                    <div>
                        <label for="end-date-filter"><?php _e('End Date:', 'bizmanage-pro'); ?></label>
                        <input type="date" id="end-date-filter" class="form-control">
                    </div>
                    
                    <button type="button" id="filter-transactions" class="bizmanage-btn bizmanage-btn-secondary">
                        <?php _e('Filter', 'bizmanage-pro'); ?>
                    </button>
                    
                    <button type="button" id="clear-filters" class="bizmanage-btn bizmanage-btn-secondary">
                        <?php _e('Clear', 'bizmanage-pro'); ?>
                    </button>
                </div>
            </div>

            <!-- Transactions List -->
            <div class="bizmanage-table-wrapper">
                <table class="bizmanage-table" id="transactions-table">
                    <thead>
                        <tr>
                            <th><?php _e('Date', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Description', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Category', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Type', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Amount', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Status', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Actions', 'bizmanage-pro'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="transactions-list">
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 20px;">
                                <?php _e('Loading transactions...', 'bizmanage-pro'); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

        <?php elseif (in_array($action, array('add-income', 'add-expense', 'edit'))): ?>
            
            <?php
            $transaction_data = null;
            $transaction_type = '';
            
            if ($action === 'edit' && $transaction_id > 0) {
                $transaction_data = $finances_manager->get_transaction($transaction_id);
                $transaction_type = $transaction_data ? $transaction_data->transaction_type : '';
            } else {
                $transaction_type = $action === 'add-income' ? 'income' : 'expense';
            }
            ?>

            <!-- Transaction Form -->
            <div class="bizmanage-form">
                <form id="transaction-form" method="post">
                    <?php wp_nonce_field('bizmanage_save_transaction', 'transaction_nonce'); ?>
                    <input type="hidden" name="transaction_id" value="<?php echo esc_attr($transaction_id); ?>">
                    <input type="hidden" name="entity_id" value="<?php echo esc_attr($selected_entity_id); ?>">
                    <input type="hidden" name="transaction_type" value="<?php echo esc_attr($transaction_type); ?>">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="amount"><?php _e('Amount', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                            <input type="number" step="0.01" id="amount" name="amount" 
                                   value="<?php echo esc_attr($transaction_data->amount ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="currency"><?php _e('Currency', 'bizmanage-pro'); ?></label>
                            <select id="currency" name="currency">
                                <?php 
                                $currencies = $utilities->get_currencies();
                                foreach ($currencies as $code => $currency_data): ?>
                                    <option value="<?php echo esc_attr($code); ?>" 
                                            <?php selected($transaction_data->currency ?? 'USD', $code); ?>>
                                        <?php echo esc_html($code . ' - ' . $currency_data['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description"><?php _e('Description', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                        <textarea id="description" name="description" rows="3" required><?php echo esc_textarea($transaction_data->description ?? ''); ?></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="category"><?php _e('Category', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                            <div class="input-group">
                                <select id="category" name="category" required>
                                    <option value=""><?php _e('Select Category', 'bizmanage-pro'); ?></option>
                                    <!-- Categories will be loaded via AJAX based on transaction type -->
                                </select>
                                <button type="button" class="bizmanage-btn bizmanage-btn-secondary bizmanage-btn-sm" id="add-category-btn">
                                    <?php _e('Add New', 'bizmanage-pro'); ?>
                                </button>
                                <button type="button" class="bizmanage-btn bizmanage-btn-secondary bizmanage-btn-sm" id="manage-categories-btn">
                                    <?php _e('Manage', 'bizmanage-pro'); ?>
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="subcategory"><?php _e('Subcategory', 'bizmanage-pro'); ?></label>
                            <input type="text" id="subcategory" name="subcategory" 
                                   value="<?php echo esc_attr($transaction_data->subcategory ?? ''); ?>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="transaction_date"><?php _e('Transaction Date', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                            <input type="date" id="transaction_date" name="transaction_date" 
                                   value="<?php echo esc_attr($transaction_data->transaction_date ?? current_time('Y-m-d')); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="payment_method"><?php _e('Payment Method', 'bizmanage-pro'); ?></label>
                            <select id="payment_method" name="payment_method">
                                <option value=""><?php _e('Select Payment Method', 'bizmanage-pro'); ?></option>
                                <?php foreach ($payment_methods as $key => $label): ?>
                                    <option value="<?php echo esc_attr($key); ?>" 
                                            <?php selected($transaction_data->payment_method ?? '', $key); ?>>
                                        <?php echo esc_html($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="reference_number"><?php _e('Reference Number', 'bizmanage-pro'); ?></label>
                            <input type="text" id="reference_number" name="reference_number" 
                                   value="<?php echo esc_attr($transaction_data->reference_number ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="tax_rate"><?php _e('Tax Rate (%)', 'bizmanage-pro'); ?></label>
                            <input type="number" step="0.01" id="tax_rate" name="tax_rate" 
                                   value="<?php echo esc_attr($transaction_data->tax_rate ?? '0.00'); ?>">
                        </div>
                    </div>

                    <div class="form-actions" style="margin-top: 30px;">
                        <button type="button" class="bizmanage-btn bizmanage-btn-primary bizmanage-save-transaction">
                            <?php echo $action === 'edit' ? __('Update Transaction', 'bizmanage-pro') : __('Save Transaction', 'bizmanage-pro'); ?>
                        </button>
                        
                        <a href="<?php echo admin_url('admin.php?page=bizmanage-finances&entity_id=' . $selected_entity_id); ?>" 
                           class="bizmanage-btn bizmanage-btn-secondary">
                            <?php _e('Cancel', 'bizmanage-pro'); ?>
                        </a>

                        <?php if ($action === 'edit' && current_user_can('bizmanage_manage_finances')): ?>
                        <button type="button" 
                                class="bizmanage-btn bizmanage-btn-danger bizmanage-delete-transaction" 
                                data-transaction-id="<?php echo esc_attr($transaction_id); ?>"
                                style="margin-left: 10px;">
                            <?php _e('Delete Transaction', 'bizmanage-pro'); ?>
                        </button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>

        <?php endif; ?>

    <?php endif; ?>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Load financial summary and transactions on page load
    if ($('#transactions-list').length) {
        loadFinancialSummary();
        loadTransactions();
    }
    
    // Load categories based on transaction type
    if ($('#category').length) {
        loadCategories('<?php echo esc_js($transaction_type); ?>');
    }

    // Category management handlers
    $('#add-category-btn').on('click', function() {
        showCategoryModal();
    });

    $('#manage-categories-btn').on('click', function() {
        showCategoryManagementModal();
    });

    // Transaction type change handler
    $('#transaction_type').on('change', function() {
        var type = $(this).val();
        if (type) {
            loadCategories(type);
        }
    });
    
    // Entity change handler
    $('#bizmanage-entity-select').on('change', function() {
        var entityId = $(this).val();

        // Reload categories for the new entity
        var transactionType = $('#transaction_type').val();
        if (transactionType) {
            loadCategories(transactionType);
        }

        // Update URL and reload page for full data refresh
        var currentUrl = new URL(window.location);
        currentUrl.searchParams.set('entity_id', entityId);
        window.location.href = currentUrl.toString();
    });
    
    // Filter transactions
    $('#filter-transactions').on('click', function() {
        loadTransactions();
    });
    
    // Clear filters
    $('#clear-filters').on('click', function() {
        $('#transaction-type-filter').val('');
        $('#start-date-filter').val('');
        $('#end-date-filter').val('');
        loadTransactions();
    });
    
    function loadFinancialSummary() {
        var entityId = $('#bizmanage-entity-select').val();
        
        BizManagePro.ajaxRequest('bizmanage_get_financial_summary', {
            entity_id: entityId
        }, function(response) {
            if (response.success && response.data) {
                updateFinancialSummary(response.data);
            }
        });
    }
    
    function updateFinancialSummary(data) {
        $('#total-income').text(BizManagePro.formatCurrency(data.income_total));
        $('#total-expenses').text(BizManagePro.formatCurrency(data.expense_total));
        $('#net-profit').text(BizManagePro.formatCurrency(data.net_profit));
        $('#transaction-count').text(data.transaction_count);
        
        // Update profit card color
        var profitCard = $('#net-profit').closest('.bizmanage-card');
        profitCard.removeClass('positive negative');
        profitCard.addClass(data.net_profit >= 0 ? 'positive' : 'negative');
    }
    
    function loadTransactions() {
        var entityId = $('#bizmanage-entity-select').val();
        var transactionType = $('#transaction-type-filter').val();
        var startDate = $('#start-date-filter').val();
        var endDate = $('#end-date-filter').val();
        
        BizManagePro.ajaxRequest('bizmanage_get_transactions', {
            entity_id: entityId,
            transaction_type: transactionType,
            start_date: startDate,
            end_date: endDate
        }, function(response) {
            if (response.success && response.data) {
                updateTransactionsList(response.data);
            }
        });
    }
    
    function updateTransactionsList(transactions) {
        var tbody = $('#transactions-list');
        tbody.empty();
        
        if (transactions.length === 0) {
            tbody.append('<tr><td colspan="7" style="text-align: center; padding: 20px;"><?php _e("No transactions found.", "bizmanage-pro"); ?></td></tr>');
            return;
        }
        
        $.each(transactions, function(index, transaction) {
            var typeClass = transaction.transaction_type === 'income' ? 'positive' : 'negative';
            var statusBadge = '<span class="status-badge status-' + transaction.status + '">' + transaction.status + '</span>';
            
            var row = '<tr>' +
                '<td>' + transaction.transaction_date + '</td>' +
                '<td>' + transaction.description + '</td>' +
                '<td>' + transaction.category + '</td>' +
                '<td><span class="' + typeClass + '">' + transaction.transaction_type + '</span></td>' +
                '<td class="' + typeClass + '">' + BizManagePro.formatCurrency(transaction.amount) + '</td>' +
                '<td>' + statusBadge + '</td>' +
                '<td class="actions">' +
                '<a href="<?php echo admin_url("admin.php?page=bizmanage-finances&action=edit&transaction_id="); ?>' + transaction.id + '" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-secondary"><?php _e("Edit", "bizmanage-pro"); ?></a> ' +
                '<button type="button" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-danger bizmanage-delete-transaction" data-transaction-id="' + transaction.id + '"><?php _e("Delete", "bizmanage-pro"); ?></button>' +
                '</td>' +
                '</tr>';
            tbody.append(row);
        });
    }
    
    function loadCategories(transactionType) {
        var entityId = $('#bizmanage-entity-select').val() || 0;

        // If entityId is still 0, try alternative selectors
        if (entityId == 0) {
            entityId = $('select[name="entity_id"]').val() || <?php echo $selected_entity_id; ?>;
        }

        // Debug logging
        console.log('loadCategories called - transactionType:', transactionType, 'entityId:', entityId);

        BizManagePro.ajaxRequest('bizmanage_get_categories', {
            type: transactionType,
            entity_id: entityId
        }, function(response) {
            console.log('loadCategories response:', response);

            if (response.success && response.data) {
                var categorySelect = $('#category');
                categorySelect.find('option:not(:first)').remove();

                $.each(response.data, function(index, category) {
                    categorySelect.append('<option value="' + category.id + '">' + category.name + '</option>');
                });

                console.log('Categories loaded:', response.data.length);
            } else {
                console.log('Failed to load categories. Response:', response);
                console.log('Response success:', response.success);
                console.log('Response data:', response.data);

                // If no categories found, show message
                if (response.success && (!response.data || response.data.length === 0)) {
                    console.log('No categories found for entity:', entityId, 'type:', transactionType);
                }
            }
        }).fail(function(xhr, status, error) {
            console.log('loadCategories AJAX error:', error);
            console.log('XHR response:', xhr.responseText);
        });
    }

    function showCategoryModal(categoryData, forceTransactionType) {
        var isEdit = categoryData ? true : false;
        var modalTitle = isEdit ? '<?php _e("Edit Category", "bizmanage-pro"); ?>' : '<?php _e("Add New Category", "bizmanage-pro"); ?>';
        var transactionType = forceTransactionType || $('#transaction_type').val() || 'income';
        var entityId = $('#bizmanage-entity-select').val() || 0;

        // Debug logging
        console.log('showCategoryModal - entityId:', entityId, 'transactionType:', transactionType);

        // If entityId is still 0, try to get it from hidden input or URL
        if (entityId == 0) {
            entityId = $('input[name="entity_id"]').val() || <?php echo $selected_entity_id; ?>;
            console.log('showCategoryModal - fallback entityId:', entityId);
        }

        var modalHtml = '<div id="category-modal" class="bizmanage-modal">' +
            '<div class="bizmanage-modal-content">' +
            '<div class="bizmanage-modal-header">' +
            '<h3>' + modalTitle + '</h3>' +
            '<span class="bizmanage-modal-close">&times;</span>' +
            '</div>' +
            '<div class="bizmanage-modal-body">' +
            '<form id="category-form">' +
            '<input type="hidden" name="category_id" value="' + (categoryData ? categoryData.id : '') + '">' +
            '<input type="hidden" name="entity_id" value="' + entityId + '">' +
            '<input type="hidden" name="type" value="' + transactionType + '">' +
            '<div class="form-group">' +
            '<label for="category_name"><?php _e("Category Name", "bizmanage-pro"); ?> <span class="required">*</span></label>' +
            '<input type="text" id="category_name" name="name" required value="' + (categoryData ? categoryData.name : '') + '">' +
            '</div>' +
            '<div class="form-group">' +
            '<label for="category_description"><?php _e("Description", "bizmanage-pro"); ?></label>' +
            '<textarea id="category_description" name="description" rows="3">' + (categoryData ? categoryData.description : '') + '</textarea>' +
            '</div>' +
            '<div class="form-group">' +
            '<label for="category_color"><?php _e("Color", "bizmanage-pro"); ?></label>' +
            '<input type="color" id="category_color" name="color" value="' + (categoryData ? categoryData.color : '#007cba') + '">' +
            '</div>' +
            '</form>' +
            '</div>' +
            '<div class="bizmanage-modal-footer">' +
            '<button type="button" class="bizmanage-btn bizmanage-btn-primary" id="save-category-btn">' +
            (isEdit ? '<?php _e("Update Category", "bizmanage-pro"); ?>' : '<?php _e("Create Category", "bizmanage-pro"); ?>') +
            '</button>' +
            '<button type="button" class="bizmanage-btn bizmanage-btn-secondary bizmanage-modal-close"><?php _e("Cancel", "bizmanage-pro"); ?></button>' +
            '</div>' +
            '</div>' +
            '</div>';

        $('body').append(modalHtml);
        $('#category-modal').show();

        // Modal close handlers
        $('.bizmanage-modal-close').on('click', function() {
            $('#category-modal').remove();
        });

        // Save category handler
        $('#save-category-btn').on('click', function() {
            saveCategory();
        });
    }

    function saveCategory() {
        // Get form data as object instead of serialized string
        var formData = {};
        $('#category-form').serializeArray().forEach(function(item) {
            formData[item.name] = item.value;
        });

        // Add nonce and action
        formData.action = 'bizmanage_save_category';
        formData.nonce = '<?php echo wp_create_nonce('bizmanage_save_category'); ?>';

        // Debug logging
        console.log('Category form data:', formData);

        BizManagePro.ajaxRequest('bizmanage_save_category', formData, function(response) {
            console.log('Category save response:', response);
            if (response.success) {
                BizManagePro.showAlert(response.data.message || response.message, 'success');
                $('#category-modal').remove();
                // Reload categories
                var transactionType = $('#transaction_type').val() || 'income';
                loadCategories(transactionType);
            } else {
                BizManagePro.showAlert(response.data || response.message, 'error');
            }
        });
    }

    function showCategoryManagementModal() {
        var transactionType = $('#transaction_type').val() || 'income';
        var entityId = $('#bizmanage-entity-select').val() || 0;

        // Debug logging
        console.log('showCategoryManagementModal - entityId:', entityId, 'transactionType:', transactionType);

        // If entityId is still 0, try to get it from hidden input or URL
        if (entityId == 0) {
            entityId = $('input[name="entity_id"]').val() || <?php echo $selected_entity_id; ?>;
            console.log('showCategoryManagementModal - fallback entityId:', entityId);
        }

        var modalHtml = '<div id="category-management-modal" class="bizmanage-modal bizmanage-modal-large">' +
            '<div class="bizmanage-modal-content">' +
            '<div class="bizmanage-modal-header">' +
            '<h3><?php _e("Manage Categories", "bizmanage-pro"); ?></h3>' +
            '<span class="bizmanage-modal-close">&times;</span>' +
            '</div>' +
            '<div class="bizmanage-modal-body">' +
            '<div class="category-type-tabs">' +
            '<button type="button" class="tab-btn active" data-type="income"><?php _e("Income Categories", "bizmanage-pro"); ?></button>' +
            '<button type="button" class="tab-btn" data-type="expense"><?php _e("Expense Categories", "bizmanage-pro"); ?></button>' +
            '</div>' +
            '<div id="categories-list">' +
            '<div class="loading"><?php _e("Loading categories...", "bizmanage-pro"); ?></div>' +
            '</div>' +
            '</div>' +
            '<div class="bizmanage-modal-footer">' +
            '<button type="button" class="bizmanage-btn bizmanage-btn-secondary bizmanage-modal-close"><?php _e("Close", "bizmanage-pro"); ?></button>' +
            '</div>' +
            '</div>' +
            '</div>';

        $('body').append(modalHtml);
        $('#category-management-modal').show();

        // Load categories for management
        loadCategoriesForManagement('income');

        // Tab handlers
        $('.tab-btn').on('click', function() {
            $('.tab-btn').removeClass('active');
            $(this).addClass('active');
            var type = $(this).data('type');
            loadCategoriesForManagement(type);
        });

        // Modal close handlers
        $('.bizmanage-modal-close').on('click', function() {
            $('#category-management-modal').remove();
        });
    }

    function loadCategoriesForManagement(type) {
        var entityId = $('#bizmanage-entity-select').val() || 0;

        BizManagePro.ajaxRequest('bizmanage_get_categories', {
            type: type,
            entity_id: entityId
        }, function(response) {
            if (response.success && response.data) {
                var html = '<table class="bizmanage-table">' +
                    '<thead>' +
                    '<tr>' +
                    '<th><?php _e("Name", "bizmanage-pro"); ?></th>' +
                    '<th><?php _e("Description", "bizmanage-pro"); ?></th>' +
                    '<th><?php _e("Color", "bizmanage-pro"); ?></th>' +
                    '<th><?php _e("Actions", "bizmanage-pro"); ?></th>' +
                    '</tr>' +
                    '</thead>' +
                    '<tbody>';

                $.each(response.data, function(index, category) {
                    html += '<tr>' +
                        '<td><strong>' + category.name + '</strong></td>' +
                        '<td>' + (category.description || '') + '</td>' +
                        '<td><span class="color-indicator" style="background-color: ' + category.color + '"></span> ' + category.color + '</td>' +
                        '<td>' +
                        '<button type="button" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-secondary edit-category-btn" data-category=\'' + JSON.stringify(category) + '\'><?php _e("Edit", "bizmanage-pro"); ?></button> ' +
                        '<button type="button" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-danger delete-category-btn" data-category-id="' + category.id + '"><?php _e("Delete", "bizmanage-pro"); ?></button>' +
                        '</td>' +
                        '</tr>';
                });

                html += '</tbody></table>';
                $('#categories-list').html(html);

                // Edit category handlers
                $('.edit-category-btn').on('click', function() {
                    var categoryData = JSON.parse($(this).attr('data-category'));
                    $('#category-management-modal').remove();
                    showCategoryModal(categoryData);
                });

                // Delete category handlers
                $('.delete-category-btn').on('click', function() {
                    var categoryId = $(this).data('category-id');
                    if (confirm('<?php _e("Are you sure you want to delete this category?", "bizmanage-pro"); ?>')) {
                        deleteCategory(categoryId);
                    }
                });
            } else {
                $('#categories-list').html('<p><?php _e("No categories found.", "bizmanage-pro"); ?></p>');
            }
        });
    }

    function deleteCategory(categoryId) {
        BizManagePro.ajaxRequest('bizmanage_delete_category', {
            category_id: categoryId
        }, function(response) {
            if (response.success) {
                BizManagePro.showAlert(response.data || response.message, 'success');
                // Reload the current tab
                var activeType = $('.tab-btn.active').data('type');
                loadCategoriesForManagement(activeType);
                // Reload categories in the main form
                var transactionType = $('#transaction_type').val() || 'income';
                loadCategories(transactionType);
            } else {
                BizManagePro.showAlert(response.data || response.message, 'error');
            }
        });
    }

    // Transaction save functionality
    function saveTransaction() {
        var formData = {};
        $('#transaction-form').serializeArray().forEach(function(item) {
            formData[item.name] = item.value;
        });

        // Map form fields to expected API fields
        var transactionData = {
            action: 'bizmanage_save_transaction',
            transaction_id: formData.transaction_id || 0,
            entity_id: formData.entity_id,
            transaction_type: formData.transaction_type || formData.type,
            amount: formData.amount,
            category_id: formData.category || formData.category_id,
            description: formData.description,
            transaction_date: formData.transaction_date || formData.date,
            reference_number: formData.reference_number || '',
            notes: formData.notes || ''
        };

        console.log('Original form data:', formData);
        console.log('Mapped transaction data:', transactionData);

        // Validate required fields
        if (!transactionData.amount || parseFloat(transactionData.amount) <= 0) {
            BizManagePro.showAlert('Please enter a valid amount.', 'error');
            return;
        }

        if (!transactionData.category_id) {
            BizManagePro.showAlert('Please select a category.', 'error');
            return;
        }

        if (!transactionData.description || transactionData.description.trim() === '') {
            BizManagePro.showAlert('Please enter a description.', 'error');
            return;
        }

        if (!transactionData.entity_id || transactionData.entity_id <= 0) {
            BizManagePro.showAlert('Invalid entity ID.', 'error');
            return;
        }

        BizManagePro.ajaxRequest('bizmanage_save_transaction', transactionData, function(response) {
            console.log('Transaction save response:', response);

            if (response.success) {
                var message = response.data && response.data.message ? response.data.message :
                             response.message ? response.message : 'Transaction saved successfully.';
                BizManagePro.showAlert(message, 'success');

                // Redirect to transactions list
                setTimeout(function() {
                    window.location.href = '<?php echo admin_url('admin.php?page=bizmanage-finances&entity_id=' . $selected_entity_id); ?>';
                }, 1500);
            } else {
                var errorMessage = response.data && response.data.message ? response.data.message :
                                  response.message ? response.message : 'Failed to save transaction.';
                BizManagePro.showAlert(errorMessage, 'error');
                console.log('Transaction save failed:', response);
            }
        }).fail(function(xhr, status, error) {
            console.log('Transaction save AJAX error:', error);
            console.log('XHR response:', xhr.responseText);
            BizManagePro.showAlert('Error saving transaction: ' + error, 'error');
        });
    }

    // Transaction save button click handler
    $(document).on('click', '.bizmanage-save-transaction', function(e) {
        e.preventDefault();
        saveTransaction();
    });

    // Add New Category button click handler
    $(document).on('click', '#add-category-btn', function(e) {
        e.preventDefault();
        var transactionType = $('#transaction_type').val() || 'income';
        showCategoryModal(null, transactionType);
    });

    // Manage Categories button click handler
    $(document).on('click', '#manage-categories-btn', function(e) {
        e.preventDefault();
        showCategoryManagementModal();
    });
});
</script>

<style>
.positive {
    color: #28a745;
}

.negative {
    color: #dc3545;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-deleted {
    background-color: #f8d7da;
    color: #721c24;
}
</style>

<?php
/**
 * Fix Category System for BizManage Pro
 * 
 * This script will diagnose and fix category-related issues
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>BizManage Pro Category System Fix</h1>';

global $wpdb;

// Check if plugin is active
$plugin_file = 'bizmanage-pro/bizmanage-pro.php';
if (!is_plugin_active($plugin_file)) {
    echo '<p style="color: red;">❌ BizManage Pro plugin is not active!</p>';
    echo '<p><a href="' . admin_url('plugins.php') . '">Activate Plugin</a></p>';
    exit;
}

// Check categories table
echo '<h2>1. Categories Table Check</h2>';
$categories_table = $wpdb->prefix . 'bizmanage_categories';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$categories_table'") === $categories_table;

if (!$table_exists) {
    echo '<p style="color: red;">❌ Categories table does not exist!</p>';
    echo '<p><a href="setup-database.php">Create Database Tables</a></p>';
    exit;
} else {
    echo '<p style="color: green;">✅ Categories table exists</p>';
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE $categories_table");
    echo '<h3>Table Structure:</h3>';
    echo '<table border="1" style="border-collapse: collapse;">';
    echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>';
    foreach ($columns as $column) {
        echo '<tr>';
        echo '<td>' . $column->Field . '</td>';
        echo '<td>' . $column->Type . '</td>';
        echo '<td>' . $column->Null . '</td>';
        echo '<td>' . $column->Key . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}

// Check business entities
echo '<h2>2. Business Entities Check</h2>';
$entities_table = $wpdb->prefix . 'bizmanage_business_entities';
$entities = $wpdb->get_results("SELECT id, business_name, status FROM $entities_table WHERE status = 'active'");

if (empty($entities)) {
    echo '<p style="color: red;">❌ No active business entities found!</p>';
    
    // Create default entity
    if (isset($_POST['create_entity'])) {
        $result = $wpdb->insert(
            $entities_table,
            array(
                'business_name' => 'Default Business',
                'entity_type' => 'Sole Proprietorship',
                'currency' => 'USD',
                'status' => 'active',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            )
        );
        
        if ($result) {
            echo '<p style="color: green;">✅ Default business entity created with ID: ' . $wpdb->insert_id . '</p>';
            $entities = $wpdb->get_results("SELECT id, business_name, status FROM $entities_table WHERE status = 'active'");
        } else {
            echo '<p style="color: red;">❌ Failed to create entity: ' . $wpdb->last_error . '</p>';
        }
    } else {
        echo '<form method="post">';
        echo '<button type="submit" name="create_entity" value="1">Create Default Business Entity</button>';
        echo '</form>';
    }
} else {
    echo '<p style="color: green;">✅ Business entities found:</p>';
    echo '<ul>';
    foreach ($entities as $entity) {
        echo '<li>ID: ' . $entity->id . ' - ' . $entity->business_name . '</li>';
    }
    echo '</ul>';
}

// Check existing categories
echo '<h2>3. Existing Categories Check</h2>';
$categories = $wpdb->get_results("SELECT * FROM $categories_table ORDER BY entity_id, type, name");

if (empty($categories)) {
    echo '<p style="color: orange;">⚠️ No categories found</p>';
} else {
    echo '<p style="color: green;">✅ Categories found (' . count($categories) . '):</p>';
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Entity ID</th><th>Name</th><th>Type</th><th>Status</th><th>Created</th></tr>';
    foreach ($categories as $cat) {
        echo '<tr>';
        echo '<td>' . $cat->id . '</td>';
        echo '<td>' . $cat->entity_id . '</td>';
        echo '<td>' . $cat->name . '</td>';
        echo '<td>' . $cat->type . '</td>';
        echo '<td>' . $cat->status . '</td>';
        echo '<td>' . $cat->created_at . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}

// Test category creation
echo '<h2>4. Test Category Creation</h2>';

if (isset($_POST['test_category'])) {
    $entity_id = intval($_POST['entity_id']);
    $category_name = sanitize_text_field($_POST['category_name']);
    $category_type = sanitize_text_field($_POST['category_type']);
    
    if ($entity_id && $category_name && $category_type) {
        // Check if category already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $categories_table WHERE entity_id = %d AND name = %s AND type = %s",
            $entity_id, $category_name, $category_type
        ));
        
        if ($existing > 0) {
            echo '<p style="color: orange;">⚠️ Category already exists!</p>';
        } else {
            $result = $wpdb->insert(
                $categories_table,
                array(
                    'entity_id' => $entity_id,
                    'name' => $category_name,
                    'type' => $category_type,
                    'description' => 'Test category created by fix script',
                    'color' => '#007cba',
                    'status' => 'active',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                )
            );
            
            if ($result) {
                echo '<p style="color: green;">✅ Test category created successfully! ID: ' . $wpdb->insert_id . '</p>';
            } else {
                echo '<p style="color: red;">❌ Failed to create category: ' . $wpdb->last_error . '</p>';
            }
        }
    } else {
        echo '<p style="color: red;">❌ Missing required fields</p>';
    }
}

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<table>';
    echo '<tr><td>Entity:</td><td><select name="entity_id" required>';
    foreach ($entities as $entity) {
        echo '<option value="' . $entity->id . '">' . $entity->business_name . '</option>';
    }
    echo '</select></td></tr>';
    echo '<tr><td>Category Name:</td><td><input type="text" name="category_name" value="Test Category ' . time() . '" required></td></tr>';
    echo '<tr><td>Type:</td><td><select name="category_type" required>';
    echo '<option value="income">Income</option>';
    echo '<option value="expense">Expense</option>';
    echo '</select></td></tr>';
    echo '<tr><td colspan="2"><button type="submit" name="test_category" value="1">Create Test Category</button></td></tr>';
    echo '</table>';
    echo '</form>';
}

// Check AJAX handlers
echo '<h2>5. AJAX Handlers Check</h2>';

// Check if BizManage Pro classes are loaded
$classes_to_check = array(
    'BizManage_Pro_Categories',
    'BizManage_Pro_Ajax',
    'BizManage_Pro_Database',
    'BizManage_Pro_Security'
);

foreach ($classes_to_check as $class) {
    if (class_exists($class)) {
        echo '<p style="color: green;">✅ ' . $class . ' class loaded</p>';
    } else {
        echo '<p style="color: red;">❌ ' . $class . ' class not loaded</p>';
    }
}

// Check AJAX actions
echo '<h3>AJAX Actions Check:</h3>';
$ajax_actions = array(
    'bizmanage_save_category',
    'bizmanage_get_categories',
    'bizmanage_delete_category'
);

foreach ($ajax_actions as $action) {
    if (has_action('wp_ajax_' . $action)) {
        echo '<p style="color: green;">✅ ' . $action . ' AJAX handler registered</p>';
    } else {
        echo '<p style="color: red;">❌ ' . $action . ' AJAX handler not registered</p>';
    }
}

// Test AJAX category save
echo '<h2>6. Test AJAX Category Save</h2>';

if (isset($_POST['test_ajax']) && !empty($entities)) {
    $entity_id = $entities[0]->id;
    
    // Simulate AJAX request
    $_POST['nonce'] = wp_create_nonce('bizmanage_save_category');
    $_POST['entity_id'] = $entity_id;
    $_POST['name'] = 'AJAX Test Category ' . time();
    $_POST['type'] = 'income';
    $_POST['description'] = 'Test category via AJAX simulation';
    $_POST['color'] = '#28a745';
    
    if (class_exists('BizManage_Pro_Categories')) {
        $categories_class = BizManage_Pro_Categories::instance();
        
        // Test create_category method
        $result = $categories_class->create_category($_POST);
        
        if ($result) {
            echo '<p style="color: green;">✅ AJAX category creation successful! ID: ' . $result . '</p>';
        } else {
            echo '<p style="color: red;">❌ AJAX category creation failed</p>';
        }
    } else {
        echo '<p style="color: red;">❌ BizManage_Pro_Categories class not available</p>';
    }
}

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<button type="submit" name="test_ajax" value="1">Test AJAX Category Creation</button>';
    echo '</form>';
}

// Create default categories
echo '<h2>7. Create Default Categories</h2>';

if (isset($_POST['create_defaults']) && !empty($entities)) {
    $entity_id = $entities[0]->id;
    
    $default_income_categories = array(
        'Sales Revenue', 'Service Revenue', 'Interest Income', 'Other Income'
    );
    
    $default_expense_categories = array(
        'Office Supplies', 'Marketing', 'Travel', 'Utilities', 'Rent', 'Insurance'
    );
    
    $created_count = 0;
    
    foreach ($default_income_categories as $category) {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $categories_table WHERE entity_id = %d AND name = %s AND type = 'income'",
            $entity_id, $category
        ));
        
        if ($existing == 0) {
            $result = $wpdb->insert(
                $categories_table,
                array(
                    'entity_id' => $entity_id,
                    'name' => $category,
                    'type' => 'income',
                    'color' => '#28a745',
                    'is_default' => 1,
                    'status' => 'active',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                )
            );
            
            if ($result) {
                $created_count++;
            }
        }
    }
    
    foreach ($default_expense_categories as $category) {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $categories_table WHERE entity_id = %d AND name = %s AND type = 'expense'",
            $entity_id, $category
        ));
        
        if ($existing == 0) {
            $result = $wpdb->insert(
                $categories_table,
                array(
                    'entity_id' => $entity_id,
                    'name' => $category,
                    'type' => 'expense',
                    'color' => '#dc3545',
                    'is_default' => 1,
                    'status' => 'active',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                )
            );
            
            if ($result) {
                $created_count++;
            }
        }
    }
    
    echo '<p style="color: green;">✅ Created ' . $created_count . ' default categories</p>';
}

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<button type="submit" name="create_defaults" value="1">Create Default Categories</button>';
    echo '</form>';
}

// Debug information
echo '<h2>8. Debug Information</h2>';
echo '<p><strong>WordPress Version:</strong> ' . get_bloginfo('version') . '</p>';
echo '<p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>';
echo '<p><strong>Plugin Active:</strong> ' . (is_plugin_active($plugin_file) ? 'Yes' : 'No') . '</p>';
echo '<p><strong>Current User ID:</strong> ' . get_current_user_id() . '</p>';
echo '<p><strong>Current User Capabilities:</strong></p>';

$user = wp_get_current_user();
$bizmanage_caps = array();
foreach ($user->allcaps as $cap => $has_cap) {
    if (strpos($cap, 'bizmanage') !== false && $has_cap) {
        $bizmanage_caps[] = $cap;
    }
}

if (!empty($bizmanage_caps)) {
    echo '<ul>';
    foreach ($bizmanage_caps as $cap) {
        echo '<li>' . $cap . '</li>';
    }
    echo '</ul>';
} else {
    echo '<p style="color: red;">No BizManage capabilities found</p>';
}

// Next steps
echo '<h2>9. Next Steps</h2>';
echo '<ol>';
echo '<li><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">Go to BizManage Pro Dashboard</a></li>';
echo '<li><a href="check-debug-log.php">Check Debug Log</a></li>';
echo '<li><a href="troubleshoot-bizmanage.php">Run Full Troubleshoot</a></li>';
echo '</ol>';

// Auto refresh
if (isset($_GET['auto_refresh'])) {
    echo '<meta http-equiv="refresh" content="10">';
    echo '<p style="color: blue;">Page will auto-refresh every 10 seconds...</p>';
    echo '<p><a href="?">Stop Auto Refresh</a></p>';
} else {
    echo '<p><a href="?auto_refresh=1">Enable Auto Refresh</a></p>';
}
?>

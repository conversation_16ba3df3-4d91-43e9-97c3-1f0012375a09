<?php
/**
 * Quick Category Fix - Works from any directory
 * 
 * This script will fix category issues and works from plugin directory too
 */

// Find WordPress root directory
$wp_root = dirname(__FILE__);
$max_depth = 5;
$depth = 0;

while ($depth < $max_depth) {
    if (file_exists($wp_root . '/wp-config.php') && file_exists($wp_root . '/wp-load.php')) {
        break;
    }
    $wp_root = dirname($wp_root);
    $depth++;
}

if (!file_exists($wp_root . '/wp-load.php')) {
    die('WordPress installation not found. Please place this file in WordPress root directory or plugin directory.');
}

// WordPress environment
define('WP_USE_THEMES', false);
require_once($wp_root . '/wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Quick Category Fix</h1>';
echo '<p><strong>WordPress Root:</strong> ' . $wp_root . '</p>';

// Check and create business entity
echo '<h2>1. Business Entity Check</h2>';
global $wpdb;
$entities_table = $wpdb->prefix . 'bizmanage_business_entities';
$entities = $wpdb->get_results("SELECT * FROM $entities_table WHERE status = 'active'");

if (empty($entities)) {
    echo '<p style="color: red;">❌ No active business entities found!</p>';
    
    // Auto-create default entity
    $result = $wpdb->insert(
        $entities_table,
        array(
            'business_name' => 'My Business',
            'entity_type' => 'Sole Proprietorship',
            'currency' => 'USD',
            'status' => 'active',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        )
    );
    
    if ($result) {
        echo '<p style="color: green;">✅ Default business entity created with ID: ' . $wpdb->insert_id . '</p>';
        $entities = $wpdb->get_results("SELECT * FROM $entities_table WHERE status = 'active'");
    } else {
        echo '<p style="color: red;">❌ Failed to create entity: ' . $wpdb->last_error . '</p>';
    }
} else {
    echo '<p style="color: green;">✅ Active business entities found:</p>';
    foreach ($entities as $entity) {
        echo '<p>ID: ' . $entity->id . ' - ' . $entity->business_name . '</p>';
    }
}

// Test category creation
echo '<h2>2. Test Category Creation</h2>';

if (isset($_POST['create_test_category']) && !empty($entities)) {
    $entity_id = $entities[0]->id;
    $category_name = 'Test Category ' . time();
    
    // Direct database insert
    $categories_table = $wpdb->prefix . 'bizmanage_categories';
    $result = $wpdb->insert(
        $categories_table,
        array(
            'entity_id' => $entity_id,
            'name' => $category_name,
            'type' => 'income',
            'description' => 'Test category created by quick fix',
            'color' => '#007cba',
            'status' => 'active',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        )
    );
    
    if ($result) {
        echo '<p style="color: green;">✅ Test category created successfully! ID: ' . $wpdb->insert_id . '</p>';
    } else {
        echo '<p style="color: red;">❌ Failed to create category: ' . $wpdb->last_error . '</p>';
    }
}

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<button type="submit" name="create_test_category" value="1">Create Test Category</button>';
    echo '</form>';
}

// Test AJAX category save
echo '<h2>3. Test AJAX Category Save</h2>';

if (isset($_POST['test_ajax_category']) && !empty($entities)) {
    $entity_id = $entities[0]->id;
    
    // Simulate AJAX request
    $_POST = array(
        'action' => 'bizmanage_save_category',
        'nonce' => wp_create_nonce('bizmanage_save_category'),
        'entity_id' => $entity_id,
        'name' => 'AJAX Test ' . time(),
        'type' => 'income',
        'description' => 'Test via AJAX simulation',
        'color' => '#28a745'
    );
    
    echo '<h3>AJAX Data:</h3>';
    echo '<pre>' . json_encode($_POST, JSON_PRETTY_PRINT) . '</pre>';
    
    if (class_exists('BizManage_Pro_Categories')) {
        $categories = BizManage_Pro_Categories::instance();
        
        // Capture output
        ob_start();
        $categories->ajax_save_category();
        $output = ob_get_clean();
        
        echo '<h3>AJAX Output:</h3>';
        echo '<pre>' . htmlspecialchars($output) . '</pre>';
    } else {
        echo '<p style="color: red;">❌ BizManage_Pro_Categories class not found</p>';
    }
}

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<button type="submit" name="test_ajax_category" value="1">Test AJAX Category Save</button>';
    echo '</form>';
}

// Current categories
echo '<h2>4. Current Categories</h2>';
$categories_table = $wpdb->prefix . 'bizmanage_categories';
$categories = $wpdb->get_results("SELECT * FROM $categories_table ORDER BY id DESC LIMIT 10");

if (!empty($categories)) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Entity ID</th><th>Name</th><th>Type</th><th>Status</th><th>Created</th></tr>';
    foreach ($categories as $cat) {
        echo '<tr>';
        echo '<td>' . $cat->id . '</td>';
        echo '<td>' . $cat->entity_id . '</td>';
        echo '<td>' . $cat->name . '</td>';
        echo '<td>' . $cat->type . '</td>';
        echo '<td>' . $cat->status . '</td>';
        echo '<td>' . $cat->created_at . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p>No categories found.</p>';
}

// JavaScript test
echo '<h2>5. JavaScript Test</h2>';
echo '<div id="js-result"></div>';

if (!empty($entities)) {
    $entity_id = $entities[0]->id;
    echo '<button onclick="testCategoryJS()">Test Category via JavaScript</button>';
    
    echo '<script>
    function testCategoryJS() {
        var data = {
            action: "bizmanage_save_category",
            nonce: "' . wp_create_nonce('bizmanage_save_category') . '",
            entity_id: ' . $entity_id . ',
            name: "JS Test " + Date.now(),
            type: "income",
            description: "Test from JavaScript",
            color: "#dc3545"
        };
        
        console.log("JavaScript AJAX data:", data);
        
        jQuery.post(ajaxurl, data, function(response) {
            console.log("JavaScript AJAX response:", response);
            document.getElementById("js-result").innerHTML = 
                "<h4>Response:</h4><pre>" + JSON.stringify(response, null, 2) + "</pre>";
        }).fail(function(xhr, status, error) {
            console.log("JavaScript AJAX error:", error);
            document.getElementById("js-result").innerHTML = 
                "<h4 style=\"color: red;\">Error:</h4><pre>" + xhr.responseText + "</pre>";
        });
    }
    </script>';
}

// Recent debug log
echo '<h2>6. Recent Debug Log</h2>';
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -10);
    
    $bizmanage_lines = array();
    foreach ($recent_lines as $line) {
        if (strpos($line, 'BizManagePro') !== false) {
            $bizmanage_lines[] = $line;
        }
    }
    
    if (!empty($bizmanage_lines)) {
        echo '<div style="background: #f0f0f0; padding: 10px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">';
        foreach ($bizmanage_lines as $line) {
            echo htmlspecialchars($line) . '<br>';
        }
        echo '</div>';
    } else {
        echo '<p>No recent BizManage Pro log entries.</p>';
    }
} else {
    echo '<p>Debug log file not found.</p>';
}

// Instructions
echo '<h2>7. Next Steps</h2>';
echo '<ol>';
echo '<li>Test the buttons above to verify category creation works</li>';
echo '<li>Go to <a href="' . admin_url('admin.php?page=bizmanage-finances') . '">BizManage Pro Finances</a></li>';
echo '<li>Try creating a category from the dashboard</li>';
echo '<li>Check browser console (F12) for any JavaScript errors</li>';
echo '</ol>';

echo '<h2>8. Quick Links</h2>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">BizManage Pro Dashboard</a></p>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances') . '">Finances Page</a></p>';
echo '<p><a href="' . admin_url('plugins.php') . '">WordPress Plugins</a></p>';
?>

<?php
/**
 * BizManage Pro Settings Class
 *
 * Handles plugin settings management and configuration
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Settings Class
 */
class BizManage_Pro_Settings {

    /**
     * Instance of this class
     * @var BizManage_Pro_Settings
     */
    private static $instance = null;

    /**
     * Database instance
     * @var BizManage_Pro_Database
     */
    private $db;

    /**
     * Security instance
     * @var BizManage_Pro_Security
     */
    private $security;

    /**
     * Utilities instance
     * @var BizManage_Pro_Utilities
     */
    private $utilities;

    /**
     * Default settings
     * @var array
     */
    private $default_settings;

    /**
     * Get instance
     * @return BizManage_Pro_Settings
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->db = BizManage_Pro_Database::instance();
        $this->security = BizManage_Pro_Security::instance();
        $this->utilities = BizManage_Pro_Utilities::instance();
        $this->init_default_settings();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_ajax_bizmanage_save_settings', array($this, 'ajax_save_settings'));
        add_action('wp_ajax_bizmanage_reset_settings', array($this, 'ajax_reset_settings'));
        add_action('wp_ajax_bizmanage_export_settings', array($this, 'ajax_export_settings'));
        add_action('wp_ajax_bizmanage_import_settings', array($this, 'ajax_import_settings'));
    }

    /**
     * Initialize default settings
     */
    private function init_default_settings() {
        $this->default_settings = array(
            // General Settings
            'general' => array(
                'default_currency' => 'USD',
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i:s',
                'timezone' => 'UTC',
                'decimal_places' => 2,
                'thousand_separator' => ',',
                'decimal_separator' => '.',
            ),
            
            // Financial Settings
            'financial' => array(
                'fiscal_year_start' => '01-01',
                'default_tax_rate' => '0.00',
                'enable_multi_currency' => '1',
                'auto_currency_conversion' => '1',
                'default_payment_method' => 'cash',
                'enable_recurring_transactions' => '1',
            ),
            
            // Document Settings
            'documents' => array(
                'max_file_size' => '10485760', // 10MB
                'allowed_file_types' => 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png',
                'encryption_enabled' => '1',
                'auto_categorize' => '0',
                'require_description' => '0',
                'enable_versioning' => '1',
            ),
            
            // Security Settings
            'security' => array(
                'enable_audit_log' => '1',
                'session_timeout' => '3600', // 1 hour
                'max_login_attempts' => '5',
                'enable_two_factor' => '0',
                'password_strength' => 'medium',
                'data_retention_days' => '2555', // 7 years
            ),
            
            // Backup Settings
            'backup' => array(
                'auto_backup' => '1',
                'backup_frequency' => 'weekly',
                'backup_retention' => '12', // 12 backups
                'include_documents' => '1',
                'backup_location' => 'local',
                'email_notifications' => '1',
            ),
            
            // Notification Settings
            'notifications' => array(
                'email_notifications' => '1',
                'admin_email' => get_option('admin_email'),
                'transaction_notifications' => '1',
                'document_notifications' => '1',
                'report_notifications' => '0',
                'backup_notifications' => '1',
            ),
            
            // Advanced Settings
            'advanced' => array(
                'debug_mode' => '0',
                'cache_enabled' => '1',
                'api_enabled' => '0',
                'webhook_enabled' => '0',
                'custom_css' => '',
                'remove_data_on_uninstall' => '0',
            ),
        );
    }

    /**
     * Get setting value
     * @param string $key
     * @param string $section
     * @param int $entity_id
     * @return mixed
     */
    public function get($key, $section = 'general', $entity_id = null) {
        // Try to get entity-specific setting first
        if ($entity_id) {
            $entity_setting = $this->get_entity_setting($entity_id, $section . '_' . $key);
            if ($entity_setting !== null) {
                return $entity_setting;
            }
        }

        // Fall back to global setting
        $option_name = 'bizmanage_pro_' . $section . '_' . $key;
        $value = get_option($option_name);

        // If no value found, return default
        if ($value === false && isset($this->default_settings[$section][$key])) {
            return $this->default_settings[$section][$key];
        }

        return $value;
    }

    /**
     * Set setting value
     * @param string $key
     * @param mixed $value
     * @param string $section
     * @param int $entity_id
     * @return bool
     */
    public function set($key, $value, $section = 'general', $entity_id = null) {
        if ($entity_id) {
            return $this->set_entity_setting($entity_id, $section . '_' . $key, $value);
        }

        $option_name = 'bizmanage_pro_' . $section . '_' . $key;
        return update_option($option_name, $value);
    }

    /**
     * Get all settings for a section
     * @param string $section
     * @param int $entity_id
     * @return array
     */
    public function get_section($section, $entity_id = null) {
        $settings = array();

        if (isset($this->default_settings[$section])) {
            foreach ($this->default_settings[$section] as $key => $default_value) {
                $settings[$key] = $this->get($key, $section, $entity_id);
            }
        }

        return $settings;
    }

    /**
     * Set multiple settings for a section
     * @param string $section
     * @param array $settings
     * @param int $entity_id
     * @return bool
     */
    public function set_section($section, $settings, $entity_id = null) {
        $success = true;

        foreach ($settings as $key => $value) {
            if (!$this->set($key, $value, $section, $entity_id)) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Get entity-specific setting
     * @param int $entity_id
     * @param string $key
     * @return mixed|null
     */
    private function get_entity_setting($entity_id, $key) {
        $setting = $this->db->get_row('settings', array(
            'entity_id' => $entity_id,
            'setting_key' => $key
        ));

        return $setting ? $setting->setting_value : null;
    }

    /**
     * Set entity-specific setting
     * @param int $entity_id
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    private function set_entity_setting($entity_id, $key, $value) {
        // Check if setting exists
        $existing = $this->db->get_row('settings', array(
            'entity_id' => $entity_id,
            'setting_key' => $key
        ));

        if ($existing) {
            return $this->db->update('settings', 
                array('setting_value' => $value, 'updated_at' => current_time('mysql')), 
                array('id' => $existing->id)
            ) !== false;
        } else {
            return $this->db->insert('settings', array(
                'entity_id' => $entity_id,
                'setting_key' => $key,
                'setting_value' => $value
            )) !== false;
        }
    }

    /**
     * Reset settings to defaults
     * @param string $section
     * @param int $entity_id
     * @return bool
     */
    public function reset_section($section, $entity_id = null) {
        if (!isset($this->default_settings[$section])) {
            return false;
        }

        return $this->set_section($section, $this->default_settings[$section], $entity_id);
    }

    /**
     * Get all available settings sections
     * @return array
     */
    public function get_sections() {
        return array(
            'general' => __('General Settings', 'bizmanage-pro'),
            'financial' => __('Financial Settings', 'bizmanage-pro'),
            'documents' => __('Document Settings', 'bizmanage-pro'),
            'security' => __('Security Settings', 'bizmanage-pro'),
            'backup' => __('Backup Settings', 'bizmanage-pro'),
            'notifications' => __('Notification Settings', 'bizmanage-pro'),
            'advanced' => __('Advanced Settings', 'bizmanage-pro'),
        );
    }

    /**
     * Get setting field configuration
     * @param string $section
     * @return array
     */
    public function get_field_config($section) {
        $configs = array(
            'general' => array(
                'default_currency' => array(
                    'type' => 'select',
                    'label' => __('Default Currency', 'bizmanage-pro'),
                    'description' => __('Default currency for new transactions', 'bizmanage-pro'),
                    'options' => $this->utilities->get_currencies(),
                ),
                'date_format' => array(
                    'type' => 'select',
                    'label' => __('Date Format', 'bizmanage-pro'),
                    'description' => __('Format for displaying dates', 'bizmanage-pro'),
                    'options' => array(
                        'Y-m-d' => 'YYYY-MM-DD',
                        'm/d/Y' => 'MM/DD/YYYY',
                        'd/m/Y' => 'DD/MM/YYYY',
                        'd-m-Y' => 'DD-MM-YYYY',
                    ),
                ),
                'time_format' => array(
                    'type' => 'select',
                    'label' => __('Time Format', 'bizmanage-pro'),
                    'description' => __('Format for displaying time', 'bizmanage-pro'),
                    'options' => array(
                        'H:i:s' => '24-hour (HH:MM:SS)',
                        'h:i:s A' => '12-hour (HH:MM:SS AM/PM)',
                        'H:i' => '24-hour (HH:MM)',
                        'h:i A' => '12-hour (HH:MM AM/PM)',
                    ),
                ),
                'timezone' => array(
                    'type' => 'select',
                    'label' => __('Timezone', 'bizmanage-pro'),
                    'description' => __('Default timezone for the application', 'bizmanage-pro'),
                    'options' => $this->get_timezone_options(),
                ),
            ),
            
            'financial' => array(
                'fiscal_year_start' => array(
                    'type' => 'text',
                    'label' => __('Fiscal Year Start', 'bizmanage-pro'),
                    'description' => __('Start date of fiscal year (MM-DD format)', 'bizmanage-pro'),
                    'placeholder' => '01-01',
                ),
                'default_tax_rate' => array(
                    'type' => 'number',
                    'label' => __('Default Tax Rate (%)', 'bizmanage-pro'),
                    'description' => __('Default tax rate for transactions', 'bizmanage-pro'),
                    'step' => '0.01',
                    'min' => '0',
                    'max' => '100',
                ),
                'enable_multi_currency' => array(
                    'type' => 'checkbox',
                    'label' => __('Enable Multi-Currency', 'bizmanage-pro'),
                    'description' => __('Allow transactions in different currencies', 'bizmanage-pro'),
                ),
                'auto_currency_conversion' => array(
                    'type' => 'checkbox',
                    'label' => __('Auto Currency Conversion', 'bizmanage-pro'),
                    'description' => __('Automatically convert currencies using exchange rates', 'bizmanage-pro'),
                ),
            ),
            
            'documents' => array(
                'max_file_size' => array(
                    'type' => 'select',
                    'label' => __('Maximum File Size', 'bizmanage-pro'),
                    'description' => __('Maximum allowed file size for uploads', 'bizmanage-pro'),
                    'options' => array(
                        '1048576' => '1 MB',
                        '5242880' => '5 MB',
                        '10485760' => '10 MB',
                        '20971520' => '20 MB',
                        '52428800' => '50 MB',
                    ),
                ),
                'allowed_file_types' => array(
                    'type' => 'text',
                    'label' => __('Allowed File Types', 'bizmanage-pro'),
                    'description' => __('Comma-separated list of allowed file extensions', 'bizmanage-pro'),
                    'placeholder' => 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png',
                ),
                'encryption_enabled' => array(
                    'type' => 'checkbox',
                    'label' => __('Enable File Encryption', 'bizmanage-pro'),
                    'description' => __('Encrypt uploaded documents for security', 'bizmanage-pro'),
                ),
                'enable_versioning' => array(
                    'type' => 'checkbox',
                    'label' => __('Enable Document Versioning', 'bizmanage-pro'),
                    'description' => __('Keep track of document versions', 'bizmanage-pro'),
                ),
            ),
            
            'security' => array(
                'enable_audit_log' => array(
                    'type' => 'checkbox',
                    'label' => __('Enable Audit Log', 'bizmanage-pro'),
                    'description' => __('Log user actions for security auditing', 'bizmanage-pro'),
                ),
                'session_timeout' => array(
                    'type' => 'select',
                    'label' => __('Session Timeout', 'bizmanage-pro'),
                    'description' => __('Automatic logout time for inactive users', 'bizmanage-pro'),
                    'options' => array(
                        '1800' => '30 minutes',
                        '3600' => '1 hour',
                        '7200' => '2 hours',
                        '14400' => '4 hours',
                        '28800' => '8 hours',
                    ),
                ),
                'data_retention_days' => array(
                    'type' => 'number',
                    'label' => __('Data Retention (Days)', 'bizmanage-pro'),
                    'description' => __('How long to keep deleted data before permanent removal', 'bizmanage-pro'),
                    'min' => '30',
                    'max' => '3650',
                ),
            ),
            
            'backup' => array(
                'auto_backup' => array(
                    'type' => 'checkbox',
                    'label' => __('Enable Auto Backup', 'bizmanage-pro'),
                    'description' => __('Automatically backup data at scheduled intervals', 'bizmanage-pro'),
                ),
                'backup_frequency' => array(
                    'type' => 'select',
                    'label' => __('Backup Frequency', 'bizmanage-pro'),
                    'description' => __('How often to create automatic backups', 'bizmanage-pro'),
                    'options' => array(
                        'daily' => __('Daily', 'bizmanage-pro'),
                        'weekly' => __('Weekly', 'bizmanage-pro'),
                        'monthly' => __('Monthly', 'bizmanage-pro'),
                    ),
                ),
                'backup_retention' => array(
                    'type' => 'number',
                    'label' => __('Backup Retention', 'bizmanage-pro'),
                    'description' => __('Number of backups to keep', 'bizmanage-pro'),
                    'min' => '1',
                    'max' => '100',
                ),
            ),
            
            'notifications' => array(
                'email_notifications' => array(
                    'type' => 'checkbox',
                    'label' => __('Enable Email Notifications', 'bizmanage-pro'),
                    'description' => __('Send email notifications for important events', 'bizmanage-pro'),
                ),
                'admin_email' => array(
                    'type' => 'email',
                    'label' => __('Admin Email', 'bizmanage-pro'),
                    'description' => __('Email address for administrative notifications', 'bizmanage-pro'),
                ),
                'transaction_notifications' => array(
                    'type' => 'checkbox',
                    'label' => __('Transaction Notifications', 'bizmanage-pro'),
                    'description' => __('Notify when new transactions are added', 'bizmanage-pro'),
                ),
            ),
            
            'advanced' => array(
                'debug_mode' => array(
                    'type' => 'checkbox',
                    'label' => __('Debug Mode', 'bizmanage-pro'),
                    'description' => __('Enable debug logging (for troubleshooting)', 'bizmanage-pro'),
                ),
                'cache_enabled' => array(
                    'type' => 'checkbox',
                    'label' => __('Enable Caching', 'bizmanage-pro'),
                    'description' => __('Cache data for improved performance', 'bizmanage-pro'),
                ),
                'remove_data_on_uninstall' => array(
                    'type' => 'checkbox',
                    'label' => __('Remove Data on Uninstall', 'bizmanage-pro'),
                    'description' => __('WARNING: This will permanently delete all data when the plugin is uninstalled', 'bizmanage-pro'),
                ),
            ),
        );

        return isset($configs[$section]) ? $configs[$section] : array();
    }

    /**
     * Get timezone options
     * @return array
     */
    private function get_timezone_options() {
        $timezones = timezone_identifiers_list();
        $options = array();
        
        foreach ($timezones as $timezone) {
            $options[$timezone] = $timezone;
        }
        
        return $options;
    }

    /**
     * AJAX handler for saving settings
     */
    public function ajax_save_settings() {
        // Verify nonce and permissions
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'bizmanage_ajax')) {
            wp_send_json_error(__('Security check failed.', 'bizmanage-pro'));
        }

        if (!current_user_can('bizmanage_manage_settings')) {
            wp_send_json_error(__('You do not have permission to manage settings.', 'bizmanage-pro'));
        }

        $section = sanitize_text_field($_POST['section'] ?? '');
        $settings = $_POST['settings'] ?? array();
        $entity_id = intval($_POST['entity_id'] ?? 0);

        if (empty($section)) {
            wp_send_json_error(__('Invalid section.', 'bizmanage-pro'));
        }

        // Sanitize settings
        $sanitized_settings = array();
        $field_config = $this->get_field_config($section);

        foreach ($settings as $key => $value) {
            if (isset($field_config[$key])) {
                $config = $field_config[$key];
                
                switch ($config['type']) {
                    case 'checkbox':
                        $sanitized_settings[$key] = $value ? '1' : '0';
                        break;
                    case 'number':
                        $sanitized_settings[$key] = floatval($value);
                        break;
                    case 'email':
                        $sanitized_settings[$key] = sanitize_email($value);
                        break;
                    default:
                        $sanitized_settings[$key] = sanitize_text_field($value);
                }
            }
        }

        // Save settings
        if ($this->set_section($section, $sanitized_settings, $entity_id ?: null)) {
            wp_send_json_success(__('Settings saved successfully.', 'bizmanage-pro'));
        } else {
            wp_send_json_error(__('Failed to save settings.', 'bizmanage-pro'));
        }
    }

    /**
     * AJAX handler for resetting settings
     */
    public function ajax_reset_settings() {
        // Verify nonce and permissions
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'bizmanage_ajax')) {
            wp_send_json_error(__('Security check failed.', 'bizmanage-pro'));
        }

        if (!current_user_can('bizmanage_manage_settings')) {
            wp_send_json_error(__('You do not have permission to manage settings.', 'bizmanage-pro'));
        }

        $section = sanitize_text_field($_POST['section'] ?? '');
        $entity_id = intval($_POST['entity_id'] ?? 0);

        if ($this->reset_section($section, $entity_id ?: null)) {
            wp_send_json_success(__('Settings reset to defaults.', 'bizmanage-pro'));
        } else {
            wp_send_json_error(__('Failed to reset settings.', 'bizmanage-pro'));
        }
    }
}

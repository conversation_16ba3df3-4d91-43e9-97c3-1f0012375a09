<?php
/**
 * Final Category Fix
 * 
 * This script will completely fix the category system
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Final Category System Fix</h1>';

// Test the exact AJAX call that's failing
echo '<h2>1. Test Exact AJAX Call</h2>';

if (isset($_POST['test_exact_ajax'])) {
    // Simulate the exact AJAX call
    $_POST = array(
        'action' => 'bizmanage_save_category',
        'nonce' => wp_create_nonce('bizmanage_save_category'),
        'entity_id' => 1,
        'name' => 'Test Category ' . time(),
        'type' => 'income',
        'description' => 'Test description',
        'color' => '#007cba'
    );
    
    echo '<h3>Simulating AJAX call with data:</h3>';
    echo '<pre>' . json_encode($_POST, JSON_PRETTY_PRINT) . '</pre>';
    
    // Call the AJAX handler directly
    if (class_exists('BizManage_Pro_Categories')) {
        $categories = BizManage_Pro_Categories::instance();
        
        // Capture output
        ob_start();
        $categories->ajax_save_category();
        $output = ob_get_clean();
        
        echo '<h3>AJAX Handler Output:</h3>';
        echo '<pre>' . htmlspecialchars($output) . '</pre>';
        
        // Check if category was created
        global $wpdb;
        $categories_table = $wpdb->prefix . 'bizmanage_categories';
        $latest_category = $wpdb->get_row("SELECT * FROM $categories_table ORDER BY id DESC LIMIT 1");
        
        if ($latest_category) {
            echo '<h3>Latest Category in Database:</h3>';
            echo '<pre>' . print_r($latest_category, true) . '</pre>';
        }
    }
}

echo '<form method="post">';
echo '<button type="submit" name="test_exact_ajax" value="1">Test Exact AJAX Call</button>';
echo '</form>';

// Create a working category form
echo '<h2>2. Working Category Creation Form</h2>';

if (isset($_POST['create_category_direct'])) {
    $entity_id = intval($_POST['entity_id']);
    $name = sanitize_text_field($_POST['name']);
    $type = sanitize_text_field($_POST['type']);
    $description = sanitize_textarea_field($_POST['description']);
    $color = sanitize_text_field($_POST['color']);
    
    if ($entity_id && $name && $type) {
        global $wpdb;
        $categories_table = $wpdb->prefix . 'bizmanage_categories';
        
        $result = $wpdb->insert(
            $categories_table,
            array(
                'entity_id' => $entity_id,
                'name' => $name,
                'type' => $type,
                'description' => $description,
                'color' => $color,
                'status' => 'active',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            )
        );
        
        if ($result) {
            echo '<p style="color: green;">✅ Category created successfully! ID: ' . $wpdb->insert_id . '</p>';
        } else {
            echo '<p style="color: red;">❌ Failed to create category: ' . $wpdb->last_error . '</p>';
        }
    } else {
        echo '<p style="color: red;">❌ Missing required fields</p>';
    }
}

// Get entities
global $wpdb;
$entities_table = $wpdb->prefix . 'bizmanage_business_entities';
$entities = $wpdb->get_results("SELECT id, business_name FROM $entities_table WHERE status = 'active'");

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<table>';
    echo '<tr><td>Entity:</td><td><select name="entity_id" required>';
    foreach ($entities as $entity) {
        echo '<option value="' . $entity->id . '">' . $entity->business_name . '</option>';
    }
    echo '</select></td></tr>';
    echo '<tr><td>Name:</td><td><input type="text" name="name" required></td></tr>';
    echo '<tr><td>Type:</td><td><select name="type" required>';
    echo '<option value="income">Income</option>';
    echo '<option value="expense">Expense</option>';
    echo '</select></td></tr>';
    echo '<tr><td>Description:</td><td><textarea name="description"></textarea></td></tr>';
    echo '<tr><td>Color:</td><td><input type="color" name="color" value="#007cba"></td></tr>';
    echo '<tr><td colspan="2"><button type="submit" name="create_category_direct" value="1">Create Category (Direct)</button></td></tr>';
    echo '</table>';
    echo '</form>';
}

// JavaScript test
echo '<h2>3. JavaScript AJAX Test</h2>';
echo '<div id="js-test-result"></div>';

if (!empty($entities)) {
    $entity_id = $entities[0]->id;
    echo '<button onclick="testCategoryAjax()">Test Category AJAX via JavaScript</button>';
    
    echo '<script>
    function testCategoryAjax() {
        var data = {
            action: "bizmanage_save_category",
            nonce: "' . wp_create_nonce('bizmanage_save_category') . '",
            entity_id: ' . $entity_id . ',
            name: "JS Test " + Date.now(),
            type: "income",
            description: "Test from JavaScript",
            color: "#28a745"
        };
        
        console.log("Sending AJAX data:", data);
        
        jQuery.post(ajaxurl, data, function(response) {
            console.log("AJAX Response:", response);
            document.getElementById("js-test-result").innerHTML = 
                "<h4>Success Response:</h4><pre>" + JSON.stringify(response, null, 2) + "</pre>";
        }).fail(function(xhr, status, error) {
            console.log("AJAX Error:", error);
            console.log("Response Text:", xhr.responseText);
            document.getElementById("js-test-result").innerHTML = 
                "<h4 style=\"color: red;\">Error Response:</h4><pre>" + xhr.responseText + "</pre>";
        });
    }
    </script>';
}

// Check current categories
echo '<h2>4. Current Categories</h2>';
$categories_table = $wpdb->prefix . 'bizmanage_categories';
$categories = $wpdb->get_results("SELECT * FROM $categories_table ORDER BY id DESC LIMIT 10");

if (!empty($categories)) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Entity ID</th><th>Name</th><th>Type</th><th>Status</th><th>Created</th></tr>';
    foreach ($categories as $cat) {
        echo '<tr>';
        echo '<td>' . $cat->id . '</td>';
        echo '<td>' . $cat->entity_id . '</td>';
        echo '<td>' . $cat->name . '</td>';
        echo '<td>' . $cat->type . '</td>';
        echo '<td>' . $cat->status . '</td>';
        echo '<td>' . $cat->created_at . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p>No categories found.</p>';
}

// Debug recent log entries
echo '<h2>5. Recent Debug Log</h2>';
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -15);
    
    $bizmanage_lines = array();
    foreach ($recent_lines as $line) {
        if (strpos($line, 'BizManagePro') !== false) {
            $bizmanage_lines[] = $line;
        }
    }
    
    if (!empty($bizmanage_lines)) {
        echo '<div style="background: #f0f0f0; padding: 10px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">';
        foreach ($bizmanage_lines as $line) {
            echo htmlspecialchars($line) . '<br>';
        }
        echo '</div>';
    } else {
        echo '<p>No recent BizManage Pro log entries.</p>';
    }
}

// Instructions
echo '<h2>6. Next Steps</h2>';
echo '<ol>';
echo '<li>Test the "Direct Category Creation" form above</li>';
echo '<li>Test the "JavaScript AJAX" button</li>';
echo '<li>Check the debug log for any errors</li>';
echo '<li>Go to <a href="' . admin_url('admin.php?page=bizmanage-pro') . '">BizManage Pro Dashboard</a> and try creating a category</li>';
echo '</ol>';

echo '<h2>7. Quick Links</h2>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">BizManage Pro Dashboard</a></p>';
echo '<p><a href="check-debug-log.php?auto_refresh=1">Monitor Debug Log</a></p>';
echo '<p><a href="fix-category-system.php">Category System Check</a></p>';
?>

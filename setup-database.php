<?php
/**
 * Manual Database Setup for BizManage Pro
 * 
 * Place this file in WordPress root directory and run it to manually create database tables
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>BizManage Pro Manual Database Setup</h1>';

global $wpdb;

$charset_collate = $wpdb->get_charset_collate();

// Business entities table
$table_name = $wpdb->prefix . 'bizmanage_business_entities';
$sql = "CREATE TABLE $table_name (
    id int(11) NOT NULL AUTO_INCREMENT,
    business_name varchar(255) NOT NULL,
    entity_type varchar(100) NOT NULL,
    tax_id varchar(50),
    registration_number varchar(100),
    address_line_1 varchar(255),
    address_line_2 varchar(255),
    city varchar(100),
    state varchar(100),
    postal_code varchar(20),
    country varchar(100),
    phone varchar(20),
    email varchar(100),
    website varchar(255),
    industry varchar(100),
    fiscal_year_start date,
    currency varchar(10) DEFAULT 'USD',
    timezone varchar(50),
    status varchar(20) DEFAULT 'active',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_status (status),
    KEY idx_entity_type (entity_type)
) $charset_collate;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
$result1 = dbDelta($sql);
echo '<p>Business Entities Table: ' . (strpos($result1[$table_name], 'Created') !== false ? 'Created' : 'Updated') . '</p>';

// Documents table
$table_name = $wpdb->prefix . 'bizmanage_documents';
$sql = "CREATE TABLE $table_name (
    id int(11) NOT NULL AUTO_INCREMENT,
    entity_id int(11) NOT NULL,
    title varchar(255) NOT NULL,
    description text,
    file_name varchar(255) NOT NULL,
    file_path varchar(500) NOT NULL,
    file_size int(11),
    file_type varchar(100),
    category varchar(100),
    tags text,
    is_encrypted tinyint(1) DEFAULT 1,
    encryption_key varchar(255),
    uploaded_by int(11),
    status varchar(20) DEFAULT 'active',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_entity_id (entity_id),
    KEY idx_category (category),
    KEY idx_status (status),
    FOREIGN KEY (entity_id) REFERENCES {$wpdb->prefix}bizmanage_business_entities(id) ON DELETE CASCADE
) $charset_collate;";

$result2 = dbDelta($sql);
echo '<p>Documents Table: ' . (strpos($result2[$table_name], 'Created') !== false ? 'Created' : 'Updated') . '</p>';

// Transactions table
$table_name = $wpdb->prefix . 'bizmanage_transactions';
$sql = "CREATE TABLE $table_name (
    id int(11) NOT NULL AUTO_INCREMENT,
    entity_id int(11) NOT NULL,
    transaction_type enum('income','expense') NOT NULL,
    amount decimal(15,4) NOT NULL,
    currency varchar(10) DEFAULT 'USD',
    exchange_rate decimal(10,6) DEFAULT 1.000000,
    base_amount decimal(15,4),
    description text NOT NULL,
    category varchar(100),
    subcategory varchar(100),
    payment_method varchar(100),
    reference_number varchar(100),
    transaction_date date NOT NULL,
    due_date date,
    tax_rate decimal(5,4) DEFAULT 0.0000,
    tax_amount decimal(15,4) DEFAULT 0.0000,
    discount_amount decimal(15,4) DEFAULT 0.0000,
    total_amount decimal(15,4),
    contact_id int(11),
    project_id int(11),
    bank_account_id int(11),
    recurring_transaction_id int(11),
    parent_transaction_id int(11),
    notes text,
    attachments text,
    tags text,
    status varchar(20) DEFAULT 'completed',
    user_id int(11),
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_entity_id (entity_id),
    KEY idx_transaction_type (transaction_type),
    KEY idx_transaction_date (transaction_date),
    KEY idx_category (category),
    KEY idx_status (status),
    KEY idx_amount (amount),
    FOREIGN KEY (entity_id) REFERENCES {$wpdb->prefix}bizmanage_business_entities(id) ON DELETE CASCADE
) $charset_collate;";

$result3 = dbDelta($sql);
echo '<p>Transactions Table: ' . (strpos($result3[$table_name], 'Created') !== false ? 'Created' : 'Updated') . '</p>';

// Categories table
$table_name = $wpdb->prefix . 'bizmanage_categories';
$sql = "CREATE TABLE $table_name (
    id int(11) NOT NULL AUTO_INCREMENT,
    entity_id int(11) NOT NULL,
    name varchar(255) NOT NULL,
    type enum('income','expense') NOT NULL,
    description text,
    parent_id int(11),
    color varchar(7) DEFAULT '#007cba',
    icon varchar(50),
    sort_order int(11) DEFAULT 0,
    is_default tinyint(1) DEFAULT 0,
    status varchar(20) DEFAULT 'active',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_entity_id (entity_id),
    KEY idx_type (type),
    KEY idx_parent_id (parent_id),
    KEY idx_status (status),
    UNIQUE KEY unique_category (entity_id, name, type),
    FOREIGN KEY (entity_id) REFERENCES {$wpdb->prefix}bizmanage_business_entities(id) ON DELETE CASCADE
) $charset_collate;";

$result4 = dbDelta($sql);
echo '<p>Categories Table: ' . (strpos($result4[$table_name], 'Created') !== false ? 'Created' : 'Updated') . '</p>';

// Recurring transactions table
$table_name = $wpdb->prefix . 'bizmanage_recurring_transactions';
$sql = "CREATE TABLE $table_name (
    id int(11) NOT NULL AUTO_INCREMENT,
    entity_id int(11) NOT NULL,
    transaction_type enum('income','expense') NOT NULL,
    amount decimal(15,4) NOT NULL,
    currency varchar(10) DEFAULT 'USD',
    description text NOT NULL,
    category varchar(100),
    payment_method varchar(100),
    frequency enum('daily','weekly','monthly','quarterly','yearly') NOT NULL,
    interval_value int(11) DEFAULT 1,
    start_date date NOT NULL,
    end_date date,
    next_date date NOT NULL,
    last_generated_date date,
    total_occurrences int(11),
    generated_count int(11) DEFAULT 0,
    status varchar(20) DEFAULT 'active',
    user_id int(11),
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_entity_id (entity_id),
    KEY idx_next_date (next_date),
    KEY idx_status (status),
    FOREIGN KEY (entity_id) REFERENCES {$wpdb->prefix}bizmanage_business_entities(id) ON DELETE CASCADE
) $charset_collate;";

$result5 = dbDelta($sql);
echo '<p>Recurring Transactions Table: ' . (strpos($result5[$table_name], 'Created') !== false ? 'Created' : 'Updated') . '</p>';

// Bank accounts table
$table_name = $wpdb->prefix . 'bizmanage_bank_accounts';
$sql = "CREATE TABLE $table_name (
    id int(11) NOT NULL AUTO_INCREMENT,
    entity_id int(11) NOT NULL,
    account_name varchar(255) NOT NULL,
    account_type varchar(100) NOT NULL,
    account_number varchar(100),
    bank_name varchar(255),
    branch_name varchar(255),
    routing_number varchar(50),
    swift_code varchar(20),
    currency varchar(10) DEFAULT 'USD',
    opening_balance decimal(15,4) DEFAULT 0.0000,
    current_balance decimal(15,4) DEFAULT 0.0000,
    is_default tinyint(1) DEFAULT 0,
    status varchar(20) DEFAULT 'active',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_entity_id (entity_id),
    KEY idx_status (status),
    FOREIGN KEY (entity_id) REFERENCES {$wpdb->prefix}bizmanage_business_entities(id) ON DELETE CASCADE
) $charset_collate;";

$result6 = dbDelta($sql);
echo '<p>Bank Accounts Table: ' . (strpos($result6[$table_name], 'Created') !== false ? 'Created' : 'Updated') . '</p>';

// Settings table
$table_name = $wpdb->prefix . 'bizmanage_settings';
$sql = "CREATE TABLE $table_name (
    id int(11) NOT NULL AUTO_INCREMENT,
    entity_id int(11),
    section varchar(100) NOT NULL,
    setting_key varchar(255) NOT NULL,
    setting_value longtext,
    setting_type varchar(50) DEFAULT 'text',
    is_encrypted tinyint(1) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_entity_id (entity_id),
    KEY idx_section (section),
    KEY idx_setting_key (setting_key),
    UNIQUE KEY unique_setting (entity_id, section, setting_key)
) $charset_collate;";

$result7 = dbDelta($sql);
echo '<p>Settings Table: ' . (strpos($result7[$table_name], 'Created') !== false ? 'Created' : 'Updated') . '</p>';

// Create default business entity
$existing_entity = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}bizmanage_business_entities");
if ($existing_entity == 0) {
    $wpdb->insert(
        $wpdb->prefix . 'bizmanage_business_entities',
        array(
            'business_name' => 'Default Business',
            'entity_type' => 'Sole Proprietorship',
            'currency' => 'USD',
            'status' => 'active',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        )
    );
    echo '<p>Default Business Entity Created with ID: ' . $wpdb->insert_id . '</p>';
}

// Create default categories
$entity_id = $wpdb->get_var("SELECT id FROM {$wpdb->prefix}bizmanage_business_entities LIMIT 1");
if ($entity_id) {
    $default_income_categories = array(
        'Sales Revenue', 'Service Revenue', 'Interest Income', 'Other Income'
    );
    
    $default_expense_categories = array(
        'Office Supplies', 'Marketing', 'Travel', 'Utilities', 'Rent', 'Insurance'
    );
    
    foreach ($default_income_categories as $category) {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}bizmanage_categories WHERE entity_id = %d AND name = %s AND type = 'income'",
            $entity_id, $category
        ));
        
        if ($existing == 0) {
            $wpdb->insert(
                $wpdb->prefix . 'bizmanage_categories',
                array(
                    'entity_id' => $entity_id,
                    'name' => $category,
                    'type' => 'income',
                    'color' => '#28a745',
                    'is_default' => 1,
                    'status' => 'active',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                )
            );
        }
    }
    
    foreach ($default_expense_categories as $category) {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}bizmanage_categories WHERE entity_id = %d AND name = %s AND type = 'expense'",
            $entity_id, $category
        ));
        
        if ($existing == 0) {
            $wpdb->insert(
                $wpdb->prefix . 'bizmanage_categories',
                array(
                    'entity_id' => $entity_id,
                    'name' => $category,
                    'type' => 'expense',
                    'color' => '#dc3545',
                    'is_default' => 1,
                    'status' => 'active',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                )
            );
        }
    }
    
    echo '<p>Default Categories Created for Entity ID: ' . $entity_id . '</p>';
}

echo '<h2>Database Setup Complete!</h2>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">Go to BizManage Pro Dashboard</a></p>';
echo '<p><a href="debug-database-check.php">Run Database Check</a></p>';
?>

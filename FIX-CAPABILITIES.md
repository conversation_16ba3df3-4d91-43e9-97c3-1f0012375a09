# Fix BizManage Pro Capabilities Issue

## Quick Fix

If you're getting "Sorry, you are not allowed to access this page" errors, follow these steps:

### Method 1: Automatic Fix (Recommended)

1. **Go to this URL in your browser** (replace `localhost` with your domain):
   ```
   http://localhost/wordpress/wp-admin/admin.php?bizmanage_fix_caps=1
   ```

2. **You should see a message** saying capabilities have been added

3. **Click the link** to go to the dashboard

4. **Try accessing the pages again** - they should work now!

### Method 2: Manual Plugin Reactivation

1. **Go to WordPress Admin** → Plugins
2. **Deactivate** BizManage Pro
3. **Activate** BizManage Pro again
4. **Try accessing the pages** - they should work now!

### Method 3: Database Fix (Advanced Users)

If the above methods don't work, you can manually add capabilities:

1. **Go to WordPress Admin** → Users → Your Profile
2. **Note your user ID** from the URL
3. **Run this in your database** (replace `1` with your user ID):

```sql
-- Add capabilities to administrator role
INSERT INTO wp_usermeta (user_id, meta_key, meta_value) VALUES 
(1, 'wp_bizmanage_manage_finances', '1'),
(1, 'wp_bizmanage_view_finances', '1'),
(1, 'wp_bizmanage_manage_entities', '1'),
(1, 'wp_bizmanage_view_entities', '1'),
(1, 'wp_bizmanage_manage_documents', '1'),
(1, 'wp_bizmanage_view_documents', '1'),
(1, 'wp_bizmanage_view_reports', '1'),
(1, 'wp_bizmanage_manage_settings', '1')
ON DUPLICATE KEY UPDATE meta_value = '1';
```

## What Was the Problem?

The issue was that WordPress administrators didn't automatically get the custom BizManage Pro capabilities. The plugin was checking for specific capabilities like `bizmanage_view_finances` and `bizmanage_manage_finances`, but these weren't assigned to the administrator role.

## What I Fixed

1. **Updated the roles system** to automatically grant all BizManage capabilities to administrators
2. **Added a capability filter** that ensures administrators always have access
3. **Created a debug method** to manually fix capabilities if needed
4. **Updated the installer** to properly add all capabilities during activation

## Test the Fix

After applying the fix, try these URLs:

- Dashboard: `http://localhost/wordpress/wp-admin/admin.php?page=bizmanage-dashboard`
- Add Income: `http://localhost/wordpress/wp-admin/admin.php?page=bizmanage-finances&action=add-income`
- Add Expense: `http://localhost/wordpress/wp-admin/admin.php?page=bizmanage-finances&action=add-expense`
- Documents: `http://localhost/wordpress/wp-admin/admin.php?page=bizmanage-documents`
- Reports: `http://localhost/wordpress/wp-admin/admin.php?page=bizmanage-reports`

All should work without permission errors!

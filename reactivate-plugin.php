<?php
/**
 * Reactivate BizManage Pro Plugin
 * 
 * Place this file in WordPress root directory and run it to reactivate the plugin
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>BizManage Pro Plugin Reactivation</h1>';

// Deactivate plugin first
$plugin_file = 'bizmanage-pro/bizmanage-pro.php';

if (is_plugin_active($plugin_file)) {
    deactivate_plugins($plugin_file);
    echo '<p>Plugin deactivated.</p>';
} else {
    echo '<p>Plugin was not active.</p>';
}

// Clear any cached data
wp_cache_flush();
delete_option('bizmanage_pro_activated');
delete_option('bizmanage_pro_db_version');

echo '<p>Cleared cached data.</p>';

// Activate plugin
$result = activate_plugin($plugin_file);

if (is_wp_error($result)) {
    echo '<p style="color: red;">Plugin activation failed: ' . $result->get_error_message() . '</p>';
} else {
    echo '<p style="color: green;">Plugin activated successfully!</p>';
}

// Check if plugin is now active
if (is_plugin_active($plugin_file)) {
    echo '<p style="color: green;">Plugin is now active.</p>';
} else {
    echo '<p style="color: red;">Plugin activation verification failed.</p>';
}

// Add user capabilities manually
$admin_role = get_role('administrator');
if ($admin_role) {
    $capabilities = array(
        'bizmanage_view_dashboard',
        'bizmanage_manage_entities',
        'bizmanage_view_entities',
        'bizmanage_edit_entities',
        'bizmanage_delete_entities',
        'bizmanage_manage_documents',
        'bizmanage_view_documents',
        'bizmanage_upload_documents',
        'bizmanage_delete_documents',
        'bizmanage_manage_finances',
        'bizmanage_view_finances',
        'bizmanage_add_transactions',
        'bizmanage_edit_transactions',
        'bizmanage_delete_transactions',
        'bizmanage_manage_categories',
        'bizmanage_view_reports',
        'bizmanage_generate_reports',
        'bizmanage_export_reports',
        'bizmanage_manage_settings',
        'bizmanage_view_settings',
        'bizmanage_manage_users',
        'bizmanage_backup_restore',
        'bizmanage_system_info'
    );
    
    foreach ($capabilities as $cap) {
        $admin_role->add_cap($cap);
    }
    
    echo '<p>Administrator capabilities added.</p>';
}

echo '<h2>Next Steps:</h2>';
echo '<ol>';
echo '<li><a href="setup-database.php">Run Database Setup</a></li>';
echo '<li><a href="debug-database-check.php">Check Database Tables</a></li>';
echo '<li><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">Go to BizManage Pro Dashboard</a></li>';
echo '</ol>';
?>

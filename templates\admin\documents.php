<?php
/**
 * BizManage Pro Documents Template
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get instances
$documents_manager = BizManage_Pro_Documents::instance();
$admin = BizManage_Pro_Admin::instance();
$security = BizManage_Pro_Security::instance();

// Handle actions
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
$document_id = isset($_GET['document_id']) ? intval($_GET['document_id']) : 0;

// Get current entity
$entities = $admin->get_user_entities_dropdown();
$selected_entity_id = isset($_GET['entity_id']) ? intval($_GET['entity_id']) : 0;

if (!$selected_entity_id && !empty($entities)) {
    $selected_entity_id = array_keys($entities)[0];
}

// Get document categories
$categories = $documents_manager->get_document_categories();
?>

<div class="wrap bizmanage-admin-wrap">
    <div class="bizmanage-header">
        <h1>
            <?php 
            switch ($action) {
                case 'upload':
                    _e('Upload Document', 'bizmanage-pro');
                    break;
                case 'edit':
                    _e('Edit Document', 'bizmanage-pro');
                    break;
                default:
                    _e('Document Management', 'bizmanage-pro');
            }
            ?>
        </h1>
        <p>
            <?php 
            switch ($action) {
                case 'upload':
                    _e('Upload and manage your business documents securely.', 'bizmanage-pro');
                    break;
                case 'edit':
                    _e('Update document information and metadata.', 'bizmanage-pro');
                    break;
                default:
                    _e('Manage your business documents with secure storage and organization.', 'bizmanage-pro');
            }
            ?>
        </p>
    </div>

    <?php if (empty($entities)): ?>
        <div class="notice notice-warning">
            <p>
                <?php _e('No business entities found. Please create a business entity first.', 'bizmanage-pro'); ?>
                <a href="<?php echo admin_url('admin.php?page=bizmanage-entities'); ?>" class="button button-primary">
                    <?php _e('Create Business Entity', 'bizmanage-pro'); ?>
                </a>
            </p>
        </div>
    <?php else: ?>

        <?php if ($action === 'list'): ?>
            
            <!-- List View -->
            <div class="bizmanage-entity-selector">
                <label for="bizmanage-entity-select"><?php _e('Select Business Entity:', 'bizmanage-pro'); ?></label>
                <select id="bizmanage-entity-select" name="entity_id" class="form-select">
                    <?php foreach ($entities as $id => $name): ?>
                        <option value="<?php echo esc_attr($id); ?>" <?php selected($selected_entity_id, $id); ?>>
                            <?php echo esc_html($name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Search and Filter -->
            <div class="bizmanage-documents-filters" style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
                <div style="display: grid; grid-template-columns: 1fr auto auto auto; gap: 15px; align-items: end;">
                    <div>
                        <label for="document-search"><?php _e('Search Documents:', 'bizmanage-pro'); ?></label>
                        <input type="text" id="document-search" placeholder="<?php _e('Search by title, description, or tags...', 'bizmanage-pro'); ?>" class="form-control">
                    </div>
                    
                    <div>
                        <label for="category-filter"><?php _e('Category:', 'bizmanage-pro'); ?></label>
                        <select id="category-filter" class="form-select">
                            <option value=""><?php _e('All Categories', 'bizmanage-pro'); ?></option>
                            <?php foreach ($categories as $key => $label): ?>
                                <option value="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <button type="button" id="filter-documents" class="bizmanage-btn bizmanage-btn-secondary">
                        <?php _e('Filter', 'bizmanage-pro'); ?>
                    </button>
                    
                    <?php if (current_user_can('bizmanage_upload_documents')): ?>
                    <a href="<?php echo admin_url('admin.php?page=bizmanage-documents&action=upload&entity_id=' . $selected_entity_id); ?>" 
                       class="bizmanage-btn bizmanage-btn-primary">
                        <?php _e('Upload Document', 'bizmanage-pro'); ?>
                    </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Documents List -->
            <div class="bizmanage-table-wrapper">
                <table class="bizmanage-table" id="documents-table">
                    <thead>
                        <tr>
                            <th><?php _e('Title', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Category', 'bizmanage-pro'); ?></th>
                            <th><?php _e('File Type', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Size', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Upload Date', 'bizmanage-pro'); ?></th>
                            <th><?php _e('Actions', 'bizmanage-pro'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="documents-list">
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px;">
                                <?php _e('Loading documents...', 'bizmanage-pro'); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

        <?php elseif ($action === 'upload'): ?>
            
            <!-- Upload Form -->
            <div class="bizmanage-form">
                <form id="document-upload-form" method="post" enctype="multipart/form-data">
                    <?php wp_nonce_field('bizmanage_upload_document', 'document_nonce'); ?>
                    <input type="hidden" name="entity_id" value="<?php echo esc_attr($selected_entity_id); ?>">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="document_title"><?php _e('Document Title', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                            <input type="text" id="document_title" name="title" required>
                        </div>

                        <div class="form-group">
                            <label for="document_category"><?php _e('Category', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                            <select id="document_category" name="category" required>
                                <option value=""><?php _e('Select Category', 'bizmanage-pro'); ?></option>
                                <?php foreach ($categories as $key => $label): ?>
                                    <option value="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="document_description"><?php _e('Description', 'bizmanage-pro'); ?></label>
                        <textarea id="document_description" name="description" rows="3" 
                                  placeholder="<?php _e('Brief description of the document...', 'bizmanage-pro'); ?>"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="document_tags"><?php _e('Tags', 'bizmanage-pro'); ?></label>
                        <input type="text" id="document_tags" name="tags" 
                               placeholder="<?php _e('Comma-separated tags for easy searching...', 'bizmanage-pro'); ?>">
                    </div>

                    <div class="form-group">
                        <label for="document_file"><?php _e('Select File', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                        <input type="file" id="document_file" name="document" required 
                               accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png">
                        <small class="form-text">
                            <?php 
                            $max_size = get_option('bizmanage_pro_max_file_size', 10485760);
                            $allowed_types = get_option('bizmanage_pro_allowed_file_types', 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png');
                            printf(
                                __('Maximum file size: %s. Allowed types: %s', 'bizmanage-pro'),
                                size_format($max_size),
                                $allowed_types
                            );
                            ?>
                        </small>
                    </div>

                    <div class="form-actions" style="margin-top: 30px;">
                        <button type="button" class="bizmanage-btn bizmanage-btn-primary bizmanage-upload-document">
                            <?php _e('Upload Document', 'bizmanage-pro'); ?>
                        </button>
                        
                        <a href="<?php echo admin_url('admin.php?page=bizmanage-documents&entity_id=' . $selected_entity_id); ?>" 
                           class="bizmanage-btn bizmanage-btn-secondary">
                            <?php _e('Cancel', 'bizmanage-pro'); ?>
                        </a>
                    </div>
                </form>
            </div>

        <?php elseif ($action === 'edit' && $document_id > 0): ?>
            
            <?php
            $document = $documents_manager->get_document($document_id);
            if (!$document): ?>
                <div class="notice notice-error">
                    <p><?php _e('Document not found.', 'bizmanage-pro'); ?></p>
                </div>
            <?php else: ?>
                
                <!-- Edit Form -->
                <div class="bizmanage-form">
                    <form id="document-edit-form" method="post">
                        <?php wp_nonce_field('bizmanage_update_document', 'document_nonce'); ?>
                        <input type="hidden" name="document_id" value="<?php echo esc_attr($document_id); ?>">

                        <div class="form-row">
                            <div class="form-group">
                                <label for="document_title"><?php _e('Document Title', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                                <input type="text" id="document_title" name="title" 
                                       value="<?php echo esc_attr($document->title); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="document_category"><?php _e('Category', 'bizmanage-pro'); ?> <span class="required">*</span></label>
                                <select id="document_category" name="category" required>
                                    <?php foreach ($categories as $key => $label): ?>
                                        <option value="<?php echo esc_attr($key); ?>" 
                                                <?php selected($document->category, $key); ?>>
                                            <?php echo esc_html($label); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="document_description"><?php _e('Description', 'bizmanage-pro'); ?></label>
                            <textarea id="document_description" name="description" rows="3"><?php echo esc_textarea($document->description); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="document_tags"><?php _e('Tags', 'bizmanage-pro'); ?></label>
                            <input type="text" id="document_tags" name="tags" 
                                   value="<?php echo esc_attr($document->tags); ?>">
                        </div>

                        <!-- Document Info -->
                        <div class="document-info" style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0;">
                            <h4><?php _e('Document Information', 'bizmanage-pro'); ?></h4>
                            <p><strong><?php _e('File Name:', 'bizmanage-pro'); ?></strong> <?php echo esc_html($document->file_name); ?></p>
                            <p><strong><?php _e('File Size:', 'bizmanage-pro'); ?></strong> <?php echo size_format($document->file_size); ?></p>
                            <p><strong><?php _e('Upload Date:', 'bizmanage-pro'); ?></strong> <?php echo date('Y-m-d H:i:s', strtotime($document->upload_date)); ?></p>
                            <p><strong><?php _e('Encrypted:', 'bizmanage-pro'); ?></strong> <?php echo $document->encrypted ? __('Yes', 'bizmanage-pro') : __('No', 'bizmanage-pro'); ?></p>
                        </div>

                        <div class="form-actions" style="margin-top: 30px;">
                            <button type="button" class="bizmanage-btn bizmanage-btn-primary bizmanage-update-document">
                                <?php _e('Update Document', 'bizmanage-pro'); ?>
                            </button>
                            
                            <a href="<?php echo admin_url('admin.php?page=bizmanage-documents&entity_id=' . $document->entity_id); ?>" 
                               class="bizmanage-btn bizmanage-btn-secondary">
                                <?php _e('Cancel', 'bizmanage-pro'); ?>
                            </a>

                            <?php if (current_user_can('bizmanage_manage_documents')): ?>
                            <button type="button" 
                                    class="bizmanage-btn bizmanage-btn-danger bizmanage-delete-document" 
                                    data-document-id="<?php echo esc_attr($document_id); ?>"
                                    style="margin-left: 10px;">
                                <?php _e('Delete Document', 'bizmanage-pro'); ?>
                            </button>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>

            <?php endif; ?>

        <?php endif; ?>

    <?php endif; ?>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Load documents on page load
    if ($('#documents-list').length) {
        loadDocuments();
    }
    
    // Entity change handler
    $('#bizmanage-entity-select').on('change', function() {
        var entityId = $(this).val();
        var currentUrl = new URL(window.location);
        currentUrl.searchParams.set('entity_id', entityId);
        window.location.href = currentUrl.toString();
    });
    
    // Filter documents
    $('#filter-documents').on('click', function() {
        loadDocuments();
    });
    
    // Search on enter
    $('#document-search').on('keypress', function(e) {
        if (e.which === 13) {
            loadDocuments();
        }
    });
    
    // Update document handler
    $(document).on('click', '.bizmanage-update-document', function(e) {
        e.preventDefault();
        
        var form = $('#document-edit-form');
        var formData = form.serialize();
        
        BizManagePro.ajaxRequest('bizmanage_update_document', formData, function(response) {
            if (response.success) {
                BizManagePro.showAlert(response.message, 'success');
            } else {
                BizManagePro.showAlert(response.message, 'error');
            }
        });
    });
    
    function loadDocuments() {
        var entityId = $('#bizmanage-entity-select').val();
        var search = $('#document-search').val();
        var category = $('#category-filter').val();
        
        BizManagePro.ajaxRequest('bizmanage_get_documents', {
            entity_id: entityId,
            search: search,
            category: category
        }, function(response) {
            if (response.success && response.data) {
                updateDocumentsList(response.data);
            }
        });
    }
    
    function updateDocumentsList(documents) {
        var tbody = $('#documents-list');
        tbody.empty();
        
        if (documents.length === 0) {
            tbody.append('<tr><td colspan="6" style="text-align: center; padding: 20px;"><?php _e("No documents found.", "bizmanage-pro"); ?></td></tr>');
            return;
        }
        
        $.each(documents, function(index, doc) {
            var row = '<tr>' +
                '<td><strong>' + doc.title + '</strong><br><small>' + doc.description + '</small></td>' +
                '<td>' + doc.category + '</td>' +
                '<td>' + doc.file_type.toUpperCase() + '</td>' +
                '<td>' + formatFileSize(doc.file_size) + '</td>' +
                '<td>' + formatDate(doc.upload_date) + '</td>' +
                '<td class="actions">' +
                '<a href="<?php echo admin_url("admin.php?page=bizmanage-documents&action=edit&document_id="); ?>' + doc.id + '" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-secondary"><?php _e("Edit", "bizmanage-pro"); ?></a> ' +
                '<button type="button" class="bizmanage-btn bizmanage-btn-sm bizmanage-btn-danger bizmanage-delete-document" data-document-id="' + doc.id + '"><?php _e("Delete", "bizmanage-pro"); ?></button>' +
                '</td>' +
                '</tr>';
            tbody.append(row);
        });
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    function formatDate(dateString) {
        var date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
});
</script>

<style>
.form-text {
    color: #6c757d;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.document-info h4 {
    margin: 0 0 10px 0;
    color: #495057;
}

.document-info p {
    margin: 5px 0;
    font-size: 14px;
}

.bizmanage-documents-filters {
    border: 1px solid #dee2e6;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}
</style>

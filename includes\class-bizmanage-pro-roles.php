<?php
/**
 * BizManage Pro Roles Class
 *
 * Handles user roles and capabilities management
 *
 * @package BizManagePro
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BizManage Pro Roles Class
 */
class BizManage_Pro_Roles {

    /**
     * Instance of this class
     * @var BizManage_Pro_Roles
     */
    private static $instance = null;

    /**
     * Available capabilities
     * @var array
     */
    private $capabilities;

    /**
     * Get instance
     * @return BizManage_Pro_Roles
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_capabilities();
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'check_user_access'));
        add_filter('user_has_cap', array($this, 'filter_user_capabilities'), 10, 3);
    }

    /**
     * Initialize capabilities
     */
    private function init_capabilities() {
        $this->capabilities = array(
            // Entity management
            'bizmanage_manage_entities' => __('Manage Business Entities', 'bizmanage-pro'),
            'bizmanage_view_entities' => __('View Business Entities', 'bizmanage-pro'),
            
            // Document management
            'bizmanage_manage_documents' => __('Manage Documents', 'bizmanage-pro'),
            'bizmanage_view_documents' => __('View Documents', 'bizmanage-pro'),
            'bizmanage_upload_documents' => __('Upload Documents', 'bizmanage-pro'),
            'bizmanage_delete_documents' => __('Delete Documents', 'bizmanage-pro'),
            
            // Financial management
            'bizmanage_manage_finances' => __('Manage Finances', 'bizmanage-pro'),
            'bizmanage_view_finances' => __('View Financial Data', 'bizmanage-pro'),
            'bizmanage_limited_finances' => __('Limited Financial Access', 'bizmanage-pro'),
            'bizmanage_submit_expenses' => __('Submit Expenses', 'bizmanage-pro'),
            
            // Reports
            'bizmanage_view_reports' => __('View Reports', 'bizmanage-pro'),
            'bizmanage_view_client_reports' => __('View Client Reports', 'bizmanage-pro'),
            'bizmanage_export_reports' => __('Export Reports', 'bizmanage-pro'),
            
            // User management
            'bizmanage_manage_users' => __('Manage Users', 'bizmanage-pro'),
            'bizmanage_view_users' => __('View Users', 'bizmanage-pro'),
            
            // Settings
            'bizmanage_manage_settings' => __('Manage Settings', 'bizmanage-pro'),
            'bizmanage_view_settings' => __('View Settings', 'bizmanage-pro'),
            
            // Data management
            'bizmanage_export_data' => __('Export Data', 'bizmanage-pro'),
            'bizmanage_import_data' => __('Import Data', 'bizmanage-pro'),
            'bizmanage_backup_data' => __('Backup Data', 'bizmanage-pro'),
            
            // Personal data
            'bizmanage_view_own_data' => __('View Own Data', 'bizmanage-pro'),
            'bizmanage_edit_own_data' => __('Edit Own Data', 'bizmanage-pro'),
        );
    }

    /**
     * Get all capabilities
     * @return array
     */
    public function get_capabilities() {
        return $this->capabilities;
    }

    /**
     * Get role capabilities
     * @param string $role
     * @return array
     */
    public function get_role_capabilities($role) {
        $role_capabilities = array();

        switch ($role) {
            case 'bizmanage_admin':
                $role_capabilities = array(
                    'read' => true,
                    'bizmanage_manage_entities' => true,
                    'bizmanage_view_entities' => true,
                    'bizmanage_manage_documents' => true,
                    'bizmanage_view_documents' => true,
                    'bizmanage_upload_documents' => true,
                    'bizmanage_delete_documents' => true,
                    'bizmanage_manage_finances' => true,
                    'bizmanage_view_finances' => true,
                    'bizmanage_view_reports' => true,
                    'bizmanage_export_reports' => true,
                    'bizmanage_manage_users' => true,
                    'bizmanage_view_users' => true,
                    'bizmanage_manage_settings' => true,
                    'bizmanage_view_settings' => true,
                    'bizmanage_export_data' => true,
                    'bizmanage_import_data' => true,
                    'bizmanage_backup_data' => true,
                );
                break;

            case 'bizmanage_accountant':
                $role_capabilities = array(
                    'read' => true,
                    'bizmanage_view_entities' => true,
                    'bizmanage_manage_documents' => true,
                    'bizmanage_view_documents' => true,
                    'bizmanage_upload_documents' => true,
                    'bizmanage_manage_finances' => true,
                    'bizmanage_view_finances' => true,
                    'bizmanage_view_reports' => true,
                    'bizmanage_export_reports' => true,
                    'bizmanage_export_data' => true,
                    'bizmanage_view_settings' => true,
                );
                break;

            case 'bizmanage_manager':
                $role_capabilities = array(
                    'read' => true,
                    'bizmanage_view_entities' => true,
                    'bizmanage_view_documents' => true,
                    'bizmanage_limited_finances' => true,
                    'bizmanage_view_finances' => true,
                    'bizmanage_view_reports' => true,
                    'bizmanage_export_reports' => true,
                );
                break;

            case 'bizmanage_employee':
                $role_capabilities = array(
                    'read' => true,
                    'bizmanage_submit_expenses' => true,
                    'bizmanage_view_own_data' => true,
                    'bizmanage_edit_own_data' => true,
                    'bizmanage_upload_documents' => true,
                );
                break;

            case 'bizmanage_client':
                $role_capabilities = array(
                    'read' => true,
                    'bizmanage_view_client_reports' => true,
                    'bizmanage_view_own_data' => true,
                );
                break;
        }

        // Return just the capability names (keys), not the boolean values
        return array_keys($role_capabilities);
    }

    /**
     * Check if user has capability
     * @param string $capability
     * @param int $user_id
     * @param int $entity_id
     * @return bool
     */
    public function user_can($capability, $user_id = null, $entity_id = null) {
        if (is_null($user_id)) {
            $user_id = get_current_user_id();
        }

        // Check if user has the capability
        if (!user_can($user_id, $capability)) {
            return false;
        }

        // Additional checks for entity-specific access
        if ($entity_id && !$this->user_can_access_entity($user_id, $entity_id)) {
            return false;
        }

        return true;
    }

    /**
     * Check if user can access entity
     * @param int $user_id
     * @param int $entity_id
     * @return bool
     */
    public function user_can_access_entity($user_id, $entity_id) {
        // Administrators can access all entities
        if (user_can($user_id, 'bizmanage_manage_entities')) {
            return true;
        }

        // Check if user is associated with the entity
        $db = BizManage_Pro_Database::instance();
        $entity = $db->get_row('business_entities', array('id' => $entity_id));

        if (!$entity) {
            return false;
        }

        // Entity owner can access
        if ($entity->user_id == $user_id) {
            return true;
        }

        // Check if user has been granted access to this entity
        $access = $db->get_row('settings', array(
            'entity_id' => $entity_id,
            'setting_key' => 'user_access_' . $user_id
        ));

        return $access && $access->setting_value === '1';
    }

    /**
     * Grant user access to entity
     * @param int $user_id
     * @param int $entity_id
     * @return bool
     */
    public function grant_entity_access($user_id, $entity_id) {
        if (!current_user_can('bizmanage_manage_users')) {
            return false;
        }

        $db = BizManage_Pro_Database::instance();
        
        return $db->insert('settings', array(
            'entity_id' => $entity_id,
            'setting_key' => 'user_access_' . $user_id,
            'setting_value' => '1'
        ));
    }

    /**
     * Revoke user access to entity
     * @param int $user_id
     * @param int $entity_id
     * @return bool
     */
    public function revoke_entity_access($user_id, $entity_id) {
        if (!current_user_can('bizmanage_manage_users')) {
            return false;
        }

        $db = BizManage_Pro_Database::instance();
        
        return $db->delete('settings', array(
            'entity_id' => $entity_id,
            'setting_key' => 'user_access_' . $user_id
        ));
    }

    /**
     * Get user's accessible entities
     * @param int $user_id
     * @return array
     */
    public function get_user_entities($user_id = null) {
        if (is_null($user_id)) {
            $user_id = get_current_user_id();
        }

        $db = BizManage_Pro_Database::instance();

        // Administrators can access all entities
        if (user_can($user_id, 'bizmanage_manage_entities')) {
            return $db->get_results('business_entities', array(
                'where' => array('status' => 'active'),
                'order_by' => 'business_name',
                'order' => 'ASC'
            ));
        }

        // Get entities owned by user
        $owned_entities = $db->get_results('business_entities', array(
            'where' => array(
                'user_id' => $user_id,
                'status' => 'active'
            ),
            'order_by' => 'business_name',
            'order' => 'ASC'
        ));

        // Get entities user has access to
        $access_settings = $db->get_results('settings', array(
            'where' => array(
                'setting_key' => 'user_access_' . $user_id,
                'setting_value' => '1'
            )
        ));

        $accessible_entity_ids = array();
        foreach ($access_settings as $setting) {
            if ($setting->entity_id) {
                $accessible_entity_ids[] = $setting->entity_id;
            }
        }

        $accessible_entities = array();
        if (!empty($accessible_entity_ids)) {
            $accessible_entities = $db->get_results('business_entities', array(
                'where' => array(
                    'id' => $accessible_entity_ids,
                    'status' => 'active'
                ),
                'order_by' => 'business_name',
                'order' => 'ASC'
            ));
        }

        // Merge and remove duplicates
        $all_entities = array_merge($owned_entities, $accessible_entities);
        $unique_entities = array();
        $seen_ids = array();

        foreach ($all_entities as $entity) {
            if (!in_array($entity->id, $seen_ids)) {
                $unique_entities[] = $entity;
                $seen_ids[] = $entity->id;
            }
        }

        return $unique_entities;
    }

    /**
     * Check user access on init
     */
    public function check_user_access() {
        // Add any initialization checks here
    }

    /**
     * Filter user capabilities
     * @param array $allcaps
     * @param array $caps
     * @param array $args
     * @return array
     */
    public function filter_user_capabilities($allcaps, $caps, $args) {
        // Grant all BizManage capabilities to administrators
        if (isset($allcaps['manage_options']) && $allcaps['manage_options']) {
            foreach ($this->capabilities as $cap => $label) {
                $allcaps[$cap] = true;
            }
        }

        // Grant capabilities to users with BizManage roles
        $user_id = isset($args[1]) ? $args[1] : get_current_user_id();
        $user = get_userdata($user_id);

        if ($user) {
            foreach ($user->roles as $role) {
                $role_caps = $this->get_role_capabilities($role);
                foreach ($role_caps as $cap) {
                    $allcaps[$cap] = true;
                }
            }
        }

        return $allcaps;
    }

    /**
     * Get users by role
     * @param string $role
     * @return array
     */
    public function get_users_by_role($role) {
        return get_users(array('role' => $role));
    }

    /**
     * Get all BizManage roles
     * @return array
     */
    public function get_bizmanage_roles() {
        return array(
            'bizmanage_admin' => __('BizManage Administrator', 'bizmanage-pro'),
            'bizmanage_accountant' => __('BizManage Accountant', 'bizmanage-pro'),
            'bizmanage_manager' => __('BizManage Manager', 'bizmanage-pro'),
            'bizmanage_employee' => __('BizManage Employee', 'bizmanage-pro'),
            'bizmanage_client' => __('BizManage Client', 'bizmanage-pro'),
        );
    }
}

<?php
/**
 * Test Category Fixes
 * 
 * This script will test all the category fixes we've applied
 */

// Find WordPress root directory
$wp_root = dirname(__FILE__);
$max_depth = 5;
$depth = 0;

while ($depth < $max_depth) {
    if (file_exists($wp_root . '/wp-config.php') && file_exists($wp_root . '/wp-load.php')) {
        break;
    }
    $wp_root = dirname($wp_root);
    $depth++;
}

if (!file_exists($wp_root . '/wp-load.php')) {
    die('WordPress installation not found.');
}

// WordPress environment
define('WP_USE_THEMES', false);
require_once($wp_root . '/wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>Test Category Fixes</h1>';

// Test 1: Check if get_category method exists
echo '<h2>1. Method Existence Check</h2>';

if (class_exists('BizManage_Pro_Categories')) {
    $categories = BizManage_Pro_Categories::instance();
    
    if (method_exists($categories, 'get_category')) {
        echo '<p style="color: green;">✅ get_category method exists</p>';
    } else {
        echo '<p style="color: red;">❌ get_category method missing</p>';
    }
    
    if (method_exists($categories, 'create_category')) {
        echo '<p style="color: green;">✅ create_category method exists</p>';
    } else {
        echo '<p style="color: red;">❌ create_category method missing</p>';
    }
    
    if (method_exists($categories, 'ajax_save_category')) {
        echo '<p style="color: green;">✅ ajax_save_category method exists</p>';
    } else {
        echo '<p style="color: red;">❌ ajax_save_category method missing</p>';
    }
} else {
    echo '<p style="color: red;">❌ BizManage_Pro_Categories class not found</p>';
}

// Test 2: Database get_row method
echo '<h2>2. Database get_row Method Test</h2>';

if (class_exists('BizManage_Pro_Database')) {
    $db = BizManage_Pro_Database::instance();
    
    // Test get_row with entity
    global $wpdb;
    $entities_table = $wpdb->prefix . 'bizmanage_business_entities';
    $entity = $wpdb->get_row("SELECT * FROM $entities_table WHERE status = 'active' LIMIT 1");
    
    if ($entity) {
        echo '<p style="color: green;">✅ Found test entity: ' . $entity->business_name . ' (ID: ' . $entity->id . ')</p>';
        
        // Test database get_row method
        try {
            $test_entity = $db->get_row('business_entities', array('where' => array('id' => $entity->id)));
            if ($test_entity) {
                echo '<p style="color: green;">✅ Database get_row method working</p>';
            } else {
                echo '<p style="color: orange;">⚠️ Database get_row returned null</p>';
            }
        } catch (Exception $e) {
            echo '<p style="color: red;">❌ Database get_row error: ' . $e->getMessage() . '</p>';
        }
    } else {
        echo '<p style="color: red;">❌ No test entity found</p>';
    }
} else {
    echo '<p style="color: red;">❌ BizManage_Pro_Database class not found</p>';
}

// Test 3: Complete category creation flow
echo '<h2>3. Complete Category Creation Test</h2>';

if (isset($_POST['test_complete_flow'])) {
    $entity_id = intval($_POST['test_entity_id']);
    $category_name = sanitize_text_field($_POST['test_category_name']);
    $category_type = sanitize_text_field($_POST['test_category_type']);
    
    if ($entity_id && $category_name && $category_type) {
        echo '<h3>Testing Complete Flow...</h3>';
        
        // Step 1: Create category via AJAX simulation
        $_POST = array(
            'action' => 'bizmanage_save_category',
            'nonce' => wp_create_nonce('bizmanage_save_category'),
            'entity_id' => $entity_id,
            'name' => $category_name,
            'type' => $category_type,
            'description' => 'Complete flow test category',
            'color' => '#007cba'
        );
        
        echo '<p><strong>Step 1:</strong> Simulating AJAX request...</p>';
        echo '<pre>Data: ' . json_encode($_POST, JSON_PRETTY_PRINT) . '</pre>';
        
        if (class_exists('BizManage_Pro_Categories')) {
            $categories = BizManage_Pro_Categories::instance();
            
            // Capture output
            ob_start();
            try {
                $categories->ajax_save_category();
                $output = ob_get_clean();
                
                echo '<p style="color: green;">✅ AJAX method executed without fatal errors</p>';
                echo '<pre>Output: ' . htmlspecialchars($output) . '</pre>';
                
                // Step 2: Verify category was created
                echo '<p><strong>Step 2:</strong> Verifying category creation...</p>';
                
                global $wpdb;
                $categories_table = $wpdb->prefix . 'bizmanage_categories';
                $created_category = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $categories_table WHERE entity_id = %d AND name = %s AND type = %s ORDER BY id DESC LIMIT 1",
                    $entity_id, $category_name, $category_type
                ));
                
                if ($created_category) {
                    echo '<p style="color: green;">✅ Category found in database!</p>';
                    echo '<pre>' . print_r($created_category, true) . '</pre>';
                    
                    // Step 3: Test get_category method
                    echo '<p><strong>Step 3:</strong> Testing get_category method...</p>';
                    
                    try {
                        $retrieved_category = $categories->get_category($created_category->id);
                        if ($retrieved_category) {
                            echo '<p style="color: green;">✅ get_category method working!</p>';
                            echo '<pre>' . print_r($retrieved_category, true) . '</pre>';
                        } else {
                            echo '<p style="color: red;">❌ get_category returned null</p>';
                        }
                    } catch (Exception $e) {
                        echo '<p style="color: red;">❌ get_category error: ' . $e->getMessage() . '</p>';
                    }
                } else {
                    echo '<p style="color: red;">❌ Category not found in database</p>';
                }
                
            } catch (Exception $e) {
                ob_end_clean();
                echo '<p style="color: red;">❌ AJAX method error: ' . $e->getMessage() . '</p>';
            }
        }
    } else {
        echo '<p style="color: red;">❌ Missing required fields</p>';
    }
}

// Test form
global $wpdb;
$entities_table = $wpdb->prefix . 'bizmanage_business_entities';
$entities = $wpdb->get_results("SELECT * FROM $entities_table WHERE status = 'active'");

if (!empty($entities)) {
    echo '<form method="post">';
    echo '<table>';
    echo '<tr><td>Entity:</td><td><select name="test_entity_id" required>';
    foreach ($entities as $entity) {
        echo '<option value="' . $entity->id . '">' . $entity->business_name . ' (ID: ' . $entity->id . ')</option>';
    }
    echo '</select></td></tr>';
    echo '<tr><td>Category Name:</td><td><input type="text" name="test_category_name" value="Complete Test ' . time() . '" required></td></tr>';
    echo '<tr><td>Type:</td><td><select name="test_category_type" required>';
    echo '<option value="income">Income</option>';
    echo '<option value="expense">Expense</option>';
    echo '</select></td></tr>';
    echo '<tr><td colspan="2"><button type="submit" name="test_complete_flow" value="1">Test Complete Category Flow</button></td></tr>';
    echo '</table>';
    echo '</form>';
}

// Test 4: Recent debug log
echo '<h2>4. Recent Debug Log</h2>';
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -15);
    
    $bizmanage_lines = array();
    foreach ($recent_lines as $line) {
        if (strpos($line, 'BizManagePro') !== false || strpos($line, 'bizmanage') !== false) {
            $bizmanage_lines[] = $line;
        }
    }
    
    if (!empty($bizmanage_lines)) {
        echo '<div style="background: #f0f0f0; padding: 10px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">';
        foreach ($bizmanage_lines as $line) {
            $color = 'black';
            if (strpos($line, 'Fatal error') !== false) {
                $color = 'red';
            } elseif (strpos($line, 'successfully') !== false) {
                $color = 'green';
            } elseif (strpos($line, 'Warning') !== false || strpos($line, 'Notice') !== false) {
                $color = 'orange';
            }
            echo '<div style="color: ' . $color . ';">' . htmlspecialchars($line) . '</div>';
        }
        echo '</div>';
    } else {
        echo '<p>No recent BizManage Pro log entries.</p>';
    }
} else {
    echo '<p>Debug log file not found.</p>';
}

// Test 5: Current categories
echo '<h2>5. Current Categories</h2>';
$categories_table = $wpdb->prefix . 'bizmanage_categories';
$categories = $wpdb->get_results("SELECT * FROM $categories_table ORDER BY id DESC LIMIT 10");

if (!empty($categories)) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Entity ID</th><th>Name</th><th>Type</th><th>Status</th><th>Created</th></tr>';
    foreach ($categories as $cat) {
        echo '<tr>';
        echo '<td>' . $cat->id . '</td>';
        echo '<td>' . $cat->entity_id . '</td>';
        echo '<td>' . $cat->name . '</td>';
        echo '<td>' . $cat->type . '</td>';
        echo '<td>' . $cat->status . '</td>';
        echo '<td>' . $cat->created_at . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p>No categories found.</p>';
}

// Instructions
echo '<h2>6. Next Steps</h2>';
echo '<ol>';
echo '<li><strong>Run the complete flow test above</strong> to verify all fixes</li>';
echo '<li><strong>Go to BizManage Pro Dashboard</strong> and try creating a category</li>';
echo '<li><strong>Check for any remaining errors</strong> in the debug log</li>';
echo '<li><strong>Test category functionality</strong> in the actual interface</li>';
echo '</ol>';

echo '<h2>7. Quick Links</h2>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-finances') . '">BizManage Pro Finances</a></p>';
echo '<p><a href="' . admin_url('admin.php?page=bizmanage-pro') . '">BizManage Pro Dashboard</a></p>';
?>
